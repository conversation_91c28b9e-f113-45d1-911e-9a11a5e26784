# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with this WordPress SEO Plugin (SLMM SEO Bundle) codebase.

# 🎯 CORE PHILOSOPHY

## PRIMARY DIRECTIVE: EFFICIENT, PATTERN-DRIVEN DEVELOPMENT
- **🎯 98% SOLUTION CERTAINTY REQUIRED** - NEVER propose solutions without absolute research confidence
- **🧠 ALWAYS use serena MCP tools for codebase analysis** - This overrides reading entire files
- **📚 REUSE existing architectural patterns** - Study memory-bank/ documentation first
- **🔧 PRESERVE dual-system architecture** - NEVER modify working keyboard shortcuts
- **📝 CLASSIC EDITOR ONLY** - All features must work perfectly in WordPress Classic Editor
- **⚡ MINIMAL viable changes** - Build on existing code rather than rewriting
- **🔍 SYMBOLIC OVER FULL FILE READS** - Use get_symbols_overview and find_symbol first
- **FALLBACK MECHANISMS** - NEVER! Make fallback mechanisms. Just make it work correctly the first time. It is a waste of code.
- **NEVER BREAK EXISTING FUNCTIONALITY** - If it works, don't touch it!
- **NO PLACEHOLDERS** - When creating a fucntion, don't just add the HTML it MUST have the correct code connetced and tested as well. Don't just make placeholders with no functionality.

## 🎯 SOLUTION CERTAINTY PROTOCOL (ABSOLUTE REQUIREMENT)
**BEFORE proposing ANY solution, you MUST achieve 98% real-world confidence:**

### Research Validation Requirements (MANDATORY)
1. **Comprehensive serena analysis** - Use find_symbol, get_symbols_overview, search_for_pattern extensively
2. **Existing pattern verification** - Confirm solution follows established codebase patterns  
3. **Integration point analysis** - Verify compatibility with dual-system architecture
4. **Dependency validation** - Check all required functions/classes exist and work as expected
5. **Real-world testing scenarios** - Mental simulation of solution in production environment
6. **Edge case consideration** - Account for WordPress variations, plugin conflicts, version differences

### Confidence Level Assessment (ENFORCE STRICTLY)
**Rate your confidence before proposing solutions:**
- **98-100%**: Solution thoroughly researched, patterns verified, integration confirmed → PROCEED
- **90-97%**: Additional research required, gaps in understanding → CONTINUE RESEARCH  
- **<90%**: Insufficient knowledge, major unknowns present → REQUEST CLARIFICATION

### Research Documentation Protocol
**Every solution proposal MUST include:**
```markdown
## Solution Confidence: [98-100%]
## Research Conducted:
- [Specific serena tools used and findings]
- [Existing patterns analyzed]
- [Integration points verified]
- [Dependencies confirmed]
## Risks Identified: [None/Low/Medium - if any Medium risks, continue research]
## Testing Approach: [How solution will be validated]
```

## CODE ANALYSIS PRINCIPLES
1. **Start with memory-bank/ documentation** to understand established patterns
2. **Use get_symbols_overview** before reading any source code files
3. **Find existing implementations** with find_symbol and search_for_pattern
4. **Analyze symbol relationships** with find_referencing_symbols
5. **Read full files only as last resort** when symbolic tools insufficient

# 🗂️ CODEBASE ORGANIZATION & FILE MANAGEMENT (TOP PRIORITY)

## 800-LINE MAXIMUM RULE (ABSOLUTE REQUIREMENT)
**EVERY PHP file MUST be under 800 lines - NO EXCEPTIONS**
** THE ONLY file that is except is the interlinking-suite.php file. This file is allowed to exceed 800 lines because it is a core controller file that cannot be split.NEVER SPLIT THIS FILE under ANY CIRCUMSTANCES.**

### Pre-Implementation Size Estimation
**MANDATORY**: Before writing ANY code, you MUST:
1. **Estimate final file size** using serena symbolic analysis of similar files
2. **Plan file architecture** if implementation will exceed 700 lines (100-line buffer)
3. **Create file split strategy** with clear separation of concerns
4. **Document size projections** in serena memory for tracking

### File Size Monitoring During Development
```php
// Check current file line count before adding code
wc -l target-file.php

// If approaching 700 lines, IMMEDIATELY split the file
if (lines > 700) {
    // Create new file with specific functionality
    // Update plugin.php integration
    // Document the architectural decision
}
```

### File Splitting Strategies
**When file approaches 700 lines:**
1. **Extract classes** into separate files in appropriate subdirectories
2. **Separate concerns** - utilities, settings, UI components
3. **Create factory patterns** for complex object creation
4. **Use WordPress autoloading** or explicit require_once patterns

### New File Creation Workflow (MANDATORY PROCESS)
**Every new file MUST follow this exact process:**

1. **Plan file structure** using serena get_symbols_overview of similar files
2. **Estimate implementation size** based on existing patterns
3. **Create file in proper directory** following existing organization
4. **Add to plugin.php** with proper initialization order
5. **Document in serena memory** with architectural decisions
6. **Test integration** with dual-system architecture

### Plugin.php Integration Requirements
**ALL new files MUST be properly integrated:**
```php
// In plugin.php - Add new file includes in logical order
require_once __DIR__ . '/includes/new-feature/class-new-feature.php';

// In slmm-seo-plugin.php - Initialize new classes properly
function slmm_seo_plugin_init() {
    // Existing initialization code...
    
    // Add new feature initialization
    if (class_exists('SLMM_New_Feature')) {
        new SLMM_New_Feature();
    }
}
```

## MANDATORY SERENA MEMORY CREATION (CRITICAL)
**EVERY complex task (3+ implementation steps) MUST create serena memory**

### Context Window Protection Protocol
**BEFORE starting any complex feature implementation:**
1. **Create comprehensive PRD** using serena write_memory
2. **Document file size estimates** and architectural decisions  
3. **Map integration points** with existing codebase
4. **Create implementation roadmap** with step-by-step breakdown
5. **Update memory** after each major implementation milestone

### PRD Documentation Template (MANDATORY)
```markdown
# Feature: [Feature Name] - Implementation PRD
## Date: [YYYY-MM-DD]
## Context: [Why this feature is needed]
## Architecture: [High-level design decisions]
## File Structure: [New files and estimated sizes]
## Integration Points: [How it connects to existing code]
## Implementation Steps: [Detailed breakdown]
## Size Estimates: [File-by-file line count projections]
## Testing Plan: [Validation requirements]
## Plugin.php Changes: [Required modifications]
```

### Serena Memory Naming Convention
**Use structured naming for easy retrieval:**
- `feature-[name]-prd` - Product requirements document
- `feature-[name]-architecture` - Architectural decisions
- `feature-[name]-implementation` - Step-by-step implementation notes
- `feature-[name]-testing` - Testing protocols and results

## FOLDER ORGANIZATION STANDARDS
**Maintain clean, logical directory structure:**
```
includes/
├── settings/           # Settings-related classes (max 800 lines each)
├── ai-integration/     # AI provider integrations (max 800 lines each)  
├── utils/             # Utility functions and helpers
├── features/          # Feature-specific implementations
│   ├── search-replace/    # Complex features in subdirectories
│   ├── lorem-detector/    # Each with multiple organized files
│   └── [new-feature]/     # Follow established patterns
└── interfaces/        # Abstract classes and interfaces
```

## FILE SIZE MONITORING COMMANDS
```bash
# Check all PHP files over 700 lines (warning threshold)
find . -name "*.php" -exec wc -l {} \; | awk '$1 > 700 {print $0}' | sort -nr

# Check files approaching limit (750+ lines)  
find . -name "*.php" -exec wc -l {} \; | awk '$1 > 750 {print "WARNING: " $0}' | sort -nr

# Check files at or over limit (800+ lines)
find . -name "*.php" -exec wc -l {} \; | awk '$1 >= 800 {print "ERROR: " $0}' | sort -nr
```

# 🔬 MANDATORY RESEARCH PROCESS

## PRE-IMPLEMENTATION RESEARCH (CRITICAL - 98% CERTAINTY REQUIRED)
**Before starting ANY new feature, you MUST complete this research process and achieve 98% solution confidence:**

### 1. Architecture Analysis (Use serena MCP - MANDATORY)
- **Check memory-bank/ documentation** for existing patterns and decisions
- **Search for similar implementations** using search_for_pattern extensively
- **Identify reusable code patterns** with find_symbol across the codebase  
- **Understand dual-system architecture** (buttons vs shortcuts) constraints
- **Review authorization system** integration requirements
- **🎯 CONFIDENCE CHECK**: Can you explain exactly how your solution integrates? YES/NO

### 2. WordPress Integration Points (VERIFY ALL)
- **Plugin initialization flow** in slmm-seo-plugin.php - confirm hook order
- **Hook system usage** (admin_enqueue_scripts, plugins_loaded, etc.) - verify timing
- **Settings storage patterns** (chatgpt_generator_options, slmm_gpt_prompts) - check format
- **AJAX endpoint patterns** and nonce verification - confirm security implementation  
- **Capability checks** and authorization system integration - validate permissions
- **🎯 CONFIDENCE CHECK**: Have you verified each integration point exists and works? YES/NO

### 3. AI Integration Analysis (VALIDATE COMPATIBILITY)
- **Multi-provider support** (OpenAI, OpenRouter, Anthropic) - check API compatibility
- **Prompt execution systems** (dual architecture constraints) - verify both systems work
- **Data localization requirements** (slmmGptPromptData structure) - confirm data format
- **API key management** and secure storage patterns - validate security approach
- **🎯 CONFIDENCE CHECK**: Will your solution work with ALL existing AI providers? YES/NO

### 4. Implementation Planning (CERTAINTY VALIDATION)
- **Break down using existing patterns** from memory-bank/patterns/ - confirm pattern match
- **Plan dual-system integration** (if GPT prompts involved) - test both execution paths
- **Consider Bricks Builder compatibility** requirements - verify visual builder works
- **Define memory-bank documentation** updates needed - plan knowledge preservation  
- **🎯 CONFIDENCE CHECK**: Have you mentally tested this solution in production? YES/NO

### 5. Solution Confidence Assessment (MANDATORY BEFORE PROCEEDING)
**STOP HERE and evaluate your research confidence:**
- [ ] All serena tools used extensively (find_symbol, search_for_pattern, get_symbols_overview)
- [ ] Existing patterns thoroughly analyzed and confirmed compatible
- [ ] Integration points verified to exist and function as expected
- [ ] Dependencies validated (all required functions/classes confirmed)
- [ ] Edge cases considered (WordPress versions, plugin conflicts, browser differences)
- [ ] Mental production testing completed with realistic scenarios
- [ ] **OVERALL CONFIDENCE: [__]%** (Must be 98%+ to proceed)

## PARALLEL RESEARCH METHODOLOGY
- **Always use serena tools in parallel** for efficiency
- **Multiple concurrent symbol searches** across different aspects
- **Parallel pattern analysis** using search_for_pattern
- **Concurrent architecture review** across multiple files

# 🛠️ serena MCP TOOL USAGE

## MANDATORY SYMBOLIC APPROACH
**NEVER read entire source files without using symbolic tools first!**

### Primary Workflow
```
1. get_symbols_overview - Understand file structure and top-level symbols
2. find_symbol - Locate specific functions/classes with include_body=false first
3. find_symbol (with include_body=true) - Read only necessary symbol bodies
4. find_referencing_symbols - Understand usage patterns and dependencies
5. search_for_pattern - Find similar implementations or specific patterns
6. Read (full file) - ONLY as absolute last resort
```

### Efficient Pattern Discovery
```javascript
// CORRECT - Symbolic approach
1. get_symbols_overview("includes/ai-integration/openai-integration.php")
2. find_symbol("execute_prompt", relative_path="includes/", substring_matching=true)
3. find_referencing_symbols("executePromptDirectly", relative_path="snippets/chat_gpt_title_and_description_generator_v2_0.php")

// WRONG - Reading entire files first
1. Read("includes/ai-integration/openai-integration.php") // 🚫 INEFFICIENT
```

### Memory Bank Integration
- **Always check memory-bank/** before analyzing code
- **Use read_memory** for established patterns and decisions
- **Write new findings** to memory bank for future sessions
- **Reference existing documentation** in memory-bank/patterns/
- **Follow memory management patterns** from memory-bank/memory-management-patterns.md

## search_for_pattern Usage Patterns
```javascript
// Find GPT prompt implementations
search_for_pattern("executePromptDirectly", restrict_search_to_code_files=true)

// Find authorization patterns
search_for_pattern("slmm_seo_check_visibility", paths_include_glob="*.php")

// Find data localization patterns
search_for_pattern("slmmGptPromptData", paths_include_glob="**/*.php")
```

# 🚨 CRITICAL PROTECTION RULES

## ABSOLUTE KEYBOARD SHORTCUT PROTECTION
**NEVER EVER modify these systems unless explicitly broken:**
- `snippets/chat_gpt_title_and_description_generator_v2_0.php` - Keyboard shortcut system
- `assets/js/slmm-keyboard-shortcuts.js` - Shortcut key bindings
- `executePromptDirectly()` function - Direct prompt execution

**These are mission-critical systems. ANY changes can break keyboard shortcuts.**

## DUAL-SYSTEM ARCHITECTURE (NON-NEGOTIABLE)
- **Button System**: `assets/js/slmm-prompt-execution.js` (DOM-driven)
- **Keyboard Shortcut System**: `snippets/chat_gpt_title_and_description_generator_v2_0.php` (data-driven)
- **NEVER assume these work the same way**
- **ALWAYS test both systems independently**
- **Data localization MUST support both systems**

## DATA LOCALIZATION PROTECTION
**CRITICAL RULE**: Always localize `slmmGptPromptData` regardless of prompt availability:
```php
// CORRECT - Always localize (from memory-bank/patterns/data-localization.md)
wp_localize_script('script', 'slmmGptPromptData', array(
    'prompts' => $prompts ?: array(),
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('slmm_execute_gpt_prompt')
));

// WRONG - Conditional localization breaks shortcuts
if (!empty($prompts)) {
    wp_localize_script('script', 'slmmGptPromptData', array('prompts' => $prompts));
}
```

## AUTHORIZATION SYSTEM PROTECTION
- **Legacy super admin**: username `deme` - Maintained for backward compatibility
- **Sophisticated emergency access**: 64-character random tokens like `&slmm_emergency=accessFJNHftqE2u0tTnw9qNWmGRKyamIIs4BTSNvahYglf6lfC3EZvdgcP8SqTO78DUc1`
- **URL format**: `http://localhost:8884/wp-admin/admin.php?page=chatgpt-generator-settings&slmm_emergency=[64-char-token]`
- **Enhanced security**: Requires pre-existing admin authentication + auto-whitelisting + comprehensive logging
- **Authorization check function**: `slmm_seo_check_visibility_authorization()` - Core security

# 🔒 MANDATORY SECURITY PATTERNS (CRITICAL REQUIREMENT)

## ABSOLUTE SECURITY REQUIREMENT
**EVERY new feature MUST implement ALL security patterns below - NO EXCEPTIONS**

### Context Understanding: Admin-Only, Whitelisted Environment
This plugin operates in a **trusted admin environment** with **explicit user whitelisting**:
- **Admin-only access**: All features restricted to `manage_options` capability
- **User authorization system**: Additional whitelist layer via `slmm_seo_check_visibility_authorization()`
- **API keys in client-side**: Acceptable in this admin-only, authorized context
- **Reduced attack surface**: No public-facing endpoints, no frontend user input

**However, defense-in-depth remains MANDATORY for plugin integrity.**

## 1. MANDATORY NONCE VERIFICATION (ABSOLUTE REQUIREMENT)

### AJAX Handler Nonce Pattern (CRITICAL - NEVER SKIP)
```php
// MANDATORY for ALL AJAX handlers - no exceptions
function slmm_handle_ajax_action() {
    // STEP 1: Verify nonce FIRST - before ANY processing
    if (!wp_verify_nonce($_POST['nonce'], 'slmm_action_nonce')) {
        wp_die('Security check failed', 'Security Error', array('response' => 403));
    }

    // STEP 2: Additional capability check
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions', 'Authorization Error', array('response' 403));
    }

    // STEP 3: Authorization system integration
    if (!slmm_seo_check_visibility_authorization()) {
        wp_die('Plugin access denied', 'Authorization Error', array('response' => 403));
    }

    // Now safe to process request
    $sanitized_input = sanitize_textarea_field($_POST['input']);

    // Always return structured JSON response
    wp_send_json_success($result);
}
```

### Form Nonce Pattern (MANDATORY)
```php
// Creating forms with nonces
function slmm_render_settings_form() {
    echo '<form method="post" action="">';
    wp_nonce_field('slmm_settings_save', 'slmm_settings_nonce');
    // Form fields...
    echo '</form>';
}

// Processing form submissions
function slmm_process_settings_form() {
    if (isset($_POST['submit'])) {
        // MANDATORY nonce verification
        if (!wp_verify_nonce($_POST['slmm_settings_nonce'], 'slmm_settings_save')) {
            wp_die('Security check failed');
        }

        // Additional checks...
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        // Process form...
    }
}
```

### JavaScript Nonce Integration (REQUIRED)
```javascript
// Always include nonce in AJAX requests
jQuery.post(slmmGptPromptData.ajax_url, {
    action: 'slmm_ajax_action',
    nonce: slmmGptPromptData.nonce,  // MANDATORY - always include
    data: sanitizedData
}, function(response) {
    if (response.success) {
        // Handle success
    } else {
        // Handle error - could be security failure
        console.error('Request failed:', response.data);
    }
});
```

## 2. REQUIRED CAPABILITY CHECKING (ENFORCE STRICTLY)

### Standard Capability Pattern (MANDATORY)
```php
// ALWAYS check capabilities before ANY admin functionality
function slmm_admin_functionality() {
    // Primary capability check - manage_options required
    if (!current_user_can('manage_options')) {
        wp_die('You do not have sufficient permissions to access this page.');
    }

    // Secondary authorization check via plugin system
    if (!slmm_seo_check_visibility_authorization()) {
        wp_die('Plugin access denied by authorization system.');
    }

    // Proceed with admin functionality
}
```

### Menu Registration Capability Control (REQUIRED)
```php
// Proper capability control in menu registration
add_action('admin_menu', 'slmm_add_admin_menu');

function slmm_add_admin_menu() {
    add_options_page(
        'SLMM SEO Settings',
        'SLMM SEO',
        'manage_options',  // MANDATORY - restrict to admin users
        'slmm-seo-settings',
        'slmm_settings_page_callback'
    );
}

function slmm_settings_page_callback() {
    // MANDATORY - double-check capabilities in callback
    if (!current_user_can('manage_options')) {
        wp_die('Access denied');
    }

    // Additional plugin authorization
    if (!slmm_seo_check_visibility_authorization()) {
        wp_die('Plugin access denied');
    }

    // Render settings page
}
```

## 3. SQL INJECTION PREVENTION (ABSOLUTE REQUIREMENT)

### Database Query Pattern (MANDATORY - USE ONLY THIS APPROACH)
```php
// CORRECT - Always use prepared statements
function slmm_get_posts_by_content($search_term) {
    global $wpdb;

    // MANDATORY - Use prepared statements for ALL queries
    $results = $wpdb->get_results($wpdb->prepare(
        "SELECT ID, post_title, post_content
         FROM {$wpdb->posts}
         WHERE post_content LIKE %s
         AND post_status = 'publish'",
        '%' . $wpdb->esc_like($search_term) . '%'
    ));

    return $results;
}

// CORRECT - Multiple parameter preparation
function slmm_search_and_replace_content($search, $replace, $post_types) {
    global $wpdb;

    // Prepare IN clause safely
    $placeholders = implode(',', array_fill(0, count($post_types), '%s'));

    $query = $wpdb->prepare(
        "SELECT ID, post_content
         FROM {$wpdb->posts}
         WHERE post_content LIKE %s
         AND post_type IN ($placeholders)
         AND post_status = 'publish'",
        '%' . $wpdb->esc_like($search) . '%',
        ...$post_types
    );

    return $wpdb->get_results($query);
}
```

### NEVER DO (FORBIDDEN PATTERNS)
```php
// FORBIDDEN - Direct string interpolation
$results = $wpdb->get_results("SELECT * FROM {$wpdb->posts} WHERE post_title = '$user_input'");

// FORBIDDEN - sprintf without prepare
$query = sprintf("SELECT * FROM {$wpdb->posts} WHERE ID = %d", $id);
$results = $wpdb->get_results($query);

// FORBIDDEN - Building queries with concatenation
$query = "SELECT * FROM {$wpdb->posts} WHERE post_content LIKE '%" . $search . "%'";
```

## 4. XSS PREVENTION REQUIREMENTS (MANDATORY OUTPUT ESCAPING)

### Output Escaping Patterns (REQUIRED FOR ALL OUTPUT)
```php
// HTML content escaping (most common)
echo '<h2>' . esc_html($page_title) . '</h2>';
echo '<p>' . esc_html__('Description text', 'slmm-seo') . '</p>';

// Attribute escaping in HTML tags
echo '<input type="text" value="' . esc_attr($form_value) . '" />';
echo '<div class="' . esc_attr($css_class) . '">';

// URL escaping for links
echo '<a href="' . esc_url($external_link) . '">Link Text</a>';

// JavaScript data (use wp_localize_script preferred)
echo '<script>var data = ' . wp_json_encode($data) . ';</script>';

// For admin URLs (WordPress internal)
echo '<a href="' . esc_url(admin_url('admin.php?page=slmm-settings')) . '">Settings</a>';
```

### Safe Data Localization Pattern (PREFERRED METHOD)
```php
// CORRECT - Use wp_localize_script for JavaScript data
wp_localize_script('slmm-admin-script', 'slmmSecureData', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('slmm_admin_action'),
    'messages' => array(
        'success' => esc_html__('Operation completed successfully', 'slmm-seo'),
        'error' => esc_html__('An error occurred', 'slmm-seo')
    ),
    'settings' => array(
        'max_length' => absint($max_length),
        'enable_feature' => (bool) $enable_feature
    )
));
```

### Text Domain Requirements (MANDATORY)
```php
// ALWAYS use text domain for translatable strings
echo esc_html__('Settings saved successfully', 'slmm-seo');
echo esc_html_e('Error occurred during processing', 'slmm-seo');

// For sprintf patterns
echo sprintf(
    esc_html__('Processing %d of %d items', 'slmm-seo'),
    absint($current),
    absint($total)
);
```

## 5. AUTHORIZATION SYSTEM INTEGRATION (CRITICAL)

### Plugin Authorization Pattern (MANDATORY FOR ALL FEATURES)
```php
// MANDATORY initialization check in ALL feature classes
class SLMM_New_Feature {
    public function __construct() {
        // CRITICAL - Check authorization before ANY initialization
        if (!slmm_seo_check_visibility_authorization()) {
            return; // Silent fail - do not initialize
        }

        // Safe to initialize feature
        add_action('admin_init', array($this, 'init'));
        add_action('wp_ajax_slmm_new_feature', array($this, 'handle_ajax'));
    }

    public function init() {
        // Additional capability check in methods
        if (!current_user_can('manage_options')) {
            return;
        }

        // Initialize feature functionality
    }

    public function handle_ajax() {
        // Triple security layer for AJAX
        if (!wp_verify_nonce($_POST['nonce'], 'slmm_new_feature_nonce')) {
            wp_die('Nonce verification failed');
        }

        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        if (!slmm_seo_check_visibility_authorization()) {
            wp_die('Plugin access denied');
        }

        // Safe to process AJAX request
    }
}
```

### Asset Loading Authorization (REQUIRED)
```php
// MANDATORY - Only load assets for authorized users
function slmm_enqueue_admin_assets() {
    // Check authorization before loading ANY assets
    if (!slmm_seo_check_visibility_authorization()) {
        return; // Do not load assets for unauthorized users
    }

    if (!current_user_can('manage_options')) {
        return; // Additional capability check
    }

    // Safe to load admin assets
    wp_enqueue_script(
        'slmm-admin-script',
        SLMM_SEO_PLUGIN_URL . 'assets/js/admin-script.js',
        array('jquery'),
        SLMM_SEO_VERSION,
        true
    );

    // MANDATORY - Include nonce in localized data
    wp_localize_script('slmm-admin-script', 'slmmAdminData', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('slmm_admin_action'),
        'user_authorized' => slmm_seo_check_visibility_authorization()
    ));
}
add_action('admin_enqueue_scripts', 'slmm_enqueue_admin_assets');
```

## 6. CLIENT-SIDE API KEY HANDLING (ADMIN-CONTEXT GUIDELINES)

### Acceptable Patterns in Admin Context
```php
// ACCEPTABLE - API keys in admin-only, whitelisted environment
function slmm_localize_ai_settings() {
    // Only for authorized admin users
    if (!slmm_seo_check_visibility_authorization() || !current_user_can('manage_options')) {
        return;
    }

    wp_localize_script('slmm-ai-script', 'slmmAiConfig', array(
        'openai_api_key' => get_option('slmm_openai_api_key', ''),
        'anthropic_api_key' => get_option('slmm_anthropic_api_key', ''),
        // ACCEPTABLE because:
        // 1. Admin-only access (manage_options)
        // 2. Additional authorization layer (plugin whitelist)
        // 3. No public-facing exposure
        // 4. Required for client-side AI integration
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('slmm_ai_action')
    ));
}
```

### Security Notes for API Keys
```php
// RECOMMENDED - Additional protection measures
function slmm_protect_api_keys() {
    // 1. Option-level protection
    add_filter('pre_option_slmm_openai_api_key', function($value) {
        if (!slmm_seo_check_visibility_authorization()) {
            return ''; // Return empty for unauthorized users
        }
        return $value;
    });

    // 2. REST API protection (if used)
    add_filter('rest_authentication_errors', function($result) {
        if (strpos($_SERVER['REQUEST_URI'], '/slmm/') !== false) {
            if (!slmm_seo_check_visibility_authorization()) {
                return new WP_Error('rest_forbidden', 'Access denied', array('status' => 403));
            }
        }
        return $result;
    });
}
```

## 7. SECURITY VALIDATION CHECKLIST (MANDATORY VERIFICATION)

### Pre-Implementation Security Review (REQUIRED)
**BEFORE implementing any new feature, verify ALL items:**

#### Input Security Checklist
- [ ] **All user inputs sanitized** using appropriate WordPress functions
- [ ] **SQL queries use prepared statements** - NO string concatenation
- [ ] **Nonce verification implemented** for all forms and AJAX calls
- [ ] **Capability checks enforced** (`manage_options` minimum)
- [ ] **Plugin authorization integrated** (`slmm_seo_check_visibility_authorization()`)

#### Output Security Checklist
- [ ] **All output escaped** using `esc_html()`, `esc_attr()`, `esc_url()`
- [ ] **JavaScript data properly localized** using `wp_localize_script()`
- [ ] **Text domains used consistently** for all translatable strings
- [ ] **JSON data encoded safely** using `wp_json_encode()`
- [ ] **Admin URLs escaped** using `esc_url(admin_url())`

#### Authorization Security Checklist
- [ ] **Feature initialization protected** by authorization check
- [ ] **Asset loading restricted** to authorized users only
- [ ] **AJAX endpoints secured** with triple security layer
- [ ] **Menu items capability-controlled** (`manage_options`)
- [ ] **Settings pages protected** with double capability checks

#### WordPress Integration Checklist
- [ ] **Hooks used correctly** with proper priority and parameters
- [ ] **Options stored securely** with appropriate sanitization
- [ ] **Transients properly prefixed** and secured if used
- [ ] **Cron jobs authorized** if scheduled tasks implemented
- [ ] **Plugin deactivation handled** securely if cleanup needed

### Security Testing Protocol (MANDATORY)
```php
// REQUIRED - Test each security layer independently
function slmm_security_test_suite() {
    // Test 1: Nonce verification
    // - Submit AJAX without nonce → Should fail
    // - Submit with invalid nonce → Should fail
    // - Submit with valid nonce → Should succeed

    // Test 2: Capability checks
    // - Access as subscriber → Should fail
    // - Access as editor → Should fail
    // - Access as admin → Should proceed to authorization check

    // Test 3: Authorization system
    // - Access as non-whitelisted admin → Should fail
    // - Access as whitelisted admin → Should succeed

    // Test 4: Input sanitization
    // - Submit malicious scripts → Should be escaped/sanitized
    // - Submit SQL injection attempts → Should be parameterized
    // - Submit XSS payloads → Should be escaped in output
}
```

## 8. REQUIRED SECURITY TESTING PATTERNS (VALIDATION REQUIREMENTS)

### Browser Console Security Tests (MANDATORY)
```javascript
// Test 1: Verify authorization data availability
console.log('User authorized:', slmmAdminData?.user_authorized);
console.log('Nonce available:', slmmAdminData?.nonce);

// Test 2: Test AJAX security enforcement
jQuery.post(slmmAdminData.ajax_url, {
    action: 'slmm_test_action',
    // Deliberately omit nonce to test security
    test_data: 'security_test'
}, function(response) {
    console.log('No nonce test:', response); // Should fail
});

// Test 3: Test with invalid nonce
jQuery.post(slmmAdminData.ajax_url, {
    action: 'slmm_test_action',
    nonce: 'invalid_nonce_test',
    test_data: 'security_test'
}, function(response) {
    console.log('Invalid nonce test:', response); // Should fail
});

// Test 4: Test with valid nonce
jQuery.post(slmmAdminData.ajax_url, {
    action: 'slmm_test_action',
    nonce: slmmAdminData.nonce,
    test_data: 'security_test'
}, function(response) {
    console.log('Valid request test:', response); // Should succeed if authorized
});
```

### PHP Security Validation Functions (REQUIRED HELPERS)
```php
// MANDATORY - Include in all new feature files
function slmm_validate_security_context($nonce_action = '') {
    $errors = array();

    // Check 1: WordPress capability
    if (!current_user_can('manage_options')) {
        $errors[] = 'Insufficient WordPress capabilities';
    }

    // Check 2: Plugin authorization
    if (!slmm_seo_check_visibility_authorization()) {
        $errors[] = 'Plugin authorization failed';
    }

    // Check 3: Nonce verification (if provided)
    if (!empty($nonce_action) && !wp_verify_nonce($_POST['nonce'], $nonce_action)) {
        $errors[] = 'Nonce verification failed';
    }

    // Check 4: AJAX context validation
    if (wp_doing_ajax() && empty($_POST['nonce'])) {
        $errors[] = 'AJAX request missing nonce';
    }

    return empty($errors) ? true : $errors;
}

// Usage in feature methods
function slmm_feature_ajax_handler() {
    $security_check = slmm_validate_security_context('slmm_feature_action');

    if ($security_check !== true) {
        wp_send_json_error(array(
            'message' => 'Security validation failed',
            'errors' => $security_check
        ));
    }

    // Safe to proceed with feature logic
}
```

### Security Documentation Requirements (MANDATORY)
**Every new feature MUST include security documentation:**
```php
/**
 * Feature Security Documentation
 *
 * Security Measures Implemented:
 * 1. Nonce verification: [action_name]
 * 2. Capability requirement: manage_options
 * 3. Plugin authorization: slmm_seo_check_visibility_authorization()
 * 4. Input sanitization: [list sanitization functions used]
 * 5. Output escaping: [list escaping functions used]
 *
 * Tested Attack Vectors:
 * - XSS payload injection: PROTECTED via esc_html()
 * - SQL injection attempts: PROTECTED via $wpdb->prepare()
 * - CSRF attacks: PROTECTED via wp_nonce_field()
 * - Unauthorized access: PROTECTED via authorization layers
 *
 * Admin-Context Justification:
 * - Feature restricted to manage_options capability
 * - Additional plugin-level authorization required
 * - No public-facing endpoints exposed
 * - Client-side API keys acceptable in this secured context
 */
```

## SECURITY IMPLEMENTATION PRIORITY ORDER (MANDATORY SEQUENCE)

### Phase 1: Foundation Security (IMPLEMENT FIRST)
1. **Authorization checks** - Plugin and WordPress capability verification
2. **Nonce verification** - All forms and AJAX endpoints
3. **Input sanitization** - All user data processing

### Phase 2: Data Security (IMPLEMENT SECOND)
1. **SQL injection prevention** - Prepared statements for all queries
2. **Output escaping** - All data display and HTML generation
3. **Safe data localization** - JavaScript data passing

### Phase 3: Integration Security (IMPLEMENT THIRD)
1. **Asset loading protection** - Authorized users only
2. **Menu and page access control** - Capability enforcement
3. **Settings storage security** - Proper option handling

### Phase 4: Testing and Validation (IMPLEMENT LAST)
1. **Security test implementation** - Browser and PHP validation
2. **Documentation completion** - Security measures documentation
3. **Attack vector testing** - Manual security validation

**🚨 CRITICAL REMINDER: In this admin-only, whitelisted environment, security measures protect plugin integrity and prevent privilege escalation rather than public-facing attacks. However, ALL security patterns remain MANDATORY for defense-in-depth and code quality.**

# 📋 ESSENTIAL DEVELOPMENT PATTERNS

## Plugin Architecture (v4.10.0)
- **Entry Point**: `plugin.php` → loads `slmm-seo-plugin.php`
- **Initialization**: `plugins_loaded` hook with visibility check first
- **Settings Structure**: Modular settings in `includes/settings/`
- **AI Integrations**: Provider-specific classes in `includes/ai-integration/`
- **Utilities**: Feature-specific utilities in `includes/utils/`
- **Frontend Assets**: Version-controlled loading in `assets/`

### Core Feature Architecture
```php
// Standard feature initialization pattern
function slmm_seo_plugin_init() {
    // Visibility check FIRST
    if (!slmm_seo_check_visibility_authorization()) {
        return;
    }
    
    // Initialize settings
    $general_settings = new SLMM_General_Settings();
    $general_settings->init();
    
    // Initialize feature classes
    SLMM_Protected_Words::get_instance();
    new SLMM_Lorem_Ipsum_Detector();
    (new SLMM_Prompt_Settings())->init();
}
```

## Multi-Instance Support Pattern
```php
// Static counter pattern for unique IDs (critical for multi-instance support)
static $instance_counter = 0;
$instance_counter++;
$unique_id = 'element-' . $instance_counter;
```

## AJAX Integration Pattern
```php
// Standard AJAX handler pattern
add_action('wp_ajax_slmm_action_name', 'slmm_handle_action');

function slmm_handle_action() {
    // Nonce verification
    if (!wp_verify_nonce($_POST['nonce'], 'slmm_action_nonce')) {
        wp_die('Security check failed');
    }
    
    // Capability check
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    // Input sanitization
    $input = sanitize_textarea_field($_POST['input']);
    
    // Process and return JSON
    wp_send_json_success($result);
}
```

## Asset Loading Pattern (v4.10.0 Updates)
```php
// Conditional asset loading with Bricks Builder detection
function slmm_enqueue_scripts() {
    $is_bricks = isset($_GET['bricks']) && $_GET['bricks'] === 'run';
    
    wp_enqueue_script(
        'slmm-script',
        SLMM_SEO_PLUGIN_URL . 'assets/js/script.js',
        array('jquery'),
        SLMM_SEO_VERSION,
        true
    );
    
    // Always localize data for dual-system support
    wp_localize_script('slmm-script', 'slmmGptPromptData', array(
        'prompts' => get_option('slmm_gpt_prompts', array()),
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('slmm_execute_gpt_prompt')
    ));
}
```

## Plugin.php Integration Protocols (MANDATORY FOR ALL NEW FILES)

### New File Registration Process (ABSOLUTE REQUIREMENT)
**EVERY new PHP file MUST be properly registered in plugin.php:**

1. **File Inclusion Order** - Add requires in logical dependency order
2. **Class Existence Check** - Always verify class exists before instantiation
3. **Proper Initialization** - Follow established initialization patterns
4. **Version Tracking** - Ensure new files inherit version constants

### File Registration Template (MANDATORY FORMAT)
```php
// In plugin.php - Add new file includes in dependency order
require_once __DIR__ . '/includes/utils/class-helper-functions.php';          // Utilities first
require_once __DIR__ . '/includes/settings/class-new-settings.php';          // Settings second
require_once __DIR__ . '/includes/features/class-new-feature.php';           // Features third
require_once __DIR__ . '/includes/ai-integration/class-new-provider.php';    // AI integrations last
```

### Initialization Pattern (ENFORCE STRICTLY)
```php
// In slmm-seo-plugin.php - Initialize new classes in plugin init function
function slmm_seo_plugin_init() {
    // Visibility check FIRST (never skip this)
    if (!slmm_seo_check_visibility_authorization()) {
        return;
    }
    
    // Initialize utilities (no dependencies)
    if (class_exists('SLMM_Helper_Functions')) {
        SLMM_Helper_Functions::get_instance();
    }
    
    // Initialize settings (may depend on utilities)
    if (class_exists('SLMM_New_Settings')) {
        (new SLMM_New_Settings())->init();
    }
    
    // Initialize features (may depend on settings)
    if (class_exists('SLMM_New_Feature')) {
        new SLMM_New_Feature();
    }
    
    // Initialize AI integrations (may depend on settings and features)
    if (class_exists('SLMM_New_Provider')) {
        SLMM_New_Provider::get_instance();
    }
}
```

### File Size Validation Integration
**Before adding ANY file to plugin.php:**
```bash
# Check file size before registration
wc -l includes/new-feature/class-new-feature.php

# If file exceeds 800 lines, MUST split before registration
if [[ $(wc -l < file.php) -gt 800 ]]; then
    echo "ERROR: File exceeds 800 lines - split required before plugin.php integration"
    exit 1
fi
```

### Directory Structure Compliance (MANDATORY)
**New files MUST follow established directory patterns:**
```php
// CORRECT - Follow established patterns
require_once __DIR__ . '/includes/settings/class-new-settings.php';
require_once __DIR__ . '/includes/features/search-replace/class-search-engine.php';
require_once __DIR__ . '/includes/features/search-replace/class-replace-engine.php';
require_once __DIR__ . '/includes/utils/class-file-size-monitor.php';

// WRONG - Random placement breaks organization
require_once __DIR__ . '/random-file.php';
require_once __DIR__ . '/includes/mixed-purposes.php';
```

### Dependency Management (CRITICAL)
**Ensure proper loading order to prevent fatal errors:**
```php
// Base utilities and interfaces first
require_once __DIR__ . '/includes/interfaces/interface-provider.php';
require_once __DIR__ . '/includes/utils/class-base-utility.php';

// Settings that may use utilities
require_once __DIR__ . '/includes/settings/class-provider-settings.php';

// Features that may use settings and utilities  
require_once __DIR__ . '/includes/features/class-advanced-feature.php';

// Providers that implement interfaces
require_once __DIR__ . '/includes/ai-integration/class-concrete-provider.php';
```

### Integration Validation Checklist (MANDATORY VERIFICATION)
**Before committing any plugin.php changes:**
- [ ] File size under 800 lines verified
- [ ] Proper directory structure followed
- [ ] Dependency order respected
- [ ] Class existence checks implemented
- [ ] Initialization follows established patterns
- [ ] No fatal errors on plugin activation
- [ ] Dual-system compatibility maintained
- [ ] Authorization system integration preserved

# 🎨 CURRENT STYLING & UI PATTERNS (v4.10.0)

## WordPress Admin Button Standardization
```css
/* 40px minimum height standard (from slmm-admin.css) */
.wp-core-ui .button,
.wp-core-ui .button-primary,
.wp-core-ui .button-secondary {
    min-height: 40px !important;
    line-height: 38px !important;
    padding: 0 12px !important;
}

/* Proper vertical alignment */
#custom-editor-buttons .button {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}
```

## Enhanced Security UI (v4.10.0 Dark Theme)
```css
/* New warning box styling - elegant dark surface */
.slmm-warning-box {
    background-color: #1a1a1a; /* Dark surface */
    border: 4px dashed #f97316; /* Orange dashed border */
    color: #d1d5db; /* Light text */
    padding: 20px;
    border-radius: 8px;
}

/* Code element styling with proper contrast */
.slmm-warning-box code {
    background: #374151;
    border: 1px solid #4b5563;
    color: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
}
```

## Professional Checkbox Design (v4.10.0)
```css
/* Custom checkbox styling for search and replace forms */
.slmm-checkbox {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    background: white;
    cursor: pointer;
}

.slmm-checkbox:checked {
    background: #3b82f6;
    border-color: #3b82f6;
}

.slmm-checkbox:hover {
    border-color: #6b7280;
}
```

## Icon System Consistency
```css
/* Professional icon integration across all tabs */
.slmm-tab-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    vertical-align: middle;
}

/* Hover and active states for navigation clarity */
.slmm-tab.active .slmm-tab-icon {
    opacity: 1;
    filter: brightness(1.2);
}
```

# 💾 MEMORY BANK SYSTEM INTEGRATION

## Directory Structure & Usage
```
memory-bank/
├── README.md - System overview
├── current-session.md - Session context
├── decisions/ - Architectural decisions
├── patterns/ - Code patterns and best practices  
├── issues/ - Known issues and solutions
├── features/ - Feature documentation
└── todo/ - Project todos and overviews
```

## Memory Bank Workflow
```javascript
// 1. Always check existing memories first
list_memories() 

// 2. Read relevant memory files
read_memory("dual-system-architecture") 
read_memory("data-localization-patterns")

// 3. Write new findings for future sessions
write_memory("new-feature-patterns", "# Feature Implementation\n...")
```

## Decision Documentation Pattern
```markdown
# Decision: [Title]
## Date: YYYY-MM-DD
## Context: [Situation requiring decision]
## Decision: [What was decided] 
## Rationale: [Why this decision was made]
## Consequences: [Impact and implications]
## Status: [Active/Deprecated/Superseded]
```

## MANDATORY SERENA MEMORY REQUIREMENTS (CRITICAL)

### Context Window Protection Protocol (ABSOLUTE REQUIREMENT)
**BEFORE any complex implementation task, you MUST:**

1. **Create task PRD memory** with serena write_memory
2. **Document architectural decisions** in structured memory files
3. **Estimate file sizes** and document split strategies if needed
4. **Create implementation roadmap** with step-by-step breakdown
5. **Update progress memories** after each major milestone

### Mandatory Memory Creation Triggers
**You MUST create serena memory when:**
- Task has 3+ implementation steps
- Multiple files will be created/modified
- Complex integrations with existing systems required
- File size estimates approach 700+ lines
- Context window is >75% utilized
- Implementation will span multiple sessions

### PRD Memory Template (MANDATORY FORMAT)
**Every complex task requires a comprehensive PRD memory:**
```markdown
# Task: [Task Name] - Implementation PRD
## Date: [YYYY-MM-DD]
## Complexity: [Simple/Medium/Complex]
## Estimated Duration: [Sessions/Hours]
## File Size Estimates: [Per file, with totals]
## Architecture Overview: [High-level approach]
## Integration Points: [Existing code connections]
## Plugin.php Changes: [Required modifications]
## Implementation Steps:
1. [Step 1 with file size estimate]
2. [Step 2 with file size estimate]
3. [etc.]
## Testing Requirements: [Validation approach]
## Risk Factors: [Potential complications]
## Rollback Plan: [If implementation fails]
```

### Memory Naming Standards (ENFORCE STRICTLY)
**Use consistent naming for easy retrieval across sessions:**
- `task-[name]-prd` - Primary requirements document
- `task-[name]-architecture` - Architectural decisions and patterns
- `task-[name]-progress` - Implementation progress and milestones
- `task-[name]-issues` - Problems encountered and solutions
- `task-[name]-testing` - Testing protocols and results
- `file-size-analysis-[date]` - File size monitoring and split decisions

### Implementation Progress Memory Updates
**Update memory after EVERY major milestone:**
```javascript
// Template for progress updates
write_memory("task-[name]-progress", `
# Progress Update: [Date]
## Completed:
- [Completed items with file sizes]
## In Progress:
- [Current work with size estimates]
## Next Steps:
- [Planned work with size estimates]  
## File Size Status:
- [Current line counts for each file]
## Architecture Decisions Made:
- [Any new patterns or decisions]
## Issues Encountered:
- [Problems and solutions]
`);
```

### Context Window Emergency Protocol
**When approaching context limit (>90% utilized):**
1. **IMMEDIATELY create emergency memory** with current state
2. **Document all file changes made** with line counts
3. **List remaining tasks** with size estimates  
4. **Note integration points** completed/remaining
5. **Create handoff instructions** for next session

# 🔍 ENHANCED DEBUGGING & TESTING (v4.10.0)

## Console Debug Commands
```javascript
// Check dual-system data availability
console.log('Shortcut data available:', typeof slmmGptPromptData !== 'undefined');
console.log('Prompts loaded:', slmmGptPromptData?.prompts);
console.log('Button system ready:', typeof jQuery !== 'undefined' && jQuery('.slmm-gpt-button').length > 0);

// Test shortcut execution directly  
if (typeof executePromptDirectly === 'function' && tinyMCE?.activeEditor) {
    executePromptDirectly('0', tinyMCE.activeEditor);
}

// Check authorization system
console.log('Authorization enabled:', slmmSettings?.visibilityEnabled);
console.log('Current user authorized:', slmmSettings?.userAuthorized);
```

## Development Mode System (v4.10.0)
```php
// Admin bar colorization for development identification
add_action('admin_bar_menu', 'slmm_dev_mode_indicator');
add_action('wp_head', 'slmm_dev_mode_styles');

// Environment detection patterns
$is_development = (defined('WP_DEBUG') && WP_DEBUG) || 
                  (isset($_GET['slmm_dev_mode']) && $_GET['slmm_dev_mode'] === 'true');
```

## Authorization System Testing
```php
// Debug access methods
// 1. Super admin backdoor: username 'deme'
// 2. Debug URL parameter: ?slmm_debug=access  
// 3. Settings configuration: authorized_admins array

// Test authorization check
$authorized = slmm_seo_check_visibility_authorization();
error_log('SLMM Authorization Result: ' . ($authorized ? 'PASSED' : 'FAILED'));
```

## Dual System Validation
```javascript
// Button system validation
function testButtonSystem() {
    const buttons = document.querySelectorAll('.slmm-gpt-button');
    console.log(`Button system: ${buttons.length} buttons found`);
    
    buttons.forEach((button, index) => {
        console.log(`Button ${index}: ID=${button.id}, Prompt=${button.dataset.promptIndex}`);
    });
}

// Keyboard shortcut validation  
function testShortcutSystem() {
    console.log('Shortcut data:', slmmGptPromptData);
    console.log('TinyMCE ready:', typeof tinyMCE !== 'undefined');
    console.log('Execute function:', typeof executePromptDirectly === 'function');
}
```

# 🔧 CURRENT FEATURES & INTEGRATION POINTS (v4.10.0)

## Advanced Search & Replace System
- **Location**: Database search and replace across WordPress tables
- **Security**: Nonce verification, capability checks, input sanitization
- **UI**: Professional table selection with checkboxes and visual feedback
- **Features**: Dry run preview, case insensitive search, whole words matching
- **Architecture**: AJAX-based with real-time progress indicators

## Lorem Ipsum Detector Tool  
- **Purpose**: Professional content scanning for placeholder text detection
- **Algorithms**: Configurable sensitivity levels and pattern matching
- **Results**: Comprehensive display with affected posts and action buttons
- **Integration**: Direct edit/view links for content remediation
- **Icon**: Professional search/document icon integration

## Development Mode System
- **Admin Bar**: Colorization system for environment identification  
- **Memory Bank**: Documentation system integration
- **Visual Indicators**: Enhanced developer experience
- **Debug Access**: Multiple authentication methods

## Notes System Integration
- **Storage**: wp_usermeta with wp_options backup
- **Display**: Admin bar integration with popup functionality
- **Persistence**: Data persistence across plugin updates
- **Bricks Integration**: Visual builder compatibility

## AI Provider Integration
- **OpenAI**: Full API integration with prompt execution
- **OpenRouter**: Alternative provider support
- **Anthropic**: Claude integration for content generation
- **Architecture**: Provider-agnostic prompt execution system

## Bricks Builder Integration
- **Detection**: Automatic detection via `?bricks=run` parameter
- **Assets**: Conditional asset loading for visual builder context
- **Toolbar**: Integration with Bricks Builder UI
- **Compatibility**: Full feature compatibility in visual builder mode

# ⚡ SUCCESS CRITERIA

**A successfully implemented feature MUST:**
1. **🎯 ACHIEVE 98% SOLUTION CERTAINTY** - Comprehensive research and validation before proposal (ABSOLUTE TOP PRIORITY)
2. **⚡ MAINTAIN 800-LINE FILE LIMIT** - ALL files under 800 lines, NO EXCEPTIONS  
3. **📋 CREATE SERENA PRD MEMORY** - Comprehensive task documentation before implementation
4. **🔗 INTEGRATE WITH PLUGIN.PHP** - Proper file registration and initialization order  
5. **🧠 USE SERENA MCP TOOLS EFFICIENTLY** - Start with symbolic analysis, avoid full file reads
6. **📚 FOLLOW ESTABLISHED PATTERNS** - Reuse existing architectural decisions from memory-bank/
7. **🔧 PRESERVE DUAL-SYSTEM ARCHITECTURE** - Never break keyboard shortcuts or button systems
8. **🔐 INCLUDE PROPER AUTHORIZATION** - Integration with visibility and security systems
9. **🎨 FOLLOW v4.10.0 STYLING STANDARDS** - 40px buttons, dark theme, professional checkboxes
10. **💾 UPDATE MEMORY BANK** - Document new patterns and architectural decisions
11. **🧪 MAINTAIN WORDPRESS CODING STANDARDS** - Nonces, capability checks, input sanitization

**🏆 IMPLEMENTATION QUALITY INDICATORS (MANDATORY):**
- **🎯 SOLUTION CERTAINTY ACHIEVED** - 98%+ confidence documented with comprehensive research evidence
- **🔍 THOROUGH SERENA RESEARCH** - Extensive use of find_symbol, search_for_pattern, get_symbols_overview
- **📊 INTEGRATION VALIDATION** - All dependencies verified, compatibility confirmed, edge cases addressed
- **🗂️ FILE SIZE COMPLIANCE** - All PHP files verified under 800 lines before commit
- **📋 PRD DOCUMENTATION** - Serena memory created with size estimates and implementation plan
- **🔗 PLUGIN.PHP INTEGRATION** - New files properly registered with dependency management
- **🧠 SYMBOLIC ANALYSIS FIRST** - Uses get_symbols_overview and find_symbol before file reads
- **💾 MEMORY BANK INTEGRATION** - Task documentation and progress tracking in serena
- **🔄 DUAL-SYSTEM COMPATIBILITY** - Both button and shortcut systems tested independently  
- **🎨 PROFESSIONAL UI CONSISTENCY** - Follows v4.10.0 styling and interaction standards
- **🔐 AUTHORIZATION INTEGRATION** - Respects visibility control and security protocols
- **⚙️ WORDPRESS INTEGRATION** - Proper hooks, AJAX patterns, and security implementations
- **📦 MINIMAL CODEBASE GROWTH** - Builds extensively on existing patterns rather than rewriting

**🚨 FAILURE CONDITIONS (IMMEDIATE REJECTION):**
- Solution proposed with <98% confidence level
- Inadequate serena research conducted (missing find_symbol, search_for_pattern analysis)
- Integration points not verified to exist and function
- Dependencies not confirmed to work as expected
- Edge cases and production scenarios not considered
- Any PHP file exceeds 800 lines
- No serena PRD memory created for complex tasks (3+ steps)
- New files not properly integrated into plugin.php
- Keyboard shortcut system functionality broken
- Dual-system architecture compromised
- Missing authorization system integration
- WordPress security standards violated

**Remember: 98% solution certainty + 800-line file limit + serena PRD creation + plugin.php integration + efficient serena usage + existing pattern reuse + dual-system preservation = successful implementation. NEVER propose solutions without absolute research confidence.**

# 🏗️ PROJECT COMMANDS & WORKFLOWS

## Development Commands
```bash
# PHP syntax validation
php -l filename.php

# WordPress coding standards (if PHPCS installed)
phpcs --standard=WordPress filename.php

# WordPress testing (browser-based)
# Navigate to WordPress admin dashboard
# Test both button system and keyboard shortcuts independently
```

## Debugging Workflow
1. **Check console** for slmmGptPromptData availability
2. **Verify authorization** using debug parameters if needed  
3. **Test dual systems** independently (buttons vs shortcuts)
4. **Review memory bank** for similar issues in issues/ folder
5. **Check asset loading** for Bricks Builder compatibility

## Version Information
- **Current Version**: 4.10.0 (extracted from plugin.php header)
- **Version Constant**: SLMM_SEO_VERSION (inherited by all components)  
- **Minimum WordPress**: 5.0
- **Minimum PHP**: 7.2
- **License**: Proprietary (Massive Organic)

## Key File Locations
- **Entry Point**: `/plugin.php` 
- **Main Logic**: `/slmm-seo-plugin.php`
- **Settings**: `/includes/settings/`
- **AI Integration**: `/includes/ai-integration/`
- **Frontend Assets**: `/assets/`
- **Memory Bank**: `/memory-bank/`
- **Documentation**: `/assets/docs/`

---

**FINAL REMINDER: 
🎯 ACHIEVE 98% SOLUTION CERTAINTY BEFORE PROPOSING ANYTHING - NO EXCEPTIONS!
🔍 RESEARCH EXTENSIVELY WITH SERENA TOOLS - NEVER GUESS OR ASSUME!
🗂️ KEEP ALL FILES UNDER 800 LINES - NO EXCEPTIONS!
📋 CREATE SERENA PRD MEMORIES FOR EVERY COMPLEX TASK!  
🔗 INTEGRATE NEW FILES PROPERLY IN PLUGIN.PHP!
🔧 This plugin's keyboard shortcut system is MISSION-CRITICAL - If it's working, don't touch it!
🧠 Use serena MCP tools efficiently with symbolic analysis first!
📚 Follow the dual-system architecture and build on existing patterns!
💾 Document everything in the memory bank for future sessions!
🔐 Respect the authorization system and security patterns
Never create fallback mechanisms.
Don't be lazy. Think abotu edge cases and plan for them.
NEVER BREAK existing functionality!

98% solution certainty + comprehensive serena research + file size compliance + PRD documentation + proper plugin integration = maintainable codebase success with ZERO failed implementations.**
## Sessions System Behaviors

@CLAUDE.sessions.md
