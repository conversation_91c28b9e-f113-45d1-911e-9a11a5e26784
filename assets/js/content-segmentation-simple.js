/**
 * SLMM Simple Content Segmentation
 * 
 * Simplified 3-click workflow for content segmentation
 * 
 * @package SLMM_SEO_Bundle
 * @version 1.0.0
 */

(function($) {
    'use strict';
    
    window.SLMMContentSegmentation = {
        
        currentStage: 0,
        clickHandler: null,
        currentEditor: null,
        overlaysVisible: false,
        overlayToggleButton: null,
        overlayUpdateTimer: null,
        // MEMORY LEAK PROTECTION
        timeouts: [], // Track all timeouts for cleanup
        isDestroyed: false, // Prevent operations after cleanup
        waitingForTinyMCE: false, // Prevent recursive waitForTinyMCE calls
        
        /**
         * Initialize the segmentation system
         */
        init: function() {
            var self = this;
            
            // Wait for Direct Editor to be ready
            $(document).on('slmm:direct_editor:ready', function() {
                self.setupSegmentationButtons();
            });
            
            // Also try to set up immediately if Direct Editor is already loaded
            if (window.SLMMDirectEditor && window.SLMMDirectEditor.isInitialized) {
                self.setupSegmentationButtons();
            }
            
            // Listen for TinyMCE editor changes to maintain overlays
            $(document).on('tinymce:editor-ready', function(e, editor) {
                console.log('SLMM: TinyMCE editor ready - checking overlays');

                // CRITICAL: Re-apply overlays only if user had them enabled
                if (self.overlaysVisible) {
                    setTimeout(function() {
                        console.log('SLMM: Re-applying user-enabled overlays to fresh TinyMCE editor');
                        self.updateOverlays(editor);
                    }, 200);
                }

                // Listen for content changes
                editor.on('change keyup', function() {
                    if (self.overlaysVisible) {
                        clearTimeout(self.overlayUpdateTimer);
                        self.overlayUpdateTimer = setTimeout(function() {
                            self.updateOverlays(editor);
                        }, 500);
                    }
                });
            });

            // CRITICAL: Also listen for WordPress TinyMCE init events
            if (typeof tinymce !== 'undefined') {
                tinymce.on('AddEditor', function(e) {
                    var editor = e.editor;
                    editor.on('init', function() {
                        console.log('SLMM: TinyMCE editor initialized - checking overlays');

                        // Only re-apply overlays if they were already enabled by user
                        setTimeout(function() {
                            if (self.overlaysVisible) {
                                console.log('SLMM: Re-applying user-enabled overlays to initialized editor');
                                self.updateOverlays(editor);
                            }
                        }, 300);
                    });
                });
            }
        },
        
        /**
         * Setup segmentation button handlers
         */
        setupSegmentationButtons: function() {
            var self = this;
            
            // Override the segment insert button handler
            $(document).off('click', '#slmm-segment-insert-btn').on('click', '#slmm-segment-insert-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.startSegmentation();
            });
            
            // Override the clear button handler  
            $(document).off('click', '#slmm-segment-clear-btn').on('click', '#slmm-segment-clear-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.clearSegmentation();
            });
            
            // Add overlay toggle button
            self.addOverlayToggleButton();
        },
        
        /**
         * Add overlay toggle button to Content Segmentation section
         */
        addOverlayToggleButton: function() {
            var self = this;

            // Simply bind to the existing button (it's now part of Direct Editor HTML template)
            this.bindOverlayToggleButton();

            // CRITICAL: Re-bind EVERY time Direct Editor modal opens
            $(document).on('slmm:direct_editor:modal_opened', function(e, data) {
                console.log('SLMM: Direct Editor modal opened - re-binding overlay toggle');
                setTimeout(function() {
                    self.bindOverlayToggleButton();
                }, 50);
            });

            // CRITICAL: Re-bind when Direct Editor is fully ready
            $(document).on('slmm:direct_editor:ready', function(e, data) {
                console.log('SLMM: Direct Editor ready - re-binding overlay toggle');
                setTimeout(function() {
                    self.bindOverlayToggleButton();

                    // Only ensure script is ready for user interaction - no auto-enable
                }, 100);
            });
        },
        
        /**
         * Bind to the existing overlay toggle button (now part of Direct Editor HTML template)
         */
        bindOverlayToggleButton: function() {
            var self = this;

            // Find the existing button in the current Direct Editor modal
            var $toggleBtn = $('.slmm-direct-editor-modal:visible .slmm-overlay-toggle');

            // Fallback: find anywhere in the page
            if ($toggleBtn.length === 0) {
                $toggleBtn = $('.slmm-overlay-toggle');
            }

            if ($toggleBtn.length === 0) {
                console.log('SLMM: Overlay toggle button not found in DOM yet');
                return;
            }

            console.log('SLMM: Binding to existing overlay toggle button (found: ' + $toggleBtn.length + ')');

            // ALWAYS remove existing handlers to prevent duplicates
            $toggleBtn.off('click.slmm-overlay mouseenter.slmm-overlay mouseleave.slmm-overlay');

            // Add hover effects
            $toggleBtn.on('mouseenter.slmm-overlay', function() {
                $(this).css({
                    'color': '#f0f0f1',
                    'background-color': 'rgba(255,255,255,0.1)'
                });
            }).on('mouseleave.slmm-overlay', function() {
                var isActive = self.overlaysVisible;
                $(this).css({
                    'color': isActive ? '#10b981' : '#ffffff',
                    'background-color': isActive ? 'rgba(16, 185, 129, 0.2)' : 'rgba(59, 130, 246, 0.6)'
                });
            });

            // Add click handler
            $toggleBtn.on('click.slmm-overlay', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('SLMM: Overlay toggle button clicked');
                self.toggleOverlays();
            });

            // Store reference (don't use data binding flag anymore)
            self.overlayToggleButton = $toggleBtn;
        },
        
        /**
         * Toggle overlay visibility - with TinyMCE ready check
         */
        toggleOverlays: function() {
            var self = this;

            // CRITICAL: Wait for TinyMCE to be ready
            this.waitForTinyMCE(function(editor) {
                self.overlaysVisible = !self.overlaysVisible;

                if (self.overlaysVisible) {
                    self.updateOverlays(editor);
                    self.updateToggleButtonState(true);
                    self.showStatus('Content overlays enabled', 'success');
                } else {
                    self.removeOverlays(editor);
                    self.updateToggleButtonState(false);
                    self.showStatus('Content overlays disabled', 'success');
                }
            });
        },

        /**
         * MEMORY EFFICIENT: Wait for TinyMCE with recursion protection
         */
        waitForTinyMCE: function(callback, attempts, maxAttempts) {
            var self = this;

            // PREVENT RECURSIVE CALLS - major memory leak fix
            if (this.waitingForTinyMCE) {
                console.log('SLMM: Already waiting for TinyMCE, skipping duplicate call');
                return;
            }

            attempts = attempts || 0;
            maxAttempts = maxAttempts || 12; // Reduced from 20 to 12 (3 seconds)

            var editor = this.getActiveEditor();

            if (editor && editor.initialized && editor.getBody) {
                console.log('SLMM: TinyMCE ready, executing callback');
                this.waitingForTinyMCE = false;
                callback(editor);
                return;
            }

            if (attempts >= maxAttempts || this.isDestroyed) {
                console.log('SLMM: TinyMCE wait timeout or destroyed');
                this.waitingForTinyMCE = false;

                var fallbackEditor = this.getActiveEditor();
                if (fallbackEditor && !this.isDestroyed) {
                    callback(fallbackEditor);
                } else {
                    this.showStatus('Editor not ready, please try again', 'warning');
                }
                return;
            }

            this.waitingForTinyMCE = true;

            // Use tracked timeout with increased interval
            var timeoutId = setTimeout(function() {
                self.waitingForTinyMCE = false;
                if (!self.isDestroyed) {
                    self.waitForTinyMCE(callback, attempts + 1, maxAttempts);
                }
            }, 250);

            this.timeouts.push(timeoutId);
        },

        /**
         * Update toggle button visual state
         */
        updateToggleButtonState: function(isActive) {
            if (!this.overlayToggleButton) return;
            
            this.overlayToggleButton.css({
                'color': isActive ? '#10b981' : '#999'
            });
            
            this.overlayToggleButton.attr('title', isActive ? 'Hide Content Overlays' : 'Show Content Overlays');
        },
        
        /**
         * Start the segmentation workflow
         */
        startSegmentation: function() {
            // Get active TinyMCE editor
            var editor = this.getActiveEditor();
            if (!editor) {
                this.showStatus('No active editor found', 'error');
                return;
            }
            
            this.currentEditor = editor;
            var content = editor.getContent();
            
            // Check current state
            var markers = this.countMarkers(content);
            
            if (markers === 0) {
                // Stage 1: Insert boundaries
                this.insertBoundaries();
            } else if (markers === 2) {
                // Stage 2: Setup first click
                this.setupContentClick(1);
            } else if (markers === 4) {
                // Stage 3: Setup second click
                this.setupContentClick(2);
            } else if (markers === 6) {
                this.showStatus('Segmentation already complete', 'success');
            } else {
                this.showStatus('Invalid state - please clear and restart', 'error');
            }
        },
        
        /**
         * Insert boundary markers (TOP_START and BOTTOM_END)
         */
        insertBoundaries: function() {
            var self = this;
            var editor = this.currentEditor;
            var content = editor.getContent();
            
            // Add markers at beginning and end using direct content manipulation
            // This avoids TinyMCE's content protection mechanism
            var topMarker = '<!-- SLMM_SEGMENT_TOP_START -->';
            var bottomMarker = '<!-- SLMM_SEGMENT_BOTTOM_END -->';
            var newContent = topMarker + '\n' + content + '\n' + bottomMarker;
            
            // Use setContent to avoid TinyMCE protection
            editor.setContent(newContent);
            
            if (this.overlaysVisible) {
                this.updateOverlays(editor);
            }
            
            this.showStatus('Boundaries added. Click to place first divider.', 'success');
            this.currentStage = 1;
            
            // Auto-trigger the next stage immediately
            setTimeout(function() {
                self.setupContentClick(1);
            }, 100);
        },
        
        /**
         * Setup content click handler
         */
        setupContentClick: function(clickNum) {
            var self = this;
            var editor = this.currentEditor;
            
            // Remove any existing handler
            if (this.clickHandler) {
                editor.off('click', this.clickHandler);
            }
            
            // Visual feedback
            $(editor.getBody()).css('cursor', 'crosshair');
            
            if (clickNum === 1) {
                this.showStatus('Click where you want to end the TOP section', 'info');
            } else {
                this.showStatus('Click where you want to end the MIDDLE section', 'info');
            }
            
            // Create click handler that uses TinyMCE's selection at click point
            this.clickHandler = function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Reset cursor
                $(editor.getBody()).css('cursor', '');
                
                // Remove handler
                editor.off('click', self.clickHandler);
                
                // Insert markers at the current selection/cursor position
                // TinyMCE will have the cursor at the click location
                var markers;
                if (clickNum === 1) {
                    markers = '<!-- SLMM_SEGMENT_TOP_END --><!-- SLMM_SEGMENT_MIDDLE_START -->';
                    self.showStatus('First divider placed. Click to place second divider.', 'success');
                    self.currentStage = 2;
                } else {
                    markers = '<!-- SLMM_SEGMENT_MIDDLE_END --><!-- SLMM_SEGMENT_BOTTOM_START -->';
                    self.showStatus('Segmentation complete!', 'success');
                    self.currentStage = 3;
                }
                
                // Use TinyMCE's execCommand to insert at cursor position
                editor.execCommand('mceInsertRawHTML', false, markers);
                
                // Update overlays if enabled
                if (self.overlaysVisible) {
                    self.updateOverlays(editor);
                }
                
                // Continue to next stage if needed
                if (clickNum === 1) {
                    setTimeout(function() {
                        self.setupContentClick(2);
                    }, 100);
                }
            };
            
            // Attach handler
            editor.on('click', this.clickHandler);
        },
        
        /**
         * Clear all segmentation markers
         */
        clearSegmentation: function() {
            if (!confirm('Remove all segmentation markers?')) {
                return;
            }
            
            var editor = this.getActiveEditor();
            if (!editor) {
                this.showStatus('No active editor found', 'error');
                return;
            }
            
            var content = editor.getContent();
            
            // Remove all markers with comprehensive cleanup
            content = content.replace(/<!-- SLMM_SEGMENT_[A-Z]+_[A-Z]+ -->/g, '');
            
            // Remove TinyMCE protected content wrappers (fix for mce:protected issue)
            content = content.replace(/<!--mce:protected[^>]*%3C%21--%20SLMM_SEGMENT_[A-Z]+_[A-Z]+%20--%3E[^>]*-->/g, '');
            
            // Remove malformed markers that got mangled by TinyMCE (like those surrounded by &nbsp;)
            content = content.replace(/&nbsp;\s*<!-- SLMM_SEGMENT_[A-Z]+_[A-Z]+ -->\s*&nbsp;/g, '');
            content = content.replace(/&nbsp;\s*<!-- SLMM_SEGMENT_[A-Z]+_[A-Z]+ -->/g, '');
            content = content.replace(/<!-- SLMM_SEGMENT_[A-Z]+_[A-Z]+ -->\s*&nbsp;/g, '');
            
            // Clean up extra whitespace and line breaks left by marker removal
            content = content.replace(/\n\s*\n\s*\n/g, '\n\n'); // Replace multiple line breaks with double
            content = content.replace(/^\s+|\s+$/g, ''); // Trim start and end
            
            // Clean up extra &nbsp; entities that might be left behind
            content = content.replace(/&nbsp;\s*&nbsp;/g, '&nbsp;'); // Replace multiple &nbsp; with single
            content = content.replace(/&nbsp;\s*$/gm, ''); // Remove trailing &nbsp; at end of lines
            content = content.replace(/^\s*&nbsp;/gm, ''); // Remove leading &nbsp; at start of lines
            
            // Clean up empty paragraphs that might contain only &nbsp;
            content = content.replace(/<p>\s*&nbsp;\s*<\/p>/g, '');
            content = content.replace(/<p>\s*<\/p>/g, '');
            
            // Final cleanup of excessive whitespace
            content = content.replace(/\n{3,}/g, '\n\n'); // Limit to max 2 consecutive line breaks
            
            editor.setContent(content);
            this.removeOverlays(editor);
            
            this.currentStage = 0;
            this.showStatus('All markers cleared', 'success');
        },
        
        /**
         * Count markers in content
         */
        countMarkers: function(content) {
            var matches = content.match(/<!-- SLMM_SEGMENT_[A-Z]+_[A-Z]+ -->/g);
            return matches ? matches.length : 0;
        },
        
        /**
         * Get active TinyMCE editor
         */
        getActiveEditor: function() {
            // Look for editor in Direct Editor modal
            var $modal = $('.slmm-direct-editor-modal:visible');
            if ($modal.length === 0) {
                return null;
            }
            
            var $textarea = $modal.find('textarea[id*="editor"]');
            if ($textarea.length === 0) {
                $textarea = $modal.find('textarea').first();
            }
            
            if ($textarea.length > 0) {
                var editorId = $textarea.attr('id');
                return tinymce.get(editorId);
            }
            
            return null;
        },
        
        /**
         * Update visual overlays
         */
        updateOverlays: function(editor) {
            if (!this.overlaysVisible || !editor) return;
            
            var content = editor.getContent();
            var $body = $(editor.getBody());
            
            // Remove existing overlays from body
            $body.find('.slmm-section-overlay').remove();
            
            // Check which sections are complete
            var hasTopSection = content.indexOf('<!-- SLMM_SEGMENT_TOP_START -->') !== -1 && 
                               content.indexOf('<!-- SLMM_SEGMENT_TOP_END -->') !== -1;
            var hasMiddleSection = content.indexOf('<!-- SLMM_SEGMENT_MIDDLE_START -->') !== -1 && 
                                  content.indexOf('<!-- SLMM_SEGMENT_MIDDLE_END -->') !== -1;
            
            // Apply CSS classes to editor body for styling
            $body.removeClass('slmm-has-top-section slmm-has-middle-section slmm-has-bottom-section');
            
            if (hasTopSection) $body.addClass('slmm-has-top-section');
            if (hasMiddleSection) $body.addClass('slmm-has-middle-section');
            
            // Create direct overlay elements in the body
            this.createDirectBodyOverlays($body, hasTopSection, hasMiddleSection);
            
            // Inject styles if not already present
            this.injectEditorStyles(editor);
        },
        
        /**
         * Create direct body overlays with content-based positioning
         */
        createDirectBodyOverlays: function($body, hasTopSection, hasMiddleSection) {
            if (!$body || $body.length === 0) return;
            
            var editor = this.getActiveEditor();
            if (!editor) return;
            
            // Make body position relative for absolute positioning
            $body.css('position', 'relative');
            
            // Get raw content and calculate section percentages
            var content = editor.getContent();
            var sectionData = this.calculateSectionPositions(content);
            
            // Create TOP section overlay 
            if (hasTopSection && sectionData.topSection) {
                this.createPercentageBasedOverlay($body, sectionData.topSection, 'top');
            }
            
            // Create MIDDLE section overlay
            if (hasMiddleSection && sectionData.middleSection) {
                this.createPercentageBasedOverlay($body, sectionData.middleSection, 'middle');
            }
        },
        
        /**
         * Calculate section positions based on content analysis
         */
        calculateSectionPositions: function(content) {
            var sections = {};
            
            // Find marker positions in the HTML content
            var topStart = content.indexOf('<!-- SLMM_SEGMENT_TOP_START -->');
            var topEnd = content.indexOf('<!-- SLMM_SEGMENT_TOP_END -->');
            var middleStart = content.indexOf('<!-- SLMM_SEGMENT_MIDDLE_START -->');
            var middleEnd = content.indexOf('<!-- SLMM_SEGMENT_MIDDLE_END -->');
            var bottomStart = content.indexOf('<!-- SLMM_SEGMENT_BOTTOM_START -->');
            var bottomEnd = content.indexOf('<!-- SLMM_SEGMENT_BOTTOM_END -->');
            
            // Calculate content length without HTML tags for better positioning
            var cleanContent = content.replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, ' ');
            var totalLength = cleanContent.length;
            
            // Calculate TOP section
            if (topStart !== -1 && topEnd !== -1) {
                var topStartClean = content.substring(0, topStart).replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, ' ').length;
                var topEndClean = content.substring(0, topEnd).replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, ' ').length;
                
                sections.topSection = {
                    startPercent: (topStartClean / totalLength) * 100,
                    endPercent: (topEndClean / totalLength) * 100
                };
            }
            
            // Calculate MIDDLE section
            if (middleStart !== -1 && middleEnd !== -1) {
                var middleStartClean = content.substring(0, middleStart).replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, ' ').length;
                var middleEndClean = content.substring(0, middleEnd).replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, ' ').length;
                
                sections.middleSection = {
                    startPercent: (middleStartClean / totalLength) * 100,
                    endPercent: (middleEndClean / totalLength) * 100
                };
            }
            
            return sections;
        },
        
        /**
         * Create overlay based on calculated percentages
         */
        createPercentageBasedOverlay: function($body, sectionData, sectionType) {
            if (!sectionData || !$body) return;
            
            var bodyHeight = $body.height();
            var overlayTop = (bodyHeight * sectionData.startPercent / 100);
            var overlayHeight = (bodyHeight * (sectionData.endPercent - sectionData.startPercent) / 100);
            
            // Ensure minimum height
            overlayHeight = Math.max(overlayHeight, 30);
            
            // Create overlay element
            var $overlay = $('<div class="slmm-section-overlay slmm-section-' + sectionType + '"></div>');
            
            // Set colors based on section type - 20% opacity for visibility
            var backgroundColor, borderColor;
            if (sectionType === 'top') {
                backgroundColor = 'rgba(255, 255, 0, 0.2)';
                borderColor = 'rgba(245, 158, 11, 0.6)';
            } else if (sectionType === 'middle') {
                backgroundColor = 'rgba(255, 0, 0, 0.2)';
                borderColor = 'rgba(239, 68, 68, 0.6)';
            }
            
            $overlay.css({
                position: 'absolute',
                top: overlayTop + 'px',
                left: '0px',
                width: '100%',
                height: overlayHeight + 'px',
                backgroundColor: 'transparent',
                border: '3px solid ' + borderColor,
                borderRadius: '2px',
                pointerEvents: 'none',
                zIndex: '10'
            });
            
            // No labels - removed for clean appearance
            $body.append($overlay);
        },
        
        /**
         * Inject segmentation styles into editor
         */
        injectEditorStyles: function(editor) {
            var doc = editor.getDoc();
            if (!doc) return;
            
            var $head = $(doc.head);
            if ($head.find('#slmm-segmentation-styles').length === 0) {
                var styles = `
                    <style id="slmm-segmentation-styles">
                        /* Marker visibility */
                        body .mce-content-body {
                            position: relative;
                        }
                        
                        /* Visual indicators for markers */
                        body:not(.slmm-has-top-section):not(.slmm-has-middle-section):not(.slmm-has-bottom-section) {
                            background: rgba(255, 255, 255, 0.02);
                        }
                        
                        /* No corner labels - clean overlay appearance */
                    </style>
                `;
                $head.append(styles);
            }
        },
        
        /**
         * Remove overlays
         */
        removeOverlays: function(editor) {
            if (!editor) return;
            
            var $body = $(editor.getBody());
            
            // Remove CSS classes
            $body.removeClass('slmm-has-top-section slmm-has-middle-section slmm-has-bottom-section');
            
            // Remove positioned overlays from body
            $body.find('.slmm-section-overlay').remove();
            
            // Reset body position if no overlays remain
            if ($body.find('.slmm-section-overlay').length === 0) {
                $body.css('position', '');
            }
        },
        
        /**
         * Smart boundary detection - Prioritizes paragraph gaps over percentage accuracy
         */
        findSmartBoundary: function(content, targetPosition) {
            // Phase 1: Look for paragraph gaps within reasonable distance (up to 20%)
            var maxRadius = Math.floor(content.length * 0.20); // 20% max search
            var paragraphGap = this.findNearestParagraphGap(content, targetPosition, maxRadius);
            if (paragraphGap !== -1) {
                console.log('Found paragraph gap at:', paragraphGap, 'distance:', Math.abs(paragraphGap - targetPosition));
                return paragraphGap;
            }
            
            // Phase 2: If no paragraph gaps, look for heading boundaries
            var headingBoundary = this.findNearestHeadingBoundary(content, targetPosition, maxRadius);
            if (headingBoundary !== -1) return headingBoundary;
            
            // Phase 3: Sentence endings as last resort
            var sentenceEnd = this.findNearestSentenceEnd(content, targetPosition, maxRadius);
            if (sentenceEnd !== -1) return sentenceEnd;
            
            // This should rarely happen - content has no structure
            console.warn('No safe boundary found, using exact position');
            return targetPosition;
        },
        
        /**
         * Find nearest paragraph gap (</p> followed by <p>)
         */
        findNearestParagraphGap: function(content, targetPosition, maxRadius) {
            // Look for </p> followed by whitespace and <p> (paragraph gap)
            var gapPattern = /<\/p>\s*<p[^>]*>/gi;
            var match;
            var bestGap = -1;
            var minDistance = Infinity;
            
            while ((match = gapPattern.exec(content)) !== null) {
                var gapPosition = match.index + match[0].indexOf('>') + 1; // After </p>
                var distance = Math.abs(gapPosition - targetPosition);
                
                if (distance <= maxRadius && distance < minDistance) {
                    minDistance = distance;
                    bestGap = gapPosition;
                }
            }
            
            return bestGap;
        },
        
        /**
         * Find nearest heading boundary
         */
        findNearestHeadingBoundary: function(content, targetPosition, maxRadius) {
            var headingPattern = /<\/h[1-6]>/gi;
            var match;
            var bestBoundary = -1;
            var minDistance = Infinity;
            
            while ((match = headingPattern.exec(content)) !== null) {
                var boundaryPosition = match.index + match[0].length;
                var distance = Math.abs(boundaryPosition - targetPosition);
                
                if (distance <= maxRadius && distance < minDistance) {
                    minDistance = distance;
                    bestBoundary = boundaryPosition;
                }
            }
            
            return bestBoundary;
        },
        
        /**
         * Find nearest sentence ending
         */
        findNearestSentenceEnd: function(content, targetPosition, maxRadius) {
            var sentencePattern = /[.!?]\s+/g;
            var match;
            var bestEnd = -1;
            var minDistance = Infinity;
            
            while ((match = sentencePattern.exec(content)) !== null) {
                var endPosition = match.index + match[0].length;
                var distance = Math.abs(endPosition - targetPosition);
                
                if (distance <= maxRadius && distance < minDistance) {
                    minDistance = distance;
                    bestEnd = endPosition;
                }
            }
            
            return bestEnd;
        },
        
        /**
         * Analyze content structure for optimal segmentation
         */
        analyzeContentStructure: function(content) {
            var structure = {
                headings: [],
                paragraphs: [],
                lists: [],
                sections: [],
                wordCount: 0,
                readingTime: 0
            };
            
            // Extract headings with positions
            var headingMatches = content.matchAll(/<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gi);
            for (let match of headingMatches) {
                structure.headings.push({
                    level: parseInt(match[1]),
                    text: match[2],
                    position: match.index,
                    length: match[0].length
                });
            }
            
            // Analyze paragraph distribution
            var paragraphMatches = content.matchAll(/<p[^>]*>(.*?)<\/p>/gi);
            for (let match of paragraphMatches) {
                structure.paragraphs.push({
                    text: match[1],
                    position: match.index,
                    length: match[0].length,
                    wordCount: match[1].split(' ').length
                });
            }
            
            return structure;
        },
        
        /**
         * Auto-segment content by percentage with smart boundary detection
         */
        autoSegmentByPercentage: function(content, topPercent, bottomPercent) {
            if (!content) return null;
            
            var totalLength = content.length;
            
            // Calculate target positions
            var targetTop = Math.floor(totalLength * (topPercent / 100));
            var targetBottom = Math.floor(totalLength * (bottomPercent / 100));
            
            // Find smart boundaries
            var smartTop = this.findSmartBoundary(content, targetTop);
            var smartBottom = this.findSmartBoundary(content, targetBottom);
            
            return {
                topBoundary: smartTop,
                bottomBoundary: smartBottom,
                analysis: {
                    originalTargets: { top: targetTop, bottom: targetBottom },
                    adjustments: { 
                        topAdjustment: smartTop - targetTop,
                        bottomAdjustment: smartBottom - targetBottom 
                    },
                    contentLength: totalLength
                }
            };
        },
        
        /**
         * MEMORY LEAK PROTECTION: Clear all tracked timeouts
         */
        clearTimeouts: function() {
            this.timeouts.forEach(function(timeoutId) {
                clearTimeout(timeoutId);
            });
            this.timeouts = [];
        },

        /**
         * MEMORY LEAK PROTECTION: Destroy instance and cleanup resources
         */
        destroy: function() {
            this.isDestroyed = true;
            this.clearTimeouts();

            // Clear overlay timer
            if (this.overlayUpdateTimer) {
                clearTimeout(this.overlayUpdateTimer);
                this.overlayUpdateTimer = null;
            }

            // Remove all event handlers
            $(document).off('.slmm-segmentation');

            // Clear editor references
            this.currentEditor = null;
            this.overlayToggleButton = null;

            console.log('SLMM: ContentSegmentation instance destroyed and cleaned up');
        },

        /**
         * Show status message
         */
        showStatus: function(message, type) {
            // Try to use Direct Editor's status system if available
            if (window.SLMMDirectEditor && window.SLMMDirectEditor.showSegmentationStatus) {
                window.SLMMDirectEditor.showSegmentationStatus(message, type);
            } else {
                // Fallback to console
                console.log('[SLMM Segmentation] ' + type.toUpperCase() + ': ' + message);
            }
        }
    };
    
    // Initialize when DOM is ready
    $(document).ready(function() {
        SLMMContentSegmentation.init();

        // MEMORY EFFICIENT: Single tracked timeout for initial binding
        var initialTimeoutId = setTimeout(function() {
            if (!SLMMContentSegmentation.isDestroyed) {
                SLMMContentSegmentation.bindOverlayToggleButton();
            }
        }, 800); // Increased delay to reduce frequency

        SLMMContentSegmentation.timeouts.push(initialTimeoutId);

        // MEMORY EFFICIENT: Single timeout for Direct Editor modal binding with namespaced events
        $(document).on('click.slmm-segmentation', '[data-slmm-direct-editor], .slmm-direct-editor-trigger, .direct-editor-btn', function() {
            // Clear any existing timeout to prevent accumulation
            SLMMContentSegmentation.clearTimeouts();

            // Single timeout with longer delay instead of multiple attempts
            var timeoutId = setTimeout(function() {
                if (!SLMMContentSegmentation.isDestroyed) {
                    SLMMContentSegmentation.bindOverlayToggleButton();
                }
            }, 1500);

            SLMMContentSegmentation.timeouts.push(timeoutId);
        });
    });

    // MEMORY LEAK PROTECTION: Automatic cleanup on page unload
    $(window).on('beforeunload.slmm-segmentation', function() {
        SLMMContentSegmentation.destroy();
    });

    // Additional cleanup for navigation changes (SPA-like behavior)
    $(window).on('pagehide.slmm-segmentation', function() {
        SLMMContentSegmentation.destroy();
    });

    // CRITICAL FIX: Force cleanup every 60 seconds to prevent timeout accumulation
    setInterval(function() {
        if (SLMMContentSegmentation.timeouts.length > 5) {
            console.warn('SLMM: Too many active timeouts detected (' + SLMMContentSegmentation.timeouts.length + '), forcing cleanup');
            SLMMContentSegmentation.clearTimeouts();
        }
    }, 60000);

    // MEMORY MONITORING: Log memory usage periodically in debug mode
    if (window.console && typeof performance !== 'undefined' && performance.memory) {
        setInterval(function() {
            var memory = performance.memory;
            var usedMB = Math.round(memory.usedJSHeapSize / 1048576);

            // Warn if memory usage is excessive (over 500MB)
            if (usedMB > 500) {
                console.warn('SLMM: High memory usage detected:', usedMB + 'MB');
                console.log('SLMM: Active timeouts:', SLMMContentSegmentation.timeouts.length);
            }
        }, 30000); // Check every 30 seconds
    }

    // Global debug function (can be called from console)
    window.addSLMMToggleButton = function() {
        SLMMContentSegmentation.bindOverlayToggleButton();
    };

    // Global debug function to check memory status
    window.slmmMemoryStatus = function() {
        if (performance.memory) {
            var memory = performance.memory;
            console.log('SLMM Memory Status:');
            console.log('Used:', Math.round(memory.usedJSHeapSize / 1048576) + 'MB');
            console.log('Total:', Math.round(memory.totalJSHeapSize / 1048576) + 'MB');
            console.log('Limit:', Math.round(memory.jsHeapSizeLimit / 1048576) + 'MB');
            console.log('Active timeouts:', SLMMContentSegmentation.timeouts.length);
            console.log('Is destroyed:', SLMMContentSegmentation.isDestroyed);
        }
    };

})(jQuery);