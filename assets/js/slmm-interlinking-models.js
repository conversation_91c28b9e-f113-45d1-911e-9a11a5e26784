/**
 * SLMM Interlinking Models Handler
 * Handles model loading for interlinking prompts and page summarization
 */
(function($) {
    'use strict';

    // Cache for models to avoid redundant API calls
    let modelsCache = {
        openai: null,
        openrouter: null
    };

    /**
     * Refresh models for a specific prompt type
     */
    function refreshInterlinkingModels(type, provider) {
        const $button = $(`.slmm-refresh-interlinking-models[data-type="${type}"]`);
        const $modelSelect = $(`.slmm-interlinking-prompt[data-type="${type}"] .slmm-gpt-prompt-model`);
        
        // Show loading state
        $button.prop('disabled', true).addClass('loading');
        $modelSelect.prop('disabled', true);
        
        // Make AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'slmm_refresh_models',
                provider: provider,
                field: 'interlinking_' + type,
                nonce: slmmInterlinkingData.nonce || ''
            },
            success: function(response) {
                if (response.success && response.data.models) {
                    // Cache the models
                    modelsCache[provider] = response.data.models;
                    
                    // Update the dropdown
                    updateModelDropdown(type, response.data.models);
                    
                    // Show success message
                    showNotification('Models refreshed successfully', 'success');
                } else {
                    console.error('Failed to refresh models:', response);
                    showNotification('Failed to refresh models. Please check your API key.', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                showNotification('Network error while refreshing models', 'error');
            },
            complete: function() {
                // Remove loading state
                $button.prop('disabled', false).removeClass('loading');
                $modelSelect.prop('disabled', false);
            }
        });
    }

    /**
     * Update model dropdown with new models
     */
    function updateModelDropdown(type, models) {
        const $modelSelect = $(`.slmm-interlinking-prompt[data-type="${type}"] .slmm-gpt-prompt-model`);
        const currentValue = $modelSelect.val();
        
        // Clear existing options
        $modelSelect.empty();
        
        // Add new options
        $.each(models, function(modelId, modelName) {
            $modelSelect.append(
                $('<option>').val(modelId).text(modelName)
            );
        });
        
        // Try to restore previous selection
        if (currentValue && $modelSelect.find(`option[value="${currentValue}"]`).length) {
            $modelSelect.val(currentValue);
        } else {
            // Select first option if previous selection not available
            $modelSelect.prop('selectedIndex', 0);
        }
    }

    /**
     * Handle provider change
     */
    function handleProviderChange(type, provider) {
        console.log('Provider changed to:', provider, 'for type:', type);
        
        // Check if we have cached models for this provider
        if (modelsCache[provider]) {
            updateModelDropdown(type, modelsCache[provider]);
        } else {
            // Fetch models for the new provider
            refreshInterlinkingModels(type, provider);
        }
    }

    /**
     * Show notification message
     */
    function showNotification(message, type) {
        // Create notification element
        const $notification = $('<div>')
            .addClass('slmm-notification')
            .addClass('slmm-notification-' + type)
            .text(message)
            .css({
                position: 'fixed',
                top: '32px',
                right: '20px',
                zIndex: 9999,
                padding: '12px 20px',
                borderRadius: '4px',
                fontSize: '14px',
                fontWeight: '500',
                maxWidth: '400px',
                backgroundColor: type === 'success' ? '#46a049' : '#dc3545',
                color: '#fff',
                boxShadow: '0 2px 5px rgba(0,0,0,0.2)'
            });
        
        // Add to body
        $('body').append($notification);
        
        // Auto-hide after 3 seconds
        setTimeout(function() {
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

    /**
     * Initialize on document ready
     */
    $(document).ready(function() {
        // Handle provider changes for interlinking prompts
        $(document).on('change', '.slmm-interlinking-provider', function() {
            const type = $(this).data('type');
            const provider = $(this).val();
            handleProviderChange(type, provider);
        });
        
        // Handle refresh button clicks
        $(document).on('click', '.slmm-refresh-interlinking-models', function(e) {
            e.preventDefault();
            const type = $(this).data('type');
            const $providerSelect = $(`.slmm-interlinking-provider[data-type="${type}"]`);
            const provider = $providerSelect.val() || 'openai';
            refreshInterlinkingModels(type, provider);
        });
        
        // Add spinner animation CSS
        if (!$('#slmm-interlinking-spinner-css').length) {
            $('<style>')
                .attr('id', 'slmm-interlinking-spinner-css')
                .text(`
                    @keyframes slmm-spin {
                        from { transform: rotate(0deg); }
                        to { transform: rotate(360deg); }
                    }
                    .slmm-refresh-interlinking-models.loading {
                        animation: slmm-spin 1s linear infinite;
                    }
                    .slmm-interlinking-prompt .slmm-model-selector {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }
                `)
                .appendTo('head');
        }
    });

})(jQuery);