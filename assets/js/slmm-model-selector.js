/**
 * SLMM Model Selector JavaScript
 * Handles model search, provider switching, and dynamic model loading
 */
(function($) {
    'use strict';

    // Store original model lists for each provider
    let modelData = {};
    
    // Dark Theme Notification system for fallback alerts
    function showNotification(message, type = 'info') {
        const notificationId = 'slmm-model-notification';
        
        // Remove any existing notification
        $('#' + notificationId).remove();
        
        const colorMap = {
            'warning': '#1a202c',
            'error': '#1a1a1a',
            'info': '#1a202c',
            'success': '#1a202c'
        };
        
        const borderMap = {
            'warning': '#4a5568',
            'error': '#4a5568',
            'info': '#4a5568',
            'success': '#2d3748'
        };
        
        const textMap = {
            'warning': '#ffffff',
            'error': '#f7fafc',
            'info': '#ffffff',
            'success': '#e2e8f0'
        };
        
        const notification = $('<div id="' + notificationId + '" style="position: fixed; top: 32px; right: 20px; z-index: 9999; background: ' + colorMap[type] + '; color: ' + textMap[type] + '; border: 1px solid ' + borderMap[type] + '; padding: 12px 20px; border-radius: 8px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3); font-size: 14px; font-weight: 500; max-width: 400px; animation: slideIn 0.3s ease-out;">' + message + '</div>');
        
        $('body').append(notification);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            notification.fadeOut(300, () => notification.remove());
        }, 5000);
        
        // Add click to dismiss
        notification.on('click', () => {
            notification.fadeOut(300, () => notification.remove());
        });
    }
    
    // Initialize model selector functionality
    function initModelSelectors() {
        // Handle provider selection changes
        $('.provider-select').on('change', function() {
            const field = $(this).data('field');
            const provider = $(this).val();
            
            if (!provider) {
                showNotification('Please select a valid AI provider', 'warning');
                return;
            }
            
            switchProvider(field, provider);
        });

        // Handle model selection changes - auto-save like content-helper
        $('.model-select').on('change', function() {
            const field = $(this).data('field');
            const selectedModel = $(this).val();
            const selectedText = $(this).find('option:selected').text();
            
            if (!selectedModel) {
                showNotification('Please select a valid model', 'warning');
                return;
            }
            
            // Auto-save the selection (trigger form save)
            saveModelSelection(field, selectedModel);
            
            // Update selection display
            updateSelectionDisplay(field, selectedText);
        });

        // Handle refresh model buttons
        $('.refresh-models-btn').on('click', function() {
            const field = $(this).data('field');
            refreshModels(field);
        });

        // Handle clear cache links
        $('.clear-model-cache').on('click', function(e) {
            e.preventDefault();
            clearModelCache();
        });

        // Initialize model data for each field
        $('.model-select').each(function() {
            const field = $(this).data('field');
            const providerSelect = $('#' + field + '_provider');
            
            if (!providerSelect.length || !providerSelect.val()) {
                console.warn('SLMM: No provider found for field', field);
                showNotification('Warning: No AI provider configured for ' + field.replace(/_/g, ' '), 'warning');
                return;
            }
            
            const provider = providerSelect.val();
            storeModelData(field, provider);
        });
    }

    // Switch provider and reload models
    function switchProvider(field, provider) {
        const $modelSelect = $('#' + field);
        const $searchInput = $('#model-search-' + field);
        const $modelCount = $('#model-count-' + field);
        
        // Show loading state
        $modelSelect.addClass('slmm-loading');
        $searchInput.prop('disabled', true);
        
        showNotification('Switching to ' + provider + ' provider...', 'info');
        
        // Make AJAX request to get models for the new provider
        const data = {
            action: 'slmm_get_provider_models',
            provider: provider,
            field: field,
            nonce: slmmModelSelector.nonce
        };

        $.post(slmmModelSelector.ajaxurl, data, function(response) {
            if (response.success) {
                // Update model dropdown
                updateModelDropdown(field, response.data.models, provider);
                storeModelData(field, provider, response.data.models);
                
                // Update model count
                $modelCount.text(Object.keys(response.data.models).length + ' models available');
                
                // Clear search
                $searchInput.val('');
                
                // IMPORTANT: Notify user about automatic model selection
                const modelCount = Object.keys(response.data.models).length;
                if (modelCount > 0) {
                    const firstModel = Object.keys(response.data.models)[0];
                    const firstModelName = response.data.models[firstModel];
                    $modelSelect.val(firstModel);
                    
                    // Save the automatically selected model
                    saveModelSelection(field, firstModel);
                    
                    showNotification('Provider switched to ' + provider + '. Automatically selected: ' + firstModelName + '. You can change this selection if needed.', 'success');
                } else {
                    showNotification('Warning: No models available for ' + provider + '. Please check your API key.', 'warning');
                }
            } else {
                console.error('Failed to load models:', response.data);
                showNotification('Failed to load models for ' + provider + '. Please check your API key and try again.', 'error');
            }
        }).fail(function() {
            console.error('AJAX request failed for provider switch');
            showNotification('Network error: Failed to load models. Please try again.', 'error');
        }).always(function() {
            // Remove loading state
            $modelSelect.removeClass('slmm-loading');
            $searchInput.prop('disabled', false);
        });
    }

    // Filter models based on search term using AND logic like content-helper
    function filterModels(field, searchTerm) {
        const $modelSelect = $('#' + field);
        const $modelCount = $('#model-count-' + field);
        const providerSelect = $('#' + field + '_provider');
        
        if (!providerSelect.length || !providerSelect.val()) {
            console.warn('SLMM: No provider found for field', field);
            return;
        }
        
        const provider = providerSelect.val();
        
        // Ensure we have model data, store it if missing
        if (!modelData[field] || !modelData[field][provider]) {
            storeModelData(field, provider);
        }
        
        // Get stored model data
        const allModels = modelData[field] && modelData[field][provider] ? 
                         modelData[field][provider] : {};

        let filteredModels = allModels;

        if (searchTerm && searchTerm.trim()) {
            // Split search term into individual words and clean them (exactly like content-helper)
            const searchTerms = searchTerm.toLowerCase()
                .split(/\s+/)
                .filter(term => term.length > 0)
                .map(term => term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')); // Escape regex special characters
            
            if (searchTerms.length === 0) {
                filteredModels = allModels;
            } else {
                filteredModels = {};
                Object.keys(allModels).forEach(modelId => {
                    const modelName = allModels[modelId] || '';
                    const searchString = `${modelId} ${modelName}`.toLowerCase();
                    
                    // Check if ALL search terms are found in either id or name (AND logic like content-helper)
                    const matchesAll = searchTerms.every(term => {
                        const regex = new RegExp(term, 'i'); // Case insensitive search
                        return regex.test(searchString);
                    });
                    
                    if (matchesAll) {
                        filteredModels[modelId] = modelName;
                    }
                });
            }
        }

        // Update dropdown with filtered results
        updateModelDropdown(field, filteredModels, provider, searchTerm);
        
        // Update count
        const count = Object.keys(filteredModels).length;
        const totalCount = Object.keys(allModels).length;
        
        if (searchTerm && searchTerm.trim()) {
            $modelCount.text(count + ' of ' + totalCount + ' models shown');
        } else {
            $modelCount.text(totalCount + ' models available');
        }
    }

    // Update model dropdown options (maintaining selection with explicit user notifications)
    function updateModelDropdown(field, models, provider, searchTerm = '') {
        const $modelSelect = $('#' + field);
        const currentValue = $modelSelect.val();
        
        // Clear existing options
        $modelSelect.empty();
        
        if (Object.keys(models).length === 0) {
            $modelSelect.append('<option value="">No models found</option>');
            if (currentValue) {
                showNotification('Your previously selected model "' + currentValue + '" is no longer available. Please select a new model.', 'warning');
            }
            return;
        }

        // Get all available models (before filtering) to check if current selection exists
        const allModels = modelData[field] && modelData[field][provider] ? 
                         modelData[field][provider] : {};
        
        // Check if current selection is in the filtered results
        const modelInList = models.hasOwnProperty(currentValue);
        
        // Add filtered options
        const options = [];
        Object.keys(models).forEach(modelId => {
            const modelName = models[modelId];
            const option = $('<option></option>')
                .attr('value', modelId)
                .text(modelName);
            
            // Highlight search terms if filtering
            if (searchTerm.trim()) {
                option.addClass('slmm-highlighted');
            }
            
            options.push(option);
            $modelSelect.append(option);
        });
        
        // If current selection is not in filtered list but exists in all models, add it as disabled
        // This preserves the selection like content-helper does
        if (!modelInList && currentValue && allModels[currentValue]) {
            const disabledOption = $('<option></option>')
                .attr('value', currentValue)
                .attr('disabled', true)
                .text(allModels[currentValue] + ' (Hidden by filter)')
                .css('color', '#888');
            
            $modelSelect.append(disabledOption);
        }

        // Restore previous selection with notifications
        if (currentValue && (models[currentValue] || allModels[currentValue])) {
            $modelSelect.val(currentValue);
            
            // If model was hidden by filter, notify user
            if (!modelInList && allModels[currentValue] && searchTerm.trim()) {
                showNotification('Your selected model "' + allModels[currentValue] + '" is hidden by search filter but remains selected.', 'info');
            }
        } else if (Object.keys(models).length > 0) {
            // IMPORTANT: Only auto-select if we're doing initial load, not filtering
            if (!searchTerm.trim()) {
                const firstModelId = Object.keys(models)[0];
                const firstModelName = models[firstModelId];
                $modelSelect.val(firstModelId);
                
                // Only show notification and save if there was a previous selection that's now invalid
                if (currentValue && currentValue !== firstModelId) {
                    showNotification('Previous model "' + currentValue + '" is no longer available. Automatically selected: ' + firstModelName, 'warning');
                    // Auto-save the new selection
                    saveModelSelection(field, firstModelId);
                }
            }
            // If we're filtering and no valid selection, don't auto-select anything
        }
    }

    // Store model data for a field and provider
    function storeModelData(field, provider, models = null) {
        if (!modelData[field]) {
            modelData[field] = {};
        }
        
        if (models) {
            modelData[field][provider] = models;
        } else {
            // Extract current options from select
            const currentModels = {};
            const $select = $('#' + field);
            
            if ($select.length > 0) {
                $select.find('option').each(function() {
                    const value = $(this).val();
                    const text = $(this).text();
                    if (value && value !== '' && text !== 'No models found') {
                        currentModels[value] = text;
                    }
                });
                
                modelData[field][provider] = currentModels;
            }
        }
    }

    // Refresh models for a specific field
    function refreshModels(field) {
        const $button = $('.refresh-models-btn[data-field="' + field + '"]');
        const $modelSelect = $('#' + field);
        const $searchInput = $('#model-search-' + field);
        const providerSelect = $('#' + field + '_provider');
        
        if (!providerSelect.length || !providerSelect.val()) {
            showNotification('Error: No provider configured for ' + field.replace(/_/g, ' '), 'error');
            return;
        }
        
        const provider = providerSelect.val();
        
        // Show loading state
        $button.addClass('loading slmm-spin').prop('disabled', true);
        $modelSelect.addClass('slmm-loading');
        $searchInput.prop('disabled', true);
        
        const originalText = $button.text();
        $button.text('Refreshing...');

        const data = {
            action: 'slmm_refresh_models',
            provider: provider,
            field: field,
            nonce: slmmModelSelector.nonce
        };

        $.post(slmmModelSelector.ajaxurl, data, function(response) {
            if (response.success) {
                updateModelDropdown(field, response.data.models, provider);
                storeModelData(field, provider, response.data.models);
                
                // Update model count
                $('#model-count-' + field).text(Object.keys(response.data.models).length + ' models available');
                
                // Clear search
                $searchInput.val('');
                
                // Show success message briefly
                $button.text('Refreshed!');
                showNotification('Models refreshed successfully for ' + provider, 'success');
                
                setTimeout(() => {
                    $button.text(originalText);
                }, 2000);
            } else {
                console.error('Failed to refresh models:', response.data);
                showNotification('Failed to refresh models. Please check your ' + provider + ' API key and try again.', 'error');
                $button.text(originalText);
            }
        }).fail(function() {
            console.error('AJAX request failed for model refresh');
            showNotification('Network error: Failed to refresh models. Please try again.', 'error');
            $button.text(originalText);
        }).always(function() {
            // Remove loading state
            $button.removeClass('loading slmm-spin').prop('disabled', false);
            $modelSelect.removeClass('slmm-loading');
            $searchInput.prop('disabled', false);
        });
    }

    // Clear model cache
    function clearModelCache() {
        const data = {
            action: 'slmm_clear_model_cache',
            nonce: slmmModelSelector.nonce
        };

        $.post(slmmModelSelector.ajaxurl, data, function(response) {
            if (response.success) {
                showNotification('Model cache cleared successfully. You may now refresh models to get the latest list.', 'success');
            } else {
                showNotification('Failed to clear model cache. Please try again.', 'error');
            }
        }).fail(function() {
            showNotification('Network error: Failed to clear model cache. Please try again.', 'error');
        });
    }

    // Save model selection automatically (like content-helper) with enhanced validation
    function saveModelSelection(field, selectedModel) {
        // Show visual feedback
        const $modelSelect = $('#' + field);
        const $saveIndicator = $('#save-indicator-' + field);
        
        // Create save indicator if it doesn't exist
        if ($saveIndicator.length === 0) {
            $('<span id="save-indicator-' + field + '" class="save-indicator" style="margin-left: 10px; color: #46a049; display: none;">✓ Saved</span>')
                .insertAfter($modelSelect);
        }
        
        // Show saving indicator
        $('#save-indicator-' + field).text('Saving...').show().css('color', '#666');
        
        // Validate data before sending with improved error messages
        const providerSelect = $('#' + field + '_provider');
        
        if (!providerSelect.length || !providerSelect.val()) {
            const errorMsg = 'Error: No AI provider configured for ' + field.replace(/_/g, ' ');
            console.error('SLMM Model Save Error:', errorMsg);
            $('#save-indicator-' + field).text('✗ No Provider').css('color', '#f44336');
            showNotification(errorMsg, 'error');
            return;
        }
        
        const provider = providerSelect.val();
        
        if (!field || !selectedModel) {
            const errorMsg = 'Error: Missing required data for saving model selection';
            console.error('SLMM Model Save Error:', { field, selectedModel, provider });
            $('#save-indicator-' + field).text('✗ Missing Data').css('color', '#f44336');
            showNotification(errorMsg, 'error');
            return;
        }
        
        // Verify model exists in current model list
        const currentModels = modelData[field] && modelData[field][provider] ? 
                             modelData[field][provider] : {};
        
        if (!currentModels[selectedModel]) {
            const errorMsg = 'Warning: Selected model "' + selectedModel + '" may not be available in ' + provider;
            console.warn('SLMM Model Save Warning:', errorMsg);
            showNotification(errorMsg, 'warning');
        }
        
        // Save via AJAX
        const data = {
            action: 'slmm_save_model_selection',
            field: field,
            model: selectedModel,
            provider: provider,
            nonce: slmmModelSelector.nonce
        };

        console.log('SLMM: Saving model selection:', data);

        $.post(slmmModelSelector.ajaxurl, data, function(response) {
            console.log('SLMM: Save response:', response);
            if (response.success) {
                $('#save-indicator-' + field).text('✓ Saved').css('color', '#46a049');
                setTimeout(() => {
                    $('#save-indicator-' + field).fadeOut();
                }, 2000);
            } else {
                const errorMsg = 'Save failed: ' + (response.data || 'Unknown error');
                console.error('SLMM Save Error:', response);
                $('#save-indicator-' + field).text('✗ Save Failed').css('color', '#f44336');
                showNotification(errorMsg, 'error');
            }
        }).fail(function(xhr, status, error) {
            const errorMsg = 'Network error while saving model selection';
            console.error('SLMM AJAX Error:', { xhr, status, error });
            $('#save-indicator-' + field).text('✗ Network Error').css('color', '#f44336');
            showNotification(errorMsg, 'error');
        });
    }

    // Update selection display to show current choice clearly
    function updateSelectionDisplay(field, selectedText) {
        const $selectionDisplay = $('#selection-display-' + field);
        
        // Create selection display if it doesn't exist
        if ($selectionDisplay.length === 0) {
            $('<div id="selection-display-' + field + '" class="current-selection" style="border-radius:6px!important; padding: 5px 10px; background: #3a3a40; height: 37px; align-self: center; align-content: center; border-radius: 3px; font-size: 14px; color: #fff;"></div>')
                .insertAfter($('#model-count-' + field));
        }
        
        // Update display text with provider info
        const providerSelect = $('#' + field + '_provider');
        const provider = providerSelect.length ? providerSelect.val() : 'unknown';
        
        $('#selection-display-' + field).html('<strong>Selected:</strong> ' + selectedText + ' <span style="color: #666;">(' + provider + ')</span>');
    }

    // Debounce function for search input
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Initialize when document is ready
    $(document).ready(function() {
        // Add CSS for slide-in animation
        $('<style>')
            .prop('type', 'text/css')
            .html('@keyframes slideIn { from { transform: translateX(100%); } to { transform: translateX(0); } }')
            .appendTo('head');
        
        // Check if we have the required elements
        const searchInputs = $('[id^="model-search-"]');
        const modelSelects = $('.model-select');
        const providerSelects = $('.provider-select');
        
        // Initialize selectors first
        initModelSelectors();
        
        // Add debounced search for better performance
        searchInputs.off('input').on('input', debounce(function() {
            const field = $(this).attr('id').replace('model-search-', '');
            const searchTerm = $(this).val();
            filterModels(field, searchTerm);
        }, 150)); // Reduced debounce time for more responsive search
        
        // Also add immediate input event for faster response (no debounce)
        searchInputs.off('keyup').on('keyup', function() {
            const field = $(this).attr('id').replace('model-search-', '');
            const searchTerm = $(this).val();
            filterModels(field, searchTerm);
        });
        
        // Ensure all search inputs are functional after DOM is ready
        setTimeout(function() {
            $('[id^="model-search-"]').each(function() {
                const field = $(this).attr('id').replace('model-search-', '');
                const providerSelect = $('#' + field + '_provider');
                
                if (!providerSelect.length || !providerSelect.val()) {
                    console.warn('SLMM: No provider configured for field', field);
                    return;
                }
                
                const provider = providerSelect.val();
                
                // Make sure we have model data stored
                if (!modelData[field] || !modelData[field][provider]) {
                    storeModelData(field, provider);
                }
                
                // Initialize search and selection display
                filterModels(field, '');
                
                // Show current selection with provider info
                const $modelSelect = $('#' + field);
                const currentSelection = $modelSelect.find('option:selected').text();
                if (currentSelection && currentSelection !== '' && currentSelection !== 'No models found') {
                    updateSelectionDisplay(field, currentSelection);
                }
            });
        }, 500); // Increased timeout to ensure DOM is fully ready
    });

})(jQuery); 