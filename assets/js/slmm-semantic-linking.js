/**
 * SLMM Semantic Linking Tree Interaction System
 * 
 * Handles interactive semantic link creation through tree interface:
 * - Hover over node + Press 'i' → Activates semantic linking mode
 * - Click sender page → Yellow highlight with rotating dashed border  
 * - Click receiver pages → Green highlights with rotating dashed borders
 * - Press ENTER → Saves semantic link relationships to database
 * 
 * @package SLMM_SEO_Bundle
 * @since 4.10.1
 */

(function($, d3) {
    'use strict';
    
    // Ensure dependencies are available
    if (typeof $ === 'undefined' || typeof d3 === 'undefined') {
        console.error('SLMM Semantic Linking: Missing dependencies (jQuery or D3.js)');
        return;
    }
    
    // Semantic linking mode variables (integrated with main tree system)
    var isSemanticLinkingMode = false;
    var selectedSenderNode = null;
    var selectedReceiverNodes = [];
    
    /**
     * Initialize semantic linking functionality
     */
    function initializeSemanticLinking() {
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Initializing semantic linking system', null, 'semantic');
        }
        
        // Bind global keyboard events
        $(document).on('keydown.slmm-semantic', function(event) {
            handleSemanticKeyboardEvents(event);
        });
        
        // Listen for semantic node click events triggered from main handler
        $(document).on('slmm_semantic_node_click', function(event, originalEvent, nodeElement, nodeData) {
            handleSemanticNodeClick(originalEvent, nodeElement, nodeData);
        });
        
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Semantic linking system initialized', null, 'semantic');
        }
    }
    
    /**
     * Handle keyboard events for semantic linking
     * @param {Event} event Keyboard event
     */
    function handleSemanticKeyboardEvents(event) {
        // 'i' key for semantic linking mode - USES MAIN TREE HOVER TRACKING
        if (event.key === 'i' || event.key === 'I') {
            // Only trigger if not typing in an input field and hovering over a node
            if (!$(event.target).is('input, textarea, [contenteditable]') && window.currentHoveredNode) {
                event.preventDefault();
                
                const nodeData = window.currentHoveredNode;
                if (nodeData && nodeData.data.id && nodeData.data.post_type !== 'site') {
                    if (typeof SlmmDebugLogger !== 'undefined') {
                        SlmmDebugLogger.log('Semantic linking shortcut triggered for node', {nodeName: nodeData.data.name}, 'semantic');
                    }
                    
                    // Enter semantic linking mode
                    enterSemanticLinkingMode();
                    
                    // Automatically select the hovered node as sender
                    selectedSenderNode = nodeData;
                    if (typeof SlmmDebugLogger !== 'undefined') {
                        SlmmDebugLogger.log('Auto-selected semantic sender', {nodeName: nodeData.data.name}, 'semantic');
                    }
                    
                    // Add visual highlight for sender using the hovered element
                    if (window.hoveredNodeElement) {
                        d3.select(window.hoveredNodeElement).select('.slmm-node-rect')
                            .classed('slmm-semantic-sender', true);
                    }
                    
                    updateStatusMessage(`Sender auto-selected: ${nodeData.data.name}. Now click receiver nodes, then press Enter`);
                    showKeyboardHint(`Sender: ${nodeData.data.name} selected! Click receivers 📍`);
                    
                    return false;
                } else {
                    updateStatusMessage('Semantic linking not available for this node type');
                    showKeyboardHint('Semantic linking not available ❌');
                }
            }
        }
        
        // Enter key: Finalize semantic linking selections
        if (event.key === 'Enter') {
            if (!$(event.target).is('input, textarea, [contenteditable]') && isSemanticLinkingMode) {
                event.preventDefault();
                
                // Validate semantic linking selections
                if (selectedSenderNode && selectedReceiverNodes.length > 0) {
                    if (typeof SlmmDebugLogger !== 'undefined') {
                        SlmmDebugLogger.log('Creating semantic links from sender to receivers', {
                            senderName: selectedSenderNode.data.name,
                            receiverCount: selectedReceiverNodes.length
                        }, 'semantic');
                    }
                    createSemanticLinks();
                    return false;
                } else {
                    const missingMsg = !selectedSenderNode ? 'No sender selected' : 'No receivers selected';
                    updateStatusMessage(`Cannot create semantic links: ${missingMsg}`);
                    showKeyboardHint('Select sender and receiver nodes first! 📍');
                }
            }
        }
        
        // Check if ESC key pressed (keyCode 27)
        if (event.key === 'Escape') {
            if (isSemanticLinkingMode) {
                event.preventDefault();
                exitSemanticLinkingMode();
                showKeyboardHint('Semantic linking cancelled! 🚫');
                return false;
            }
        }
    }
    
    /**
     * Handle node click events for semantic linking mode
     * @param {Event} event Click event
     * @param {Element} nodeElement Node DOM element
     * @param {Object} nodeData Node data (optional, will be retrieved if not provided)
     */
    function handleSemanticNodeClick(event, nodeElement, nodeData) {
        if (!isSemanticLinkingMode) return;
        
        // Get node data if not provided
        if (!nodeData) {
            nodeData = d3.select(nodeElement).datum();
        }
        if (!nodeData || !nodeData.data.id) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        const nodeId = nodeData.data.id;
        const nodeName = nodeData.data.name;
        
        // Prevent self-linking
        if (selectedSenderNode && selectedSenderNode.data.id === nodeId) {
            updateStatusMessage('Cannot create semantic link to self');
            showKeyboardHint('Self-linking not allowed ❌');
            return;
        }
        
        // First click selects sender (but since we auto-select with 'i' key, this is for manual clicking)
        if (!selectedSenderNode) {
            selectedSenderNode = nodeData;
            if (typeof SlmmDebugLogger !== 'undefined') {
                SlmmDebugLogger.log('Semantic sender selected', {nodeName: nodeName}, 'semantic');
            }
            
            // Add visual highlight for sender
            d3.select(nodeElement).select('.slmm-node-rect')
                .classed('slmm-semantic-sender', true);
            
            updateStatusMessage(`Sender selected: ${nodeName}. Now click receiver nodes, then press Enter`);
            showKeyboardHint(`Sender: ${nodeName} selected! Click receivers 📍`);
        } else {
            // Subsequent clicks add/remove receivers
            const existingIndex = selectedReceiverNodes.findIndex(r => r.data.id === nodeId);
            
            if (existingIndex >= 0) {
                // Remove from receivers if already selected
                selectedReceiverNodes.splice(existingIndex, 1);
                d3.select(nodeElement).select('.slmm-node-rect')
                    .classed('slmm-semantic-receiver', false);
                
                if (typeof SlmmDebugLogger !== 'undefined') {
                    SlmmDebugLogger.log('Receiver removed', {nodeName: nodeName}, 'semantic');
                }
                updateStatusMessage(`Receiver removed: ${nodeName}. ${selectedReceiverNodes.length} receivers selected`);
            } else {
                // Add to receivers
                selectedReceiverNodes.push(nodeData);
                d3.select(nodeElement).select('.slmm-node-rect')
                    .classed('slmm-semantic-receiver', true);
                
                if (typeof SlmmDebugLogger !== 'undefined') {
                    SlmmDebugLogger.log('Receiver added', {nodeName: nodeName}, 'semantic');
                }
                updateStatusMessage(`Receiver added: ${nodeName}. ${selectedReceiverNodes.length} receivers selected`);
            }
            
            showKeyboardHint(`${selectedReceiverNodes.length} receiver(s) selected. Press Enter to create links! 🔗`);
        }
    }
    
    /**
     * Enter semantic linking mode
     */
    function enterSemanticLinkingMode() {
        isSemanticLinkingMode = true;
        selectedSenderNode = null;
        selectedReceiverNodes = [];
        
        // Set global flag for main click handler
        window.slmmSemanticLinkingActive = true;
        
        // Update UI to show semantic linking state
        $('body').addClass('slmm-semantic-linking-active');
        updateStatusMessage('Semantic linking mode: Click sender node, then receiver nodes, then press Enter');
        
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Entered semantic linking mode', null, 'semantic');
        }
    }
    
    /**
     * Exit semantic linking mode
     */
    function exitSemanticLinkingMode() {
        isSemanticLinkingMode = false;
        selectedSenderNode = null;
        selectedReceiverNodes = [];
        
        // Clear global flag for main click handler
        window.slmmSemanticLinkingActive = false;
        
        // Remove UI state
        $('body').removeClass('slmm-semantic-linking-active');
        
        // Remove visual feedback from all nodes
        d3.selectAll('.slmm-tree-node .slmm-node-rect')
            .classed('slmm-semantic-sender', false)
            .classed('slmm-semantic-receiver', false);
        
        // Restore normal status
        updateStatusMessage('Semantic linking mode cancelled');
        
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Exited semantic linking mode', null, 'semantic');
        }
    }
    
    /**
     * Create semantic links via AJAX - FIXED VERSION
     */
    function createSemanticLinks() {
        if (!selectedSenderNode || selectedReceiverNodes.length === 0) {
            updateStatusMessage('Cannot create semantic links: Missing sender or receivers');
            showKeyboardHint('Select sender and receivers first! 📍');
            return;
        }
        
        const senderId = selectedSenderNode.data.id;
        const senderName = selectedSenderNode.data.name;
        let linksCreated = 0;
        let linksFailed = 0;
        
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Creating semantic links from sender to receivers', {
                senderName: senderName,
                receiverCount: selectedReceiverNodes.length
            }, 'semantic');
        }
        updateStatusMessage(`Creating ${selectedReceiverNodes.length} semantic links...`);
        
        // Process each receiver individually (this was working in the original commit)
        const promises = selectedReceiverNodes.map(receiverNode => {
            const receiverId = receiverNode.data.id;
            const receiverName = receiverNode.data.name;
            
            return new Promise((resolve) => {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'slmm_create_semantic_link',  // Use single create action like in working commit
                        nonce: getSemanticLinksNonce(),
                        sender_id: senderId,
                        receiver_id: receiverId
                    },
                    success: function(response) {
                        if (response.success) {
                            linksCreated++;
                            if (typeof SlmmDebugLogger !== 'undefined') {
                                SlmmDebugLogger.log('Semantic link created', {
                                    senderName: senderName,
                                    receiverName: receiverName
                                }, 'semantic');
                            }
                        } else {
                            linksFailed++;
                            console.warn('❌ SLMM: Failed to create semantic link:', senderName, '→', receiverName, response.data);
                        }
                        resolve();
                    },
                    error: function() {
                        linksFailed++;
                        console.error('❌ SLMM: AJAX error creating semantic link:', senderName, '→', receiverName);
                        resolve();
                    }
                });
            });
        });
        
        // Wait for all requests to complete
        Promise.all(promises).then(() => {
            // Provide user feedback
            const totalAttempted = selectedReceiverNodes.length;
            if (linksCreated === totalAttempted) {
                updateStatusMessage(`Successfully created ${linksCreated} semantic links`);
                showKeyboardHint(`${linksCreated} semantic links created! 🎉`);
            } else if (linksCreated > 0) {
                updateStatusMessage(`Created ${linksCreated} of ${totalAttempted} semantic links (${linksFailed} failed)`);
                showKeyboardHint(`${linksCreated}/${totalAttempted} links created ⚠️`);
            } else {
                updateStatusMessage(`Failed to create any semantic links (${linksFailed} errors)`);
                showKeyboardHint('Link creation failed ❌');
            }
            
            // Exit semantic linking mode
            exitSemanticLinkingMode();
            
            // Refresh Direct Editor semantic links section if available
            if (typeof SLMMDirectEditor !== 'undefined' && SLMMDirectEditor.refreshSemanticLinksSection) {
                SLMMDirectEditor.refreshSemanticLinksSection();
                if (typeof SlmmDebugLogger !== 'undefined') {
                    SlmmDebugLogger.log('Direct Editor semantic links section refreshed', null, 'semantic');
                }
            }
            
            if (typeof SlmmDebugLogger !== 'undefined') {
                SlmmDebugLogger.log('Semantic link creation completed', {
                    linksCreated: linksCreated,
                    linksFailed: linksFailed
                }, 'semantic');
            }
        });
    }
    
    /**
     * Get AJAX nonce for security - FIXED VERSION
     * @returns {string} Nonce value
     */
    function getSemanticLinksNonce() {
        // Use the semantic links specific nonce for proper security validation
        return window.slmmInterlinkingData?.semantic_links_nonce || window.slmmInterlinkingData?.nonce || 'slmm_interlinking_nonce';
    }
    
    /**
     * Update status message (use main tree function if available)
     * @param {string} message Status message
     */
    function updateStatusMessage(message) {
        if (typeof window.updateStatusMessage === 'function') {
            window.updateStatusMessage(message);
        } else {
            if (typeof SlmmDebugLogger !== 'undefined') {
                SlmmDebugLogger.log('Status message updated', {message: message}, 'semantic');
            }
        }
    }
    
    /**
     * Show keyboard hint (use main tree function if available)
     * @param {string} hint Keyboard hint message
     */
    function showKeyboardHint(hint) {
        if (typeof window.showKeyboardHint === 'function') {
            window.showKeyboardHint(hint);
        } else {
            if (typeof SlmmDebugLogger !== 'undefined') {
                SlmmDebugLogger.log('Keyboard hint shown', {hint: hint}, 'semantic');
            }
        }
    }
    
    // Initialize when document is ready and wait for main tree system
    $(document).ready(function() {
        // Initialize global flag
        window.slmmSemanticLinkingActive = false;
        
        // Wait for main D3.js tree system to be ready
        const initInterval = setInterval(() => {
            if (d3.select('#slmm-tree-svg').node() && 
                typeof window.currentHoveredNode !== 'undefined' &&
                typeof window.hoveredNodeElement !== 'undefined') {
                
                initializeSemanticLinking();
                clearInterval(initInterval);
                if (typeof SlmmDebugLogger !== 'undefined') {
                    SlmmDebugLogger.log('Semantic linking integrated with main tree system', null, 'semantic');
                }
            }
        }, 500);
        
        // Stop trying after 30 seconds
        setTimeout(() => {
            clearInterval(initInterval);
        }, 30000);
    });
    
})(jQuery, d3);