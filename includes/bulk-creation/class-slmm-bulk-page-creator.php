<?php
/**
 * SLMM Bulk Page Creator
 * Core class for creating multiple WordPress pages in batch operations
 * 
 * @package SLMM_SEO_Bundle
 * @subpackage Bulk_Creation
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class SLMM_Bulk_Page_Creator
 * 
 * Handles bulk page creation with optimized performance, security, and WordPress integration.
 * Includes batch processing, error handling, and integration with the interlinking suite.
 */
class SLMM_Bulk_Page_Creator {
    
    /**
     * Singleton instance
     * @var SLMM_Bulk_Page_Creator|null
     */
    private static $instance = null;
    
    /**
     * Maximum pages per batch to prevent timeout/memory issues
     * @var int
     */
    private $max_batch_size = 25;
    
    /**
     * Maximum execution time per batch in seconds
     * @var int
     */
    private $max_execution_time = 120;
    
    /**
     * Default content template for new pages
     * @var string
     */
    private $default_content_template = '';
    
    /**
     * Get singleton instance
     * 
     * @return SLMM_Bulk_Page_Creator
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor to enforce singleton pattern
     */
    private function __construct() {
        $this->default_content_template = $this->get_default_content_template();
    }
    
    /**
     * Create pages in batch with comprehensive error handling and optimization
     * 
     * @param array $page_data Array of page data to create
     * @param array $options Creation options
     * @return array Creation results with success/error details
     */
    public function create_pages_batch($page_data, $options = array()) {
        // Validate and sanitize options
        $options = $this->validate_creation_options($options);
        
        // Initialize results structure
        $results = array(
            'created' => array(),
            'failed' => array(),
            'total_processed' => 0,
            'success_count' => 0,
            'error_count' => 0,
            'processing_time' => 0,
            'memory_used' => 0,
            'warnings' => array()
        );
        
        // Validate input data
        if (!is_array($page_data) || empty($page_data)) {
            $results['failed'][] = array(
                'error' => 'No valid page data provided',
                'code' => 'INVALID_INPUT'
            );
            return $results;
        }
        
        $start_time = microtime(true);
        $start_memory = memory_get_usage(true);
        
        try {
            // Optimize WordPress for bulk operations
            $this->optimize_for_bulk_operations();
            
            // Process pages in batches
            $batches = array_chunk($page_data, $this->max_batch_size);
            
            foreach ($batches as $batch_index => $batch) {
                $batch_results = $this->process_batch($batch, $options, $batch_index);
                
                // Merge batch results
                $results['created'] = array_merge($results['created'], $batch_results['created']);
                $results['failed'] = array_merge($results['failed'], $batch_results['failed']);
                $results['total_processed'] += $batch_results['total_processed'];
                $results['success_count'] += $batch_results['success_count'];
                $results['error_count'] += $batch_results['error_count'];
                $results['warnings'] = array_merge($results['warnings'], $batch_results['warnings']);
                
                // Memory management between batches
                if ($batch_index < count($batches) - 1) {
                    $this->cleanup_between_batches();
                    
                    // Check execution time to prevent timeout
                    if ((microtime(true) - $start_time) > $this->max_execution_time) {
                        $results['warnings'][] = 'Execution time limit reached. Some pages may not have been processed.';
                        break;
                    }
                }
            }
            
        } catch (Exception $e) {
            $results['failed'][] = array(
                'error' => 'Batch processing failed: ' . $e->getMessage(),
                'code' => 'BATCH_PROCESSING_ERROR'
            );
            
            error_log('[SLMM Bulk Creator] Batch processing exception: ' . $e->getMessage());
        } finally {
            // Restore WordPress optimizations
            $this->restore_wordpress_optimizations();
        }
        
        // Calculate final metrics
        $results['processing_time'] = round(microtime(true) - $start_time, 2);
        $results['memory_used'] = round((memory_get_usage(true) - $start_memory) / 1024 / 1024, 2); // MB
        
        // Log operation summary
        $this->log_bulk_operation($results);
        
        return $results;
    }
    
    /**
     * Process a single batch of pages
     * 
     * @param array $batch Page data for this batch
     * @param array $options Creation options
     * @param int $batch_index Current batch index
     * @return array Batch processing results
     */
    private function process_batch($batch, $options, $batch_index) {
        $batch_results = array(
            'created' => array(),
            'failed' => array(),
            'total_processed' => 0,
            'success_count' => 0,
            'error_count' => 0,
            'warnings' => array()
        );
        
        foreach ($batch as $page_index => $page_item) {
            $batch_results['total_processed']++;
            
            try {
                // Create individual page
                $page_id = $this->create_single_page($page_item, $options);
                
                if ($page_id && !is_wp_error($page_id)) {
                    $created_page = $this->format_created_page_data($page_id, $page_item);
                    $batch_results['created'][] = $created_page;
                    $batch_results['success_count']++;
                    
                    // Register with interlinking suite if applicable
                    if ($options['register_with_interlinking']) {
                        $this->register_with_interlinking_suite($page_id, $page_item, $options);
                    }
                    
                } else {
                    $error_message = is_wp_error($page_id) ? $page_id->get_error_message() : 'Unknown error creating page';
                    
                    $batch_results['failed'][] = array(
                        'title' => $page_item['title'] ?? 'Unknown',
                        'error' => $error_message,
                        'code' => is_wp_error($page_id) ? $page_id->get_error_code() : 'UNKNOWN_ERROR',
                        'batch_index' => $batch_index,
                        'page_index' => $page_index
                    );
                    $batch_results['error_count']++;
                }
                
            } catch (Exception $e) {
                $batch_results['failed'][] = array(
                    'title' => $page_item['title'] ?? 'Unknown',
                    'error' => $e->getMessage(),
                    'code' => 'EXCEPTION',
                    'batch_index' => $batch_index,
                    'page_index' => $page_index
                );
                $batch_results['error_count']++;
                
                error_log('[SLMM Bulk Creator] Page creation exception: ' . $e->getMessage());
            }
            
            // Yield control occasionally to prevent timeout
            if (($batch_results['total_processed'] % 5) === 0) {
                $this->yield_control();
            }
        }
        
        return $batch_results;
    }
    
    /**
     * Create a single WordPress page
     * 
     * @param array $page_item Page data
     * @param array $options Creation options
     * @return int|WP_Error Page ID or error
     */
    private function create_single_page($page_item, $options) {
        // Validate required fields
        if (empty($page_item['title'])) {
            return new WP_Error('missing_title', 'Page title is required');
        }
        
        // Generate slug if not provided
        $slug = $page_item['slug'] ?? sanitize_title($page_item['title']);
        
        // Ensure slug is unique
        $unique_slug = $this->ensure_unique_slug($slug);
        
        // Prepare post data
        $post_data = array(
            'post_title' => sanitize_text_field($page_item['title']),
            'post_name' => $unique_slug,
            'post_content' => $this->generate_page_content($page_item, $options),
            'post_status' => sanitize_text_field($options['status']),
            'post_type' => 'page',
            'post_author' => intval($options['author_id']),
            'post_parent' => intval($options['parent_id']),
            'menu_order' => intval($page_item['menu_order'] ?? 0),
            'comment_status' => 'closed',
            'ping_status' => 'closed'
        );
        
        // Add excerpt if provided
        if (!empty($page_item['excerpt'])) {
            $post_data['post_excerpt'] = sanitize_textarea_field($page_item['excerpt']);
        }
        
        // Create the page
        $page_id = wp_insert_post($post_data, true);
        
        if ($page_id && !is_wp_error($page_id)) {
            // Add custom meta fields
            $this->add_page_meta_fields($page_id, $page_item, $options);
            
            // Set featured image if provided
            if (!empty($page_item['featured_image_id'])) {
                set_post_thumbnail($page_id, intval($page_item['featured_image_id']));
            }
            
            // Add to specific categories/tags if specified
            if (!empty($page_item['categories'])) {
                wp_set_post_categories($page_id, array_map('intval', $page_item['categories']));
            }
            
            if (!empty($page_item['tags'])) {
                wp_set_post_tags($page_id, $page_item['tags']);
            }
        }
        
        return $page_id;
    }
    
    /**
     * Generate content for the new page
     * 
     * @param array $page_item Page data
     * @param array $options Creation options
     * @return string Generated content
     */
    private function generate_page_content($page_item, $options) {
        // Use custom content if provided
        if (!empty($page_item['content'])) {
            return wp_kses_post($page_item['content']);
        }
        
        // Use custom template if provided in options
        if (!empty($options['content_template'])) {
            $template = $options['content_template'];
        } else {
            $template = $this->default_content_template;
        }
        
        // Replace placeholders in template
        $placeholders = array(
            '{TITLE}' => $page_item['title'],
            '{DATE}' => current_time('F j, Y'),
            '{PARENT_TITLE}' => $options['parent_title'] ?? '',
            '{SLUG}' => $page_item['slug'] ?? sanitize_title($page_item['title']),
            '{KEYWORDS}' => $this->extract_keywords_from_title($page_item['title'])
        );
        
        $content = str_replace(array_keys($placeholders), array_values($placeholders), $template);
        
        return wp_kses_post($content);
    }
    
    /**
     * Add meta fields to the created page
     * 
     * @param int $page_id Created page ID
     * @param array $page_item Page data
     * @param array $options Creation options
     */
    private function add_page_meta_fields($page_id, $page_item, $options) {
        // SLMM-specific meta fields
        update_post_meta($page_id, '_slmm_created_via_bulk', current_time('mysql'));
        update_post_meta($page_id, '_slmm_needs_content_optimization', 1);
        update_post_meta($page_id, '_slmm_bulk_creation_batch', $options['batch_id'] ?? '');
        
        // Set default importance level to 3 for bulk created pages
        update_post_meta($page_id, '_slmm_importance_rating', 3);
        
        // SEO meta fields
        if (!empty($page_item['meta_description'])) {
            update_post_meta($page_id, '_slmm_meta_description', sanitize_textarea_field($page_item['meta_description']));
        }
        
        if (!empty($page_item['target_keywords'])) {
            $keywords = is_array($page_item['target_keywords']) ? 
                       $page_item['target_keywords'] : 
                       array_map('trim', explode(',', $page_item['target_keywords']));
            update_post_meta($page_id, '_slmm_target_keywords', array_map('sanitize_text_field', $keywords));
        } else {
            // Auto-generate basic keywords from title
            $auto_keywords = $this->extract_keywords_from_title($page_item['title']);
            update_post_meta($page_id, '_slmm_target_keywords', $auto_keywords);
        }
        
        // Content optimization flags
        update_post_meta($page_id, '_slmm_content_depth', 'minimal');
        update_post_meta($page_id, '_slmm_seo_score', 45); // Starting score for new pages
        update_post_meta($page_id, '_slmm_last_seo_analysis', current_time('mysql'));
        
        // Parent relationship data for interlinking
        if (!empty($options['parent_id'])) {
            update_post_meta($page_id, '_slmm_parent_page_id', intval($options['parent_id']));
            update_post_meta($page_id, '_slmm_auto_link_to_parent', $options['auto_link'] ? 1 : 0);
        }
        
        // Additional custom meta fields
        if (!empty($page_item['custom_meta']) && is_array($page_item['custom_meta'])) {
            foreach ($page_item['custom_meta'] as $meta_key => $meta_value) {
                $sanitized_key = sanitize_key($meta_key);
                if (strpos($sanitized_key, '_slmm_') === 0) { // Only allow SLMM prefixed meta
                    update_post_meta($page_id, $sanitized_key, sanitize_text_field($meta_value));
                }
            }
        }
    }
    
    /**
     * Format created page data for response
     * 
     * @param int $page_id Created page ID
     * @param array $page_item Original page data
     * @return array Formatted page data
     */
    private function format_created_page_data($page_id, $page_item) {
        $page = get_post($page_id);
        
        return array(
            'id' => $page_id,
            'title' => $page->post_title,
            'slug' => $page->post_name,
            'url' => get_permalink($page_id),
            'edit_url' => admin_url('post.php?post=' . $page_id . '&action=edit'),
            'status' => $page->post_status,
            'parent_id' => $page->post_parent,
            'created_time' => $page->post_date,
            'author_id' => $page->post_author,
            'menu_order' => $page->menu_order
        );
    }
    
    /**
     * Register created page with interlinking suite
     * 
     * @param int $page_id Created page ID
     * @param array $page_item Original page data
     * @param array $options Creation options
     */
    private function register_with_interlinking_suite($page_id, $page_item, $options) {
        // Add to interlinking suite data structure
        if (class_exists('SLMM_Interlinking_Suite')) {
            $interlinking = SLMM_Interlinking_Suite::get_instance();
            
            // Register the new page in the tree structure
            if (method_exists($interlinking, 'add_page_to_tree')) {
                $interlinking->add_page_to_tree($page_id, $options['parent_id'] ?? 0);
            }
            
            // Create automatic internal link to parent if requested
            if ($options['auto_link'] && !empty($options['parent_id'])) {
                $this->create_auto_link_to_parent($page_id, $options['parent_id']);
            }
        }
        
        // Trigger action for external integrations
        do_action('slmm_bulk_page_created', $page_id, $page_item, $options);
    }
    
    /**
     * Create automatic internal link from new page to parent
     * 
     * @param int $page_id New page ID
     * @param int $parent_id Parent page ID
     */
    private function create_auto_link_to_parent($page_id, $parent_id) {
        $page = get_post($page_id);
        $parent = get_post($parent_id);
        
        if (!$page || !$parent) {
            return;
        }
        
        $parent_url = get_permalink($parent_id);
        $parent_title = $parent->post_title;
        
        // Add contextual link to the new page content
        $link_html = sprintf(
            '<p><em>This page is part of our <a href="%s">%s</a> resource collection.</em></p>',
            esc_url($parent_url),
            esc_html($parent_title)
        );
        
        // Append to existing content
        $current_content = $page->post_content;
        $updated_content = $current_content . "\n\n" . $link_html;
        
        wp_update_post(array(
            'ID' => $page_id,
            'post_content' => $updated_content
        ));
    }
    
    /**
     * Ensure slug is unique by appending numbers if necessary
     * 
     * @param string $slug Base slug
     * @return string Unique slug
     */
    private function ensure_unique_slug($slug) {
        $original_slug = $slug;
        $counter = 1;
        
        while ($this->slug_exists($slug)) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
            
            // Prevent infinite loops
            if ($counter > 1000) {
                $slug = $original_slug . '-' . time();
                break;
            }
        }
        
        return $slug;
    }
    
    /**
     * Check if slug already exists
     * 
     * @param string $slug Slug to check
     * @return bool True if slug exists
     */
    private function slug_exists($slug) {
        global $wpdb;
        
        $exists = $wpdb->get_var($wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts} WHERE post_name = %s AND post_type = 'page' AND post_status != 'trash'",
            $slug
        ));
        
        return !empty($exists);
    }
    
    /**
     * Extract keywords from page title
     * 
     * @param string $title Page title
     * @return array Extracted keywords
     */
    private function extract_keywords_from_title($title) {
        // Remove common stop words
        $stop_words = array('the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'should', 'could', 'can', 'may', 'might', 'must', 'shall');
        
        // Clean and split title
        $words = preg_split('/[^\w]+/', strtolower($title));
        $keywords = array();
        
        foreach ($words as $word) {
            $word = trim($word);
            if (strlen($word) > 2 && !in_array($word, $stop_words)) {
                $keywords[] = $word;
            }
        }
        
        // Return up to 5 keywords
        return array_slice(array_unique($keywords), 0, 5);
    }
    
    /**
     * Validate and sanitize creation options
     * 
     * @param array $options Raw options
     * @return array Validated options
     */
    private function validate_creation_options($options) {
        $defaults = array(
            'status' => 'draft',
            'author_id' => get_current_user_id(),
            'parent_id' => 0,
            'parent_title' => '',
            'auto_link' => true,
            'content_template' => '',
            'batch_id' => uniqid('batch_'),
            'register_with_interlinking' => true
        );
        
        $validated = wp_parse_args($options, $defaults);
        
        // Sanitize values
        $validated['status'] = in_array($validated['status'], array('draft', 'publish', 'private')) ? 
                              $validated['status'] : 'draft';
        $validated['author_id'] = absint($validated['author_id']) ?: get_current_user_id();
        $validated['parent_id'] = absint($validated['parent_id']);
        $validated['parent_title'] = sanitize_text_field($validated['parent_title']);
        $validated['auto_link'] = (bool) $validated['auto_link'];
        $validated['content_template'] = wp_kses_post($validated['content_template']);
        $validated['batch_id'] = sanitize_key($validated['batch_id']);
        $validated['register_with_interlinking'] = (bool) $validated['register_with_interlinking'];
        
        return $validated;
    }
    
    /**
     * Optimize WordPress settings for bulk operations
     */
    private function optimize_for_bulk_operations() {
        // Suspend cache operations during bulk creation
        wp_suspend_cache_addition(true);
        
        // Increase memory limit if possible
        if (function_exists('ini_set')) {
            $current_limit = ini_get('memory_limit');
            if ($current_limit && $current_limit !== '-1') {
                $limit_bytes = $this->convert_memory_limit_to_bytes($current_limit);
                if ($limit_bytes < 256 * 1024 * 1024) { // Less than 256MB
                    @ini_set('memory_limit', '256M');
                }
            }
        }
        
        // Disable automatic spam/moderation checks
        remove_action('transition_post_status', '_transition_post_status', 10);
        
        // Remove unnecessary actions during bulk operation
        $this->removed_actions = array();
        
        // Store and remove post updated messages
        if (has_action('post_updated_messages')) {
            $this->removed_actions['post_updated_messages'] = true;
            remove_all_actions('post_updated_messages');
        }
    }
    
    /**
     * Restore WordPress optimizations after bulk operation
     */
    private function restore_wordpress_optimizations() {
        // Re-enable cache operations
        wp_suspend_cache_addition(false);
        
        // Restore removed actions
        if (isset($this->removed_actions['post_updated_messages'])) {
            add_action('transition_post_status', '_transition_post_status', 10, 3);
        }
        
        // Clear any accumulated cache
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
    }
    
    /**
     * Cleanup operations between batches
     */
    private function cleanup_between_batches() {
        // Clear object cache
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // Force garbage collection
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
        
        // Yield control to prevent timeout
        $this->yield_control();
    }
    
    /**
     * Yield control back to server to prevent timeout
     */
    private function yield_control() {
        if (function_exists('fastcgi_finish_request')) {
            fastcgi_finish_request();
        }
        
        // Brief pause to prevent overwhelming server
        usleep(10000); // 10ms
    }
    
    /**
     * Convert memory limit string to bytes
     * 
     * @param string $limit Memory limit string (e.g., '128M')
     * @return int Bytes
     */
    private function convert_memory_limit_to_bytes($limit) {
        $value = intval($limit);
        $unit = strtoupper(substr($limit, -1));
        
        switch ($unit) {
            case 'G':
                $value *= 1024 * 1024 * 1024;
                break;
            case 'M':
                $value *= 1024 * 1024;
                break;
            case 'K':
                $value *= 1024;
                break;
        }
        
        return $value;
    }
    
    /**
     * Log bulk operation results
     * 
     * @param array $results Operation results
     */
    private function log_bulk_operation($results) {
        $log_data = array(
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'success_count' => $results['success_count'],
            'error_count' => $results['error_count'],
            'processing_time' => $results['processing_time'],
            'memory_used' => $results['memory_used']
        );
        
        error_log('[SLMM Bulk Creator] Operation completed: ' . wp_json_encode($log_data));
        
        // Store operation log in database for admin review if needed
        $this->store_operation_log($log_data);
    }
    
    /**
     * Store operation log in database
     * 
     * @param array $log_data Log data to store
     */
    private function store_operation_log($log_data) {
        $logs = get_option('slmm_bulk_creation_logs', array());
        
        // Keep only last 50 operations
        if (count($logs) >= 50) {
            $logs = array_slice($logs, -49);
        }
        
        $logs[] = $log_data;
        update_option('slmm_bulk_creation_logs', $logs, false);
    }
    
    /**
     * Get default content template for new pages
     * 
     * @return string Default template
     */
    private function get_default_content_template() {
        return '<h1>{TITLE}</h1>

<p>This page was created on {DATE} and is ready for content development as part of your SEO content strategy.</p>

<h2>Content Development Guidelines</h2>
<p>To optimize this page for search engines and user experience, consider including:</p>

<ul>
<li><strong>Target Keywords:</strong> {KEYWORDS}</li>
<li><strong>Comprehensive Content:</strong> Aim for 1500+ words of valuable, in-depth information</li>
<li><strong>Internal Linking:</strong> Link to related pages in your content silo structure</li>
<li><strong>Visual Elements:</strong> Include relevant images, videos, or infographics</li>
<li><strong>Call-to-Action:</strong> Guide users to the next step in their journey</li>
</ul>

<h2>SEO Optimization Checklist</h2>
<ul>
<li>✅ Page title optimized for target keywords</li>
<li>⏳ Meta description (add in SEO settings)</li>
<li>⏳ Header structure (H1, H2, H3) for content organization</li>
<li>⏳ Internal links to related pages</li>
<li>⏳ External links to authoritative sources</li>
<li>⏳ Image alt text for accessibility and SEO</li>
<li>⏳ Page loading speed optimization</li>
</ul>

<div style="background: #f8f9fa; padding: 20px; border-left: 4px solid #007cba; margin: 20px 0;">
<p><strong>💡 Content Development Tips:</strong></p>
<ul>
<li>Research what your audience is searching for related to "{TITLE}"</li>
<li>Analyze competitor content to identify content gaps</li>
<li>Create content that provides unique value and insights</li>
<li>Update and refresh content regularly to maintain relevance</li>
</ul>
</div>

<p><em>This page is part of your website\'s content architecture. <a href="#edit-content">Start editing</a> to transform this template into valuable content for your audience.</em></p>';
    }
    
    /**
     * Get bulk creation statistics for admin dashboard
     * 
     * @return array Statistics data
     */
    public function get_bulk_creation_stats() {
        $logs = get_option('slmm_bulk_creation_logs', array());
        
        if (empty($logs)) {
            return array(
                'total_operations' => 0,
                'total_pages_created' => 0,
                'average_processing_time' => 0,
                'success_rate' => 0
            );
        }
        
        $total_operations = count($logs);
        $total_pages = array_sum(array_column($logs, 'success_count'));
        $total_errors = array_sum(array_column($logs, 'error_count'));
        $average_time = array_sum(array_column($logs, 'processing_time')) / $total_operations;
        $success_rate = $total_pages > 0 ? ($total_pages / ($total_pages + $total_errors)) * 100 : 0;
        
        return array(
            'total_operations' => $total_operations,
            'total_pages_created' => $total_pages,
            'total_errors' => $total_errors,
            'average_processing_time' => round($average_time, 2),
            'success_rate' => round($success_rate, 1)
        );
    }
    
    /**
     * Clean up old bulk creation logs
     * 
     * @param int $days_to_keep Number of days to keep logs
     */
    public function cleanup_old_logs($days_to_keep = 30) {
        $logs = get_option('slmm_bulk_creation_logs', array());
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days_to_keep} days"));
        
        $filtered_logs = array_filter($logs, function($log) use ($cutoff_date) {
            return isset($log['timestamp']) && $log['timestamp'] >= $cutoff_date;
        });
        
        if (count($filtered_logs) !== count($logs)) {
            update_option('slmm_bulk_creation_logs', array_values($filtered_logs));
            return count($logs) - count($filtered_logs);
        }
        
        return 0;
    }
}