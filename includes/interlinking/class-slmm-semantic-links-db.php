<?php
/**
 * SLMM Semantic Links Database Handler
 * Manages database operations for semantic link relationships
 * 
 * @package SLMM_SEO_Bundle
 * @subpackage Interlinking
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class SLMM_Semantic_Links_DB
 * 
 * Handles all database operations for semantic link relationships.
 * Uses custom table for many-to-many post relationships.
 */
class SLMM_Semantic_Links_DB {
    
    /**
     * Singleton instance
     * @var SLMM_Semantic_Links_DB|null
     */
    private static $instance = null;
    
    /**
     * Database table name (without prefix)
     * @var string
     */
    private $table_name = 'slmm_semantic_links';
    
    /**
     * Database version for schema updates
     * @var string
     */
    private $db_version = '1.0.0';
    
    /**
     * Get singleton instance
     * 
     * @return SLMM_Semantic_Links_DB
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor to enforce singleton pattern
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize database handler
     */
    private function init() {
        // Register activation hook for table creation
        register_activation_hook(SLMM_SEO_PLUGIN_DIR . 'plugin.php', array($this, 'create_semantic_links_table'));
        
        // Check if table needs updates on admin init
        add_action('admin_init', array($this, 'check_db_version'));
        
        // Ensure table exists immediately (for development/updates)
        $this->ensure_table_exists();
    }
    
    /**
     * Ensure semantic links table exists
     * Called during initialization to handle cases where table wasn't created on activation
     */
    private function ensure_table_exists() {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        
        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        
        if (!$table_exists) {
            error_log('[SLMM Semantic Links DB] Table does not exist, creating now: ' . $table_name);
            $this->create_semantic_links_table();
        }
    }
    
    /**
     * Get full table name with WordPress prefix
     * 
     * @return string
     */
    private function get_table_name() {
        global $wpdb;
        return $wpdb->prefix . $this->table_name;
    }
    
    /**
     * Create semantic links table
     * Called on plugin activation
     */
    public function create_semantic_links_table() {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            sender_id bigint(20) unsigned NOT NULL,
            receiver_id bigint(20) unsigned NOT NULL,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY sender_id (sender_id),
            KEY receiver_id (receiver_id),
            KEY sender_receiver (sender_id, receiver_id),
            UNIQUE KEY unique_relationship (sender_id, receiver_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Update database version
        update_option('slmm_semantic_links_db_version', $this->db_version);
        
        error_log('[SLMM Semantic Links DB] Database table created: ' . $table_name);
    }
    
    /**
     * Check database version and update if needed
     */
    public function check_db_version() {
        $installed_version = get_option('slmm_semantic_links_db_version', '0.0.0');
        
        if (version_compare($installed_version, $this->db_version, '<')) {
            $this->create_semantic_links_table();
        }
    }
    
    /**
     * Create a new semantic link relationship
     * 
     * @param int $sender_id   Post ID of the sending page
     * @param int $receiver_id Post ID of the receiving page
     * @return int|false       Insert ID on success, false on failure
     */
    public function create_semantic_link($sender_id, $receiver_id) {
        global $wpdb;
        
        // Validate input
        $sender_id = absint($sender_id);
        $receiver_id = absint($receiver_id);
        
        if (!$sender_id || !$receiver_id) {
            error_log('[SLMM Semantic Links DB] Invalid IDs: sender=' . $sender_id . ', receiver=' . $receiver_id);
            return false;
        }
        
        // Prevent self-linking
        if ($sender_id === $receiver_id) {
            error_log('[SLMM Semantic Links DB] Prevented self-linking for post ID: ' . $sender_id);
            return false;
        }
        
        // Verify both posts exist
        if (!$this->post_exists($sender_id) || !$this->post_exists($receiver_id)) {
            error_log('[SLMM Semantic Links DB] One or both posts do not exist: ' . $sender_id . ', ' . $receiver_id);
            return false;
        }
        
        $table_name = $this->get_table_name();
        
        // Log attempt for debugging
        error_log('[SLMM Semantic Links DB] Attempting to create link: ' . $sender_id . ' → ' . $receiver_id . ' in table: ' . $table_name);
        
        $result = $wpdb->insert(
            $table_name,
            array(
                'sender_id' => $sender_id,
                'receiver_id' => $receiver_id,
                'created_date' => current_time('mysql')
            ),
            array('%d', '%d', '%s')
        );
        
        if ($result === false) {
            error_log('[SLMM Semantic Links DB] Failed to create link: ' . $wpdb->last_error . ' | Query: ' . $wpdb->last_query);
            return false;
        }
        
        $insert_id = $wpdb->insert_id;
        error_log('[SLMM Semantic Links DB] Successfully created semantic link: ' . $sender_id . ' → ' . $receiver_id . ' (ID: ' . $insert_id . ')');
        return $insert_id;
    }
    
    /**
     * Delete a semantic link relationship
     * 
     * @param int $sender_id   Post ID of the sending page
     * @param int $receiver_id Post ID of the receiving page
     * @return bool            True on success, false on failure
     */
    public function delete_semantic_link($sender_id, $receiver_id) {
        global $wpdb;
        
        $sender_id = absint($sender_id);
        $receiver_id = absint($receiver_id);
        
        if (!$sender_id || !$receiver_id) {
            return false;
        }
        
        $table_name = $this->get_table_name();
        
        $result = $wpdb->delete(
            $table_name,
            array(
                'sender_id' => $sender_id,
                'receiver_id' => $receiver_id
            ),
            array('%d', '%d')
        );
        
        if ($result !== false) {
            error_log('[SLMM Semantic Links DB] Deleted semantic link: ' . $sender_id . ' → ' . $receiver_id);
        }
        
        return $result !== false;
    }
    
    /**
     * Get all semantic links for a specific sender post
     * 
     * @param int $sender_id Post ID of the sending page
     * @return array         Array of receiver post objects with link data
     */
    public function get_semantic_links_for_sender($sender_id) {
        global $wpdb;
        
        $sender_id = absint($sender_id);
        
        if (!$sender_id) {
            return array();
        }
        
        $table_name = $this->get_table_name();
        
        $sql = $wpdb->prepare("
            SELECT sl.*, p.post_title, p.post_status, p.post_type
            FROM $table_name sl
            INNER JOIN {$wpdb->posts} p ON sl.receiver_id = p.ID
            WHERE sl.sender_id = %d
            AND p.post_status IN ('publish', 'draft', 'pending')
            ORDER BY sl.created_date DESC
        ", $sender_id);
        
        $results = $wpdb->get_results($sql);
        
        if (!$results) {
            return array();
        }
        
        // Format results with additional data
        foreach ($results as &$link) {
            $link->permalink = get_permalink($link->receiver_id);
            $link->edit_link = get_edit_post_link($link->receiver_id);
        }
        
        return $results;
    }
    
    /**
     * Check if a semantic link relationship exists
     * 
     * @param int $sender_id   Post ID of the sending page
     * @param int $receiver_id Post ID of the receiving page
     * @return bool            True if relationship exists, false otherwise
     */
    public function semantic_link_exists($sender_id, $receiver_id) {
        global $wpdb;
        
        $sender_id = absint($sender_id);
        $receiver_id = absint($receiver_id);
        
        if (!$sender_id || !$receiver_id) {
            return false;
        }
        
        $table_name = $this->get_table_name();
        
        $sql = $wpdb->prepare("
            SELECT COUNT(*) 
            FROM $table_name 
            WHERE sender_id = %d AND receiver_id = %d
        ", $sender_id, $receiver_id);
        
        $count = $wpdb->get_var($sql);
        
        return $count > 0;
    }
    
    /**
     * Get count of semantic links for a post (sent only)
     * 
     * @param int $post_id Post ID
     * @return int         Count of outgoing semantic links
     */
    public function get_semantic_links_count($post_id) {
        global $wpdb;
        
        $post_id = absint($post_id);
        
        if (!$post_id) {
            return 0;
        }
        
        $table_name = $this->get_table_name();
        
        // Count sent links only (outgoing semantic links)
        $sql = $wpdb->prepare("
            SELECT COUNT(*) 
            FROM $table_name 
            WHERE sender_id = %d
        ", $post_id);
        
        $count = $wpdb->get_var($sql);
        
        return absint($count);
    }
    
    /**
     * Delete all semantic links for a post (both sent and received)
     * Used when posts are deleted
     * 
     * @param int $post_id Post ID
     * @return bool        True on success, false on failure
     */
    public function delete_all_semantic_links_for_post($post_id) {
        global $wpdb;
        
        $post_id = absint($post_id);
        
        if (!$post_id) {
            return false;
        }
        
        $table_name = $this->get_table_name();
        
        // Delete all links where post is sender or receiver
        $result = $wpdb->query($wpdb->prepare("
            DELETE FROM $table_name 
            WHERE sender_id = %d OR receiver_id = %d
        ", $post_id, $post_id));
        
        if ($result !== false) {
            error_log('[SLMM Semantic Links DB] Deleted all semantic links for post: ' . $post_id);
        }
        
        return $result !== false;
    }
    
    /**
     * Verify that a post exists and is accessible
     * 
     * @param int $post_id Post ID to check
     * @return bool        True if post exists, false otherwise
     */
    private function post_exists($post_id) {
        $post = get_post($post_id);
        return $post && !empty($post->ID);
    }
    
    /**
     * Get suggested posts for semantic linking
     * Excludes posts that already have semantic links
     * 
     * @param int    $sender_id   Post ID of the sending page
     * @param string $search_term Optional search term
     * @param int    $limit       Maximum number of suggestions
     * @return array              Array of suggested post objects
     */
    public function get_semantic_link_suggestions($sender_id, $search_term = '', $limit = 20) {
        global $wpdb;
        
        $sender_id = absint($sender_id);
        $limit = absint($limit);
        
        if (!$sender_id) {
            return array();
        }
        
        $table_name = $this->get_table_name();
        
        // Build search query
        $search_sql = '';
        if (!empty($search_term)) {
            $search_term = '%' . $wpdb->esc_like(sanitize_text_field($search_term)) . '%';
            $search_sql = $wpdb->prepare(" AND p.post_title LIKE %s", $search_term);
        }
        
        $sql = $wpdb->prepare("
            SELECT p.ID, p.post_title, p.post_type, p.post_status
            FROM {$wpdb->posts} p
            WHERE p.post_status IN ('publish', 'draft', 'pending')
            AND p.post_type IN ('post', 'page')
            AND p.ID != %d
            AND p.ID NOT IN (
                SELECT receiver_id 
                FROM $table_name 
                WHERE sender_id = %d
            )
            $search_sql
            ORDER BY p.post_title ASC
            LIMIT %d
        ", $sender_id, $sender_id, $limit);
        
        $results = $wpdb->get_results($sql);
        
        if (!$results) {
            return array();
        }
        
        // Add additional data to results
        foreach ($results as &$post) {
            $post->permalink = get_permalink($post->ID);
            $post->edit_link = get_edit_post_link($post->ID);
        }
        
        return $results;
    }
}