<?php
// File: includes/interlinking/memory-leak-tester.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Memory Leak Testing Utilities for Interlinking Suite
 *
 * Provides comprehensive backend PHP testing for memory leaks,
 * performance bottlenecks, and server load scenarios.
 */
class SLMM_Memory_Leak_Tester {

    private $test_results = array();
    private $memory_snapshots = array();
    private $start_memory = 0;
    private $peak_memory = 0;

    public function __construct() {
        $this->start_memory = memory_get_usage(true);

        // Add testing endpoints
        add_action('wp_ajax_slmm_test_memory_leak', array($this, 'ajax_test_memory_leak'));
        add_action('wp_ajax_slmm_test_server_load', array($this, 'ajax_test_server_load'));
        add_action('wp_ajax_slmm_simulate_large_site', array($this, 'ajax_simulate_large_site'));
    }

    /**
     * Test memory usage during large site processing
     */
    public function ajax_test_memory_leak() {
        // Security checks
        if (!wp_verify_nonce($_POST['nonce'], 'slmm_memory_test_nonce')) {
            wp_die('Security check failed');
        }

        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $test_size = intval($_POST['test_size']) ?: 1000;
        $iterations = intval($_POST['iterations']) ?: 10;

        $this->memory_snapshots = array();

        error_log('[SLMM Memory Test] Starting memory leak test - Size: ' . $test_size . ', Iterations: ' . $iterations);

        // Baseline memory
        $baseline = $this->take_memory_snapshot('baseline');

        for ($i = 1; $i <= $iterations; $i++) {
            // Simulate large site processing
            $this->simulate_large_site_processing($test_size);

            // Take memory snapshot after each iteration
            $snapshot = $this->take_memory_snapshot('iteration_' . $i);

            // Calculate growth
            $growth = $snapshot['current'] - $baseline['current'];
            $growth_mb = round($growth / 1024 / 1024, 2);

            error_log('[SLMM Memory Test] Iteration ' . $i . ' - Memory Growth: ' . $growth_mb . 'MB');

            // Force garbage collection
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            // Check for concerning growth
            if ($growth > (50 * 1024 * 1024)) { // 50MB growth
                error_log('[SLMM Memory Test] WARNING: Significant memory growth detected');
                break;
            }
        }

        $analysis = $this->analyze_memory_patterns();

        wp_send_json_success(array(
            'snapshots' => $this->memory_snapshots,
            'analysis' => $analysis,
            'test_params' => array(
                'test_size' => $test_size,
                'iterations' => $iterations
            )
        ));
    }

    /**
     * Test server load under high traffic scenarios
     */
    public function ajax_test_server_load() {
        if (!wp_verify_nonce($_POST['nonce'], 'slmm_load_test_nonce')) {
            wp_die('Security check failed');
        }

        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $concurrent_requests = intval($_POST['concurrent_requests']) ?: 10;
        $request_duration = intval($_POST['duration']) ?: 30; // seconds

        error_log('[SLMM Load Test] Starting server load test - Concurrent: ' . $concurrent_requests . ', Duration: ' . $request_duration . 's');

        $start_time = microtime(true);
        $load_metrics = array();

        // Monitor system resources during load test
        while ((microtime(true) - $start_time) < $request_duration) {
            $metrics = array(
                'timestamp' => microtime(true),
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true),
                'load_average' => $this->get_system_load(),
                'mysql_processes' => $this->get_mysql_process_count(),
                'response_time' => $this->measure_interlinking_response_time()
            );

            $load_metrics[] = $metrics;

            // Small delay between measurements
            usleep(500000); // 0.5 seconds
        }

        $analysis = $this->analyze_load_patterns($load_metrics);

        wp_send_json_success(array(
            'metrics' => $load_metrics,
            'analysis' => $analysis,
            'test_duration' => microtime(true) - $start_time
        ));
    }

    /**
     * Simulate large site data processing
     */
    public function ajax_simulate_large_site() {
        if (!wp_verify_nonce($_POST['nonce'], 'slmm_simulate_nonce')) {
            wp_die('Security check failed');
        }

        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $page_count = intval($_POST['page_count']) ?: 5000;

        error_log('[SLMM Simulation] Simulating site with ' . $page_count . ' pages');

        $start_memory = memory_get_usage(true);
        $start_time = microtime(true);

        // Generate test data
        $test_data = $this->generate_test_site_data($page_count);

        // Process through interlinking algorithms
        $interlinking_suite = SLMM_Interlinking_Suite::get_instance();
        $processed_data = $interlinking_suite->ajax_generate_silo_grid();

        $end_memory = memory_get_usage(true);
        $end_time = microtime(true);

        $results = array(
            'pages_processed' => $page_count,
            'memory_used' => $end_memory - $start_memory,
            'processing_time' => $end_time - $start_time,
            'peak_memory' => memory_get_peak_usage(true),
            'memory_efficiency' => ($end_memory - $start_memory) / $page_count
        );

        error_log('[SLMM Simulation] Results: ' . json_encode($results));

        wp_send_json_success($results);
    }

    /**
     * Take memory snapshot with detailed information
     */
    private function take_memory_snapshot($label) {
        $snapshot = array(
            'label' => $label,
            'timestamp' => microtime(true),
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => $this->get_memory_limit(),
            'usage_percentage' => (memory_get_usage(true) / $this->get_memory_limit()) * 100
        );

        $this->memory_snapshots[] = $snapshot;

        return $snapshot;
    }

    /**
     * Simulate processing of large site
     */
    private function simulate_large_site_processing($size) {
        $pages = array();

        // Generate fake page data
        for ($i = 0; $i < $size; $i++) {
            $pages[] = array(
                'ID' => $i + 1,
                'post_title' => 'Test Page ' . ($i + 1),
                'post_content' => str_repeat('Test content ', 50), // ~500 chars
                'post_type' => 'page',
                'post_status' => 'publish',
                'post_parent' => rand(0, max(0, $i - 100)) // Random hierarchy
            );
        }

        // Simulate authority calculations
        foreach ($pages as &$page) {
            $page['authority_score'] = rand(1, 100) / 100;
            $page['link_count'] = rand(0, 20);
            $page['seo_metrics'] = array(
                'keyword_density' => rand(1, 10) / 100,
                'internal_links' => rand(0, 15),
                'external_links' => rand(0, 5)
            );
        }

        // Simulate hierarchical processing
        $hierarchy = $this->build_test_hierarchy($pages);

        // Simulate authority distribution
        $this->simulate_authority_distribution($hierarchy);

        unset($pages, $hierarchy); // Cleanup
    }

    /**
     * Build test hierarchy structure
     */
    private function build_test_hierarchy($pages) {
        $hierarchy = array();

        foreach ($pages as $page) {
            $parent_id = $page['post_parent'];
            if (!isset($hierarchy[$parent_id])) {
                $hierarchy[$parent_id] = array();
            }
            $hierarchy[$parent_id][] = $page;
        }

        return $hierarchy;
    }

    /**
     * Simulate authority distribution calculations
     */
    private function simulate_authority_distribution($hierarchy) {
        // Simulate PageRank-style calculations
        foreach ($hierarchy as $parent_id => $children) {
            $total_authority = 1.0;
            $child_count = count($children);

            if ($child_count > 0) {
                $authority_per_child = $total_authority / $child_count;

                foreach ($children as $child) {
                    // Simulate complex calculations
                    $calculated_authority = $authority_per_child * (1 + rand(-20, 20) / 100);
                    // Store result (simulation)
                }
            }
        }
    }

    /**
     * Analyze memory usage patterns for leaks
     */
    private function analyze_memory_patterns() {
        if (count($this->memory_snapshots) < 2) {
            return array('error' => 'Insufficient data for analysis');
        }

        $baseline = $this->memory_snapshots[0];
        $final = end($this->memory_snapshots);

        $total_growth = $final['current'] - $baseline['current'];
        $peak_growth = $final['peak'] - $baseline['peak'];

        $analysis = array(
            'memory_leak_detected' => $total_growth > (10 * 1024 * 1024), // 10MB threshold
            'total_memory_growth' => $total_growth,
            'total_growth_mb' => round($total_growth / 1024 / 1024, 2),
            'peak_memory_growth' => $peak_growth,
            'peak_growth_mb' => round($peak_growth / 1024 / 1024, 2),
            'usage_percentage_start' => $baseline['usage_percentage'],
            'usage_percentage_end' => $final['usage_percentage'],
            'snapshots_analyzed' => count($this->memory_snapshots)
        );

        // Add recommendations
        if ($analysis['memory_leak_detected']) {
            $analysis['recommendations'] = array(
                'Potential memory leak detected',
                'Review variable cleanup in processing loops',
                'Check for unreleased references',
                'Consider implementing chunk processing'
            );
        } else {
            $analysis['recommendations'] = array(
                'Memory usage appears stable',
                'Continue monitoring during production use'
            );
        }

        return $analysis;
    }

    /**
     * Get system load average (Unix systems)
     */
    private function get_system_load() {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return $load[0]; // 1-minute average
        }
        return null;
    }

    /**
     * Get MySQL process count
     */
    private function get_mysql_process_count() {
        global $wpdb;

        try {
            $result = $wpdb->get_var("SHOW STATUS LIKE 'Threads_connected'");
            return intval($result);
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Measure interlinking suite response time
     */
    private function measure_interlinking_response_time() {
        $start = microtime(true);

        // Simulate typical interlinking operation
        $test_data = array(
            'post_type_filter' => 'page',
            'max_depth' => 3,
            'include_posts' => true
        );

        // Measure time for data generation (simplified)
        $pages = get_pages(array(
            'number' => 100,
            'sort_column' => 'menu_order,post_title'
        ));

        $end = microtime(true);

        return ($end - $start) * 1000; // Return in milliseconds
    }

    /**
     * Analyze load testing patterns
     */
    private function analyze_load_patterns($metrics) {
        if (empty($metrics)) {
            return array('error' => 'No metrics to analyze');
        }

        $memory_values = array_column($metrics, 'memory_usage');
        $response_times = array_column($metrics, 'response_time');

        $analysis = array(
            'avg_memory_usage' => array_sum($memory_values) / count($memory_values),
            'max_memory_usage' => max($memory_values),
            'avg_response_time' => array_sum($response_times) / count($response_times),
            'max_response_time' => max($response_times),
            'samples_collected' => count($metrics)
        );

        // Performance thresholds
        $analysis['memory_pressure'] = $analysis['max_memory_usage'] > (256 * 1024 * 1024); // 256MB
        $analysis['slow_responses'] = $analysis['max_response_time'] > 2000; // 2 seconds

        return $analysis;
    }

    /**
     * Generate test site data
     */
    private function generate_test_site_data($count) {
        $data = array();

        for ($i = 1; $i <= $count; $i++) {
            $data[] = array(
                'id' => $i,
                'title' => 'Test Page ' . $i,
                'content' => str_repeat('Sample content for SEO analysis. ', 20),
                'type' => ($i % 10 === 0) ? 'post' : 'page',
                'parent' => ($i > 1) ? rand(1, max(1, $i - 50)) : 0
            );
        }

        return $data;
    }

    /**
     * Get PHP memory limit in bytes
     */
    private function get_memory_limit() {
        $memory_limit = ini_get('memory_limit');

        if (preg_match('/^(\d+)(.)$/', $memory_limit, $matches)) {
            $number = $matches[1];
            $suffix = strtoupper($matches[2]);

            switch ($suffix) {
                case 'G': return $number * 1024 * 1024 * 1024;
                case 'M': return $number * 1024 * 1024;
                case 'K': return $number * 1024;
                default: return intval($memory_limit);
            }
        }

        return 128 * 1024 * 1024; // 128MB default
    }
}