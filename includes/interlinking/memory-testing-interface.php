<?php
// File: includes/interlinking/memory-testing-interface.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Memory Testing Interface for Interlinking Suite
 *
 * Provides admin interface for running memory leak and performance tests
 */
class SLMM_Memory_Testing_Interface {

    public function __construct() {
        // Only load in debug mode
        if (!defined('WP_DEBUG') || !WP_DEBUG) {
            return;
        }

        add_action('admin_menu', array($this, 'add_testing_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_testing_assets'));
    }

    /**
     * Add testing menu to WordPress admin
     */
    public function add_testing_menu() {
        add_management_page(
            'SLMM Memory Testing',
            'SLMM Memory Tests',
            'manage_options',
            'slmm-memory-testing',
            array($this, 'render_testing_page')
        );
    }

    /**
     * Enqueue testing page assets
     */
    public function enqueue_testing_assets($hook) {
        if ($hook !== 'tools_page_slmm-memory-testing') {
            return;
        }

        wp_enqueue_script('jquery');
        wp_localize_script('jquery', 'slmmMemoryTest', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'memory_nonce' => wp_create_nonce('slmm_memory_test_nonce'),
            'load_nonce' => wp_create_nonce('slmm_load_test_nonce'),
            'simulate_nonce' => wp_create_nonce('slmm_simulate_nonce')
        ));
    }

    /**
     * Render the testing interface page
     */
    public function render_testing_page() {
        ?>
        <div class="wrap">
            <h1>SLMM Interlinking Suite - Memory & Performance Testing</h1>

            <div class="notice notice-warning">
                <p><strong>Warning:</strong> These tests are resource-intensive and should only be run on development/staging environments.</p>
            </div>

            <!-- Memory Leak Testing -->
            <div class="card">
                <h2>Memory Leak Testing</h2>
                <p>Test for memory leaks during repeated large site processing operations.</p>

                <table class="form-table">
                    <tr>
                        <th scope="row">Test Size</th>
                        <td>
                            <input type="number" id="memory-test-size" value="1000" min="100" max="10000" />
                            <p class="description">Number of pages to simulate per iteration</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Iterations</th>
                        <td>
                            <input type="number" id="memory-test-iterations" value="10" min="5" max="50" />
                            <p class="description">Number of test iterations to run</p>
                        </td>
                    </tr>
                </table>

                <p>
                    <button type="button" id="run-memory-test" class="button button-primary">Run Memory Leak Test</button>
                    <span id="memory-test-status" style="margin-left: 15px;"></span>
                </p>

                <div id="memory-test-results" style="display: none;">
                    <h3>Test Results</h3>
                    <pre id="memory-results-data"></pre>
                </div>
            </div>

            <!-- Server Load Testing -->
            <div class="card">
                <h2>Server Load Testing</h2>
                <p>Monitor server performance under simulated high-traffic conditions.</p>

                <table class="form-table">
                    <tr>
                        <th scope="row">Test Duration</th>
                        <td>
                            <input type="number" id="load-test-duration" value="30" min="10" max="300" />
                            <p class="description">Test duration in seconds</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Concurrent Requests</th>
                        <td>
                            <input type="number" id="load-test-concurrent" value="10" min="1" max="100" />
                            <p class="description">Simulated concurrent request load</p>
                        </td>
                    </tr>
                </table>

                <p>
                    <button type="button" id="run-load-test" class="button button-primary">Run Load Test</button>
                    <span id="load-test-status" style="margin-left: 15px;"></span>
                </p>

                <div id="load-test-results" style="display: none;">
                    <h3>Load Test Results</h3>
                    <pre id="load-results-data"></pre>
                </div>
            </div>

            <!-- Large Site Simulation -->
            <div class="card">
                <h2>Large Site Simulation</h2>
                <p>Test performance with very large site datasets.</p>

                <table class="form-table">
                    <tr>
                        <th scope="row">Page Count</th>
                        <td>
                            <input type="number" id="simulate-page-count" value="5000" min="1000" max="50000" />
                            <p class="description">Number of pages to simulate</p>
                        </td>
                    </tr>
                </table>

                <p>
                    <button type="button" id="run-simulation" class="button button-primary">Run Simulation</button>
                    <span id="simulation-status" style="margin-left: 15px;"></span>
                </p>

                <div id="simulation-results" style="display: none;">
                    <h3>Simulation Results</h3>
                    <pre id="simulation-data"></pre>
                </div>
            </div>

            <!-- System Information -->
            <div class="card">
                <h2>Current System Information</h2>
                <table class="widefat">
                    <tr>
                        <td><strong>PHP Memory Limit:</strong></td>
                        <td><?php echo ini_get('memory_limit'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Current Memory Usage:</strong></td>
                        <td><?php echo round(memory_get_usage(true) / 1024 / 1024, 2); ?> MB</td>
                    </tr>
                    <tr>
                        <td><strong>Peak Memory Usage:</strong></td>
                        <td><?php echo round(memory_get_peak_usage(true) / 1024 / 1024, 2); ?> MB</td>
                    </tr>
                    <tr>
                        <td><strong>Max Execution Time:</strong></td>
                        <td><?php echo ini_get('max_execution_time'); ?> seconds</td>
                    </tr>
                    <tr>
                        <td><strong>WordPress Post Count:</strong></td>
                        <td><?php echo wp_count_posts()->publish; ?> published posts</td>
                    </tr>
                    <tr>
                        <td><strong>WordPress Page Count:</strong></td>
                        <td><?php echo wp_count_posts('page')->publish; ?> published pages</td>
                    </tr>
                </table>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            // Memory Leak Test
            $('#run-memory-test').click(function() {
                var button = $(this);
                var status = $('#memory-test-status');
                var results = $('#memory-test-results');

                button.prop('disabled', true);
                status.html('<span style="color: orange;">Running memory leak test...</span>');

                $.post(slmmMemoryTest.ajax_url, {
                    action: 'slmm_test_memory_leak',
                    nonce: slmmMemoryTest.memory_nonce,
                    test_size: $('#memory-test-size').val(),
                    iterations: $('#memory-test-iterations').val()
                }, function(response) {
                    button.prop('disabled', false);

                    if (response.success) {
                        status.html('<span style="color: green;">Test completed!</span>');
                        $('#memory-results-data').text(JSON.stringify(response.data, null, 2));
                        results.show();
                    } else {
                        status.html('<span style="color: red;">Test failed: ' + response.data + '</span>');
                    }
                }).fail(function() {
                    button.prop('disabled', false);
                    status.html('<span style="color: red;">Test failed - server error</span>');
                });
            });

            // Load Test
            $('#run-load-test').click(function() {
                var button = $(this);
                var status = $('#load-test-status');
                var results = $('#load-test-results');

                button.prop('disabled', true);
                status.html('<span style="color: orange;">Running load test...</span>');

                $.post(slmmMemoryTest.ajax_url, {
                    action: 'slmm_test_server_load',
                    nonce: slmmMemoryTest.load_nonce,
                    concurrent_requests: $('#load-test-concurrent').val(),
                    duration: $('#load-test-duration').val()
                }, function(response) {
                    button.prop('disabled', false);

                    if (response.success) {
                        status.html('<span style="color: green;">Load test completed!</span>');
                        $('#load-results-data').text(JSON.stringify(response.data, null, 2));
                        results.show();
                    } else {
                        status.html('<span style="color: red;">Load test failed: ' + response.data + '</span>');
                    }
                }).fail(function() {
                    button.prop('disabled', false);
                    status.html('<span style="color: red;">Load test failed - server error</span>');
                });
            });

            // Simulation Test
            $('#run-simulation').click(function() {
                var button = $(this);
                var status = $('#simulation-status');
                var results = $('#simulation-results');

                button.prop('disabled', true);
                status.html('<span style="color: orange;">Running simulation...</span>');

                $.post(slmmMemoryTest.ajax_url, {
                    action: 'slmm_simulate_large_site',
                    nonce: slmmMemoryTest.simulate_nonce,
                    page_count: $('#simulate-page-count').val()
                }, function(response) {
                    button.prop('disabled', false);

                    if (response.success) {
                        status.html('<span style="color: green;">Simulation completed!</span>');
                        $('#simulation-data').text(JSON.stringify(response.data, null, 2));
                        results.show();
                    } else {
                        status.html('<span style="color: red;">Simulation failed: ' + response.data + '</span>');
                    }
                }).fail(function() {
                    button.prop('disabled', false);
                    status.html('<span style="color: red;">Simulation failed - server error</span>');
                });
            });
        });
        </script>

        <style>
        .card {
            background: #fff;
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }

        .card h2 {
            margin-top: 0;
        }

        pre {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 10px;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }

        .widefat td {
            padding: 8px 10px;
        }
        </style>
        <?php
    }
}

// Initialize only in debug mode
if (defined('WP_DEBUG') && WP_DEBUG) {
    new SLMM_Memory_Testing_Interface();
}