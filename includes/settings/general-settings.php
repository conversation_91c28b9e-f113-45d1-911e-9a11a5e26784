<?php
// File: includes/settings/general-settings.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}
add_action('admin_menu', 'admin_menu_add_external_links_as_submenu');
add_action('admin_head', 'admin_menu_add_external_links_as_submenu_jquery');

function admin_menu_add_external_links_as_submenu() {
    global $submenu;

    $menu_slug = "externallink"; // used as "key" in menus
    $menu_pos = 2; // whatever position you want your menu to appear

    // create the top level menu with the superhero dashicon
    add_menu_page( 
        'external_link',              // Page title
        'SLMM SEO',                   // Menu title
        'read',                       // Capability
        $menu_slug,                   // Menu slug
        '',                           // Callback function
        'dashicons-superhero',        // Dashicon class for menu icon
        $menu_pos                     // Menu position
    );

    // add the external links to the slug you used when adding the top level menu
    $submenu[$menu_slug][] = array('<div id="newtab1"><span class="dashicons dashicons-admin-generic"></span> SLMM SEO Settings</div>', 'manage_options', '/wp-admin/admin.php?page=chatgpt-generator-settings');
    $submenu[$menu_slug][] = array('<div id="newtab2"><span class="dashicons dashicons-admin-links"></span> Interlinking Suite</div>', 'manage_options', '/wp-admin/admin.php?page=slmm-interlinking-suite');
    $submenu[$menu_slug][] = array('<div id="newtab3"><span class="dashicons dashicons-clock"></span> Content Freshness</div>', 'manage_options', '/wp-admin/admin.php?page=content-freshness'); 
 
    $submenu[$menu_slug][] = array('<div id="newtab5"><span class="dashicons dashicons-editor-quote"></span> SLMM GPT Prompts</div>', 'manage_options', '/wp-admin/admin.php?page=slmm-gpt-prompts'); 
}

function admin_menu_add_external_links_as_submenu_jquery() {
    ?>
    <script type="text/javascript">
        jQuery(document).ready(function($) {   
            $('#newtab1').parent().attr('target','_self');
            $('#newtab2').parent().attr('target','_self');
            $('#newtab3').parent().attr('target','_self');  
            $('#newtab5').parent().attr('target', '_self');

            // Add some CSS to align the dashicons with the text
            $('.toplevel_page_externallink .wp-submenu li a').css({
                'display': 'flex',
                'align-items': 'center',
            });
            $('.toplevel_page_externallink .wp-submenu li a').css({
                'min-width': '160px'
            });
            $('.toplevel_page_externallink .wp-submenu li a .dashicons').css({
                'margin-right': '5px'
            });
        

        });
        
    </script>
    <?php
}
class SLMM_General_Settings {
    private $options;
    private $visibility_controller;

    public function init() {
        static $initialized = false;
        if ($initialized) return;
        $initialized = true;
    
        add_action('admin_menu', array($this, 'add_menu_items'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_init', array($this, 'handle_export_import'));
        add_action('admin_init', array($this, 'migrate_old_settings'));
        add_action('init', array($this, 'init_page_delete'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_keyboard_shortcuts'));
        add_action('wp_ajax_keyboard_shortcut_save', array($this, 'handle_keyboard_shortcut_save'));
        add_action('wp_ajax_refresh_openai_models', array($this, 'handle_refresh_openai_models'));
        add_action('wp_ajax_slmm_get_provider_models', array($this, 'handle_get_provider_models'));
        add_action('wp_ajax_slmm_refresh_models', array($this, 'handle_refresh_models'));
        add_action('wp_ajax_slmm_clear_model_cache', array($this, 'handle_clear_model_cache'));
        add_action('wp_ajax_slmm_save_model_selection', array($this, 'handle_save_model_selection'));
        add_action('wp_ajax_slmm_search_replace', array($this, 'handle_search_replace'));
        add_action('wp_ajax_slmm_get_matched_rows', array($this, 'handle_get_matched_rows'));
        add_action('wp_ajax_slmm_get_tables', array($this, 'handle_get_tables'));
        add_action('wp_ajax_slmm_regenerate_emergency_key', array($this, 'handle_regenerate_emergency_key'));
        add_action('admin_enqueue_scripts', array($this, 'add_development_mode_styling'));
        add_action('wp_enqueue_scripts', array($this, 'add_development_mode_styling'));
    }

    public function add_menu_items() {
        global $submenu;
    
        // Add the page without adding a visible menu item
        $hookname = add_submenu_page(
            null,                           // Don't add to any menu
            'ChatGPT Generator Settings',   // Page title
            'ChatGPT Generator',            // Menu title (unused)
            'manage_options',               // Capability
            'chatgpt-generator-settings',   // Menu slug
            array($this, 'render_settings_page') // Callback function
        );
    
        // Remove from options submenu if it was added there
        if (isset($submenu['options-general.php'])) {
            foreach ($submenu['options-general.php'] as $key => $item) {
                if ($item[2] === 'chatgpt-generator-settings') {
                    unset($submenu['options-general.php'][$key]);
                    break;
                }
            }
        }
    }

    public function register_settings() {
        register_setting('chatgpt_generator_settings', 'chatgpt_generator_options', array($this, 'sanitize_options'));
        register_setting(
            'chatgpt_generator_settings',
            'enable_page_delete',
            array(
                'type' => 'boolean',
                'default' => false
            )
        );
        register_setting(
            'chatgpt_generator_settings',
            'enable_keyboard_shortcuts',
            array(
                'type' => 'boolean',
                'default' => false
            )
        );
        register_setting(
            'chatgpt_generator_settings',
            'auto_empty_trash',
            array(
                'type' => 'boolean',
                'default' => false
            )
        );
        register_setting(
            'chatgpt_generator_settings',
            'page_delete_post_types',
            array(
                'type' => 'array',
                'default' => array('post', 'page')
            )
        );
        register_setting(
            'chatgpt_generator_settings',
            'page_delete_rate_limit',
            array(
                'type' => 'integer',
                'default' => 10
            )
        );
        register_setting(
            'chatgpt_generator_settings',
            'seo_overview_post_types',
            array(
                'type' => 'array',
                'default' => array('post', 'page')
            )
        );
        register_setting(
            'chatgpt_generator_settings',
            'auto_segment_top_percent',
            array(
                'type' => 'integer',
                'default' => 33
            )
        );
        register_setting(
            'chatgpt_generator_settings',
            'auto_segment_bottom_percent',
            array(
                'type' => 'integer',
                'default' => 67
            )
        );
        
        // Register AI interlinking prompts options
        register_setting(
            'chatgpt_generator_settings',
            'slmm_interlinking_prompts',
            array(
                'type' => 'array',
                'default' => array()
            )
        );
        
        // Register AI interlinking rules options
        register_setting(
            'chatgpt_generator_settings',
            'slmm_interlinking_rules',
            array(
                'type' => 'array',
                'default' => array()
            )
        );
        
        // Register page summarization settings
        register_setting(
            'chatgpt_generator_settings',
            'slmm_page_summarization_settings',
            array(
                'type' => 'array',
                'default' => array()
            )
        );

        $sections = array(
            'api_keys' => 'API Keys',
            'prompts' => 'Prompts',
            'business_info' => 'Business Information',
            'models' => 'AI Models',
            'protected_words' => 'Protected Words',
            'features' => 'Plugin Features'
        );

        foreach ($sections as $section_id => $section_title) {
            add_settings_section(
                $section_id,
                $section_title,
                array($this, "render_{$section_id}_section"),
                'chatgpt-generator-settings'
            );
        }

        $fields = array(
            'api_keys' => array(
                'openai_api_key' => 'OpenAI API Key',
                'openrouter_api_key' => 'OpenRouter API Key',
            ),
            'prompts' => array(
                'title_prompt' => 'Title Prompt',
                'description_prompt' => 'Description Prompt',
            ),
            'business_info' => array(
                'business_name' => 'Business Name',
                'phone_number' => 'Phone Number',
            ),
            'models' => array(
                'model_for_title' => 'Title Generation Model',
                'model_for_description' => 'Description Generation Model',
            ),
            'protected_words' => array(
                'protected_words_list' => 'Protected Words List',
            ),
            'features' => array(
                // Features are now handled in render_feature_callbacks with proper grouping
            ),
        );

        foreach ($fields as $section => $section_fields) {
            foreach ($section_fields as $field => $label) {
                add_settings_field(
                    $field,
                    $label,
                    array($this, 'render_field'),
                    'chatgpt-generator-settings',
                    $section,
                    array('field' => $field, 'label' => $label)
                );
            }
        }

        add_settings_field(
            'enable_page_delete',
            __('Enable Page Delete', 'chatgpt-generator'),
            array($this, 'enable_page_delete_callback'),
            'chatgpt-generator-settings',
            'features'
        );

        add_settings_field(
            'enable_keyboard_shortcuts',
            __('Enable Keyboard Shortcuts', 'chatgpt-generator'),
            array($this, 'enable_keyboard_shortcuts_callback'),
            'chatgpt-generator-settings',
            'features'
        );

        add_settings_field(
            'page_delete_post_types',
            __('Allowed Post Types for Delete', 'chatgpt-generator'),
            array($this, 'page_delete_post_types_callback'),
            'chatgpt-generator-settings',
            'features'
        );

        add_settings_field(
            'page_delete_rate_limit',
            __('Delete Rate Limit', 'chatgpt-generator'),
            array($this, 'page_delete_rate_limit_callback'),
            'chatgpt-generator-settings',
            'features'
        );

        add_settings_field(
            'visibility_enabled',
            __('Enable Plugin Visibility Settings', 'chatgpt-generator'),
            array($this, 'visibility_enabled_callback'),
            'chatgpt-generator-settings',
            'features'
        );

        add_settings_field(
            'authorized_admins',
            __('Authorized Admin Usernames', 'chatgpt-generator'),
            array($this, 'authorized_admins_callback'),
            'chatgpt-generator-settings',
            'features'
        );

        add_settings_field(
            'enable_debug_logging',
            __('Enable Debug Logging', 'chatgpt-generator'),
            array($this, 'enable_debug_logging_callback'),
            'chatgpt-generator-settings',
            'features'
        );

        add_settings_field(
            'enable_development_mode',
            __('Enable Development Mode', 'chatgpt-generator'),
            array($this, 'enable_development_mode_callback'),
            'chatgpt-generator-settings',
            'features'
        );
    }

    public function sanitize_options($input) {
        $sanitized_input = array();
        $old_options = get_option('chatgpt_generator_options', array());
    
        foreach ($input as $key => $value) {
            switch ($key) {
                case 'openai_api_key':
                    $api_key = sanitize_text_field($value);
                    if (!empty($api_key)) {
                        // Validate OpenAI API key format (starts with sk-)
                        if (strpos($api_key, 'sk-') !== 0) {
                            add_settings_error('chatgpt_generator_options', 'invalid_openai_api_key', 
                                'Invalid OpenAI API key format. API key should start with "sk-"', 'error');
                        } else {
                            // Optionally test the API key with a simple API call
                            $sanitized_input[$key] = $api_key;
                        }
                    } else {
                        $sanitized_input[$key] = $api_key;
                    }
                    break;
                case 'openrouter_api_key':
                    $api_key = sanitize_text_field($value);
                    if (!empty($api_key)) {
                        // Validate OpenRouter API key format (usually starts with sk-or-)
                        if (strpos($api_key, 'sk-or-') !== 0) {
                            add_settings_error('chatgpt_generator_options', 'invalid_openrouter_api_key', 
                                'Invalid OpenRouter API key format. API key should start with "sk-or-"', 'error');
                        } else {
                            $sanitized_input[$key] = $api_key;
                        }
                    } else {
                        $sanitized_input[$key] = $api_key;
                    }
                    break;
                case 'title_prompt':
                case 'description_prompt':
                    $sanitized_input[$key] = wp_kses_post($value);
                    break;
                case 'business_name':
                case 'phone_number':
                    $sanitized_input[$key] = sanitize_text_field($value);
                    break;
                case 'model_for_title':
                case 'model_for_description':
                    // Get the provider for this model field
                    $provider_field = $key . '_provider';
                    $provider = isset($input[$provider_field]) ? $input[$provider_field] : 'openai';
                    
                    // Validate based on the selected provider
                    if ($provider === 'openrouter') {
                        // For OpenRouter, we'll allow any model ID as it's fetched from their API
                        // Just sanitize for basic security
                        $sanitized_input[$key] = sanitize_text_field($value);
                    } else {
                        // For OpenAI, validate against available models
                        $available_models = array_keys($this->get_openai_models());
                        $sanitized_input[$key] = in_array($value, $available_models) ? $value : 'gpt-4o';
                    }
                    break;
                case 'model_for_title_provider':
                case 'model_for_description_provider':
                    // Validate provider selection
                    $sanitized_input[$key] = in_array($value, ['openai', 'openrouter']) ? $value : 'openai';
                    break;
                case 'protected_words_list':
                    // Sanitize each line and remove empty lines
                    $lines = array_filter(array_map('trim', explode("\n", $value)));
                    $sanitized_input[$key] = implode("\n", array_map('sanitize_text_field', $lines));
                    break;
                            case 'enable_checklist':
            case 'enable_seo_overview':
            case 'visibility_enabled':
                $sanitized_input[$key] = isset($value) ? (bool)$value : false;
                break;
            case 'authorized_admins':
                // Handle array of admin usernames
                if (is_array($value)) {
                    $sanitized_input[$key] = array_map('sanitize_text_field', array_filter($value));
                } else {
                    $sanitized_input[$key] = array();
                }
                break;
            case 'development_mode_color':
                // Validate hex color
                $sanitized_color = sanitize_hex_color($value);
                if (!$sanitized_color) {
                    // If invalid hex color, fall back to default
                    $sanitized_input[$key] = '#7a39e8';
                } else {
                    $sanitized_input[$key] = $sanitized_color;
                }
                break;
                default:
                    $sanitized_input[$key] = sanitize_text_field($value);
            }
        }
    
        // Preserve the API keys if they're not being updated
        if (!isset($sanitized_input['openai_api_key']) && isset($old_options['openai_api_key'])) {
            $sanitized_input['openai_api_key'] = $old_options['openai_api_key'];
        }
        if (!isset($sanitized_input['openrouter_api_key']) && isset($old_options['openrouter_api_key'])) {
            $sanitized_input['openrouter_api_key'] = $old_options['openrouter_api_key'];
        }
        
        // For checkbox options, we need to explicitly set them to false if they're not in the input
        // This ensures they can be turned off
        $checkbox_options = ['enable_checklist', 'enable_seo_overview', 'visibility_enabled', 'enable_debug_logging', 'enable_development_mode'];
        foreach ($checkbox_options as $option) {
            if (!isset($input[$option])) {
                $sanitized_input[$option] = false;
            }
        }
        
        // Handle debug logging categories
        if (isset($input['debug_logging_categories']) && is_array($input['debug_logging_categories'])) {
            $sanitized_input['debug_logging_categories'] = array();
            
            // Define valid categories (matches JavaScript debugConfig.categories)
            $valid_categories = array(
                'Direct Editor', 'Dashboard', 'ACF Title', 'Regular Title', 
                'Link Tracking', 'Important Pages', 'Silo Nav', 'Copy Links', 
                'Delete Semantic', 'Importance', 'Copy', 'Popup Management', 
                'Resize', 'ACF Direct Editor', 'Link Popup', 'ACF Integration',
                'Initialization', 'Tree Loading', 'Mouseover', 'Completion',
                'Surgical Update', 'Link Overlay', 'Notes', 'Visual Highlighting',
                'Link Connections', 'Fallback Matching', 'Debug Analysis',
                'Lorem Detector', 'Bricks Builder', 'GPT Prompt', 
                'Broken Link Detector', 'SEO Overview', 'SEO Tools',
                'Prompt Execution', 'Notes Diagnostics', 'QuickBulk D3'
            );
            
            // Only allow valid categories and sanitize as boolean
            foreach ($valid_categories as $category) {
                $sanitized_input['debug_logging_categories'][$category] = 
                    isset($input['debug_logging_categories'][$category]) ? true : false;
            }
        }
    
        return $sanitized_input;
    }

    public function render_api_keys_section() {
        echo '<p>Enter your API keys for OpenAI services.</p>';
    }

    public function render_prompts_section() {
        echo '<p>Configure your prompts for different content types.</p>';
    }

    public function render_business_info_section() {
        echo '<p>Enter your business information.</p>';
    }

    public function render_models_section() {
        echo '<p>Select AI models for different content types.</p>';
    }

    public function render_protected_words_section() {
        echo '<p>Enter words (one per line) that should always maintain their capitalization. This includes names of countries, states, brands, etc.</p>';
    }

    public function render_features_section() {
        echo '<p>Enable or disable specific plugin features.</p>';
    }

    public function render_field($args) {
        $options = get_option('chatgpt_generator_options', array());
        $field = $args['field'];
        $label = $args['label'];
        $value = isset($options[$field]) ? $options[$field] : '';
    
        echo '<label class="slmm-field-label">' . esc_html($label) . '</label>';
        
        switch ($field) {
            case 'openai_api_key':
                echo '<div class="slmm-input-wrapper">';
                echo "<input type='password' id='$field' name='chatgpt_generator_options[$field]' value='" . esc_attr($value) . "' class='slmm-input slmm-password-input' placeholder='sk-...'>";
                echo '<span class="slmm-input-icon">🔑</span>';
                echo '</div>';
                echo "<p class='slmm-field-description'>Enter your OpenAI API key here. Get one at <a href='https://platform.openai.com/settings/organization/api-keys' target='_blank' class='slmm-link'>platform.openai.com</a></p>";
                break;
            case 'openrouter_api_key':
                echo '<div class="slmm-input-wrapper">';
                echo "<input type='password' id='$field' name='chatgpt_generator_options[$field]' value='" . esc_attr($value) . "' class='slmm-input slmm-password-input' placeholder='sk-or-...'>";
                echo '<span class="slmm-input-icon">🔑</span>';
                echo '</div>';
                echo "<p class='slmm-field-description'>Enter your OpenRouter API key here. Get one at <a href='https://openrouter.ai/settings/keys' target='_blank' class='slmm-link'>openrouter.ai</a></p>";
                break;
            case 'title_prompt':
            case 'description_prompt':
                echo '<div class="slmm-textarea-wrapper">';
                echo "<textarea id='$field' name='chatgpt_generator_options[$field]' rows='4' class='slmm-textarea' placeholder='Enter your custom prompt here...'>" . esc_textarea($value) . "</textarea>";
                echo '</div>';
                break;
            case 'business_name':
                echo '<div class="slmm-input-wrapper">';
                echo "<input type='text' id='$field' name='chatgpt_generator_options[$field]' value='" . esc_attr($value) . "' class='slmm-input' placeholder='Your Business Name'>";
                echo '<span class="slmm-input-icon">🏢</span>';
                echo '</div>';
                break;
            case 'phone_number':
                echo '<div class="slmm-input-wrapper">';
                echo "<input type='text' id='$field' name='chatgpt_generator_options[$field]' value='" . esc_attr($value) . "' class='slmm-input' placeholder='+****************'>";
                echo '<span class="slmm-input-icon">📞</span>';
                echo '</div>';
                break;
            case 'model_for_title_provider':
            case 'model_for_description_provider':
                // These are handled within the model dropdown rendering, skip them
                return;
            case 'model_for_title':
            case 'model_for_description':
                echo '<div class="slmm-model-selector-wrapper">';
                $this->render_model_dropdown($field, $value);
                echo '</div>';
                break;
            case 'protected_words_list':
                echo '<div class="slmm-textarea-wrapper">';
                echo '<textarea id="' . esc_attr($field) . '" name="chatgpt_generator_options[' . esc_attr($field) . ']" rows="10" class="slmm-textarea slmm-protected-words" placeholder="Apple&#10;Google&#10;WordPress&#10;OpenAI&#10;...">' . esc_textarea($value) . '</textarea>';
                echo '</div>';
                echo '<p class="slmm-field-description">Enter one word per line. These words will maintain their capitalization across the entire website.</p>';
                break;
            case 'enable_checklist':
                echo '<div class="slmm-toggle-wrapper">';
                echo "<input type='checkbox' id='{$field}' name='chatgpt_generator_options[{$field}]' value='1' class='slmm-toggle' " . checked($value, true, false) . ">";
                echo "<label for='{$field}' class='slmm-toggle-label'>";
                echo "<span class='slmm-toggle-slider'></span>";
                echo "<span class='slmm-toggle-text'>Enable SEO Checklist</span>";
                echo "</label>";
                echo '</div>';
                break;
            case 'enable_seo_overview':
                echo '<div class="slmm-toggle-wrapper">';
                echo "<input type='checkbox' id='{$field}' name='chatgpt_generator_options[{$field}]' value='1' class='slmm-toggle' " . checked($value, true, false) . ">";
                echo "<label for='{$field}' class='slmm-toggle-label'>";
                echo "<span class='slmm-toggle-slider'></span>";
                echo "<span class='slmm-toggle-text'>Enable SEO Overview Meta Box</span>";
                echo "</label>";
                echo '</div>';
                echo "<p class='slmm-field-description'>Shows a meta box with SEO metrics in the editor sidebar. Can be toggled from Screen Options.</p>";
                break;
            case 'seo_overview_post_types':
                $this->seo_overview_post_types_callback();
                break;
            case 'development_mode':
                $enable_development_mode = isset($options['enable_development_mode']) ? $options['enable_development_mode'] : false;
                $development_mode_color = isset($options['development_mode_color']) ? $options['development_mode_color'] : '#7a39e8';
                
                echo '<div class="slmm-toggle-wrapper">';
                echo "<input type='checkbox' id='enable_development_mode' name='chatgpt_generator_options[enable_development_mode]' value='1' class='slmm-toggle' " . checked($enable_development_mode, true, false) . ">";
                echo "<label for='enable_development_mode' class='slmm-toggle-label'>";
                echo "<span class='slmm-toggle-slider'></span>";
                echo "<span class='slmm-toggle-text'>Enable Development Mode</span>";
                echo "</label>";
                echo '</div>';
                echo "<p class='slmm-field-description'>Colorizes the WordPress admin bar to help distinguish between different sites in multiple tabs.</p>";
                
                echo '<div class="slmm-color-picker-wrapper" style="margin-top: 15px;">';
                echo '<label for="development_mode_color" class="slmm-field-label">Admin Bar Color</label>';
                echo '<div class="slmm-input-wrapper">';
                echo "<input type='text' id='development_mode_color' name='chatgpt_generator_options[development_mode_color]' value='" . esc_attr($development_mode_color) . "' class='slmm-color-picker' />";
                echo '</div>';
                echo '</div>';
                break;
        }
    }

    /**
     * Fetch available OpenAI models from the API
     */
    private function get_openai_models() {
        $options = get_option('chatgpt_generator_options', array());
        $api_key = isset($options['openai_api_key']) ? $options['openai_api_key'] : '';
        
        if (empty($api_key)) {
            return $this->get_fallback_models();
        }

        // Check if we have cached models that are still valid (cache for 1 hour)
        $cached_models = get_transient('slmm_openai_models');
        if ($cached_models !== false) {
            return $cached_models;
        }

        $url = 'https://api.openai.com/v1/models';
        $headers = array(
            'Authorization' => 'Bearer ' . $api_key,
            'Content-Type' => 'application/json'
        );

        $response = wp_remote_get($url, array(
            'headers' => $headers,
            'timeout' => 10
        ));

        if (is_wp_error($response)) {
            return $this->get_fallback_models();
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!isset($data['data']) || !is_array($data['data'])) {
            return $this->get_fallback_models();
        }

        $models = array();
        foreach ($data['data'] as $model) {
            if (isset($model['id'])) {
                // Filter for GPT models (you can adjust this filter as needed)
                if (strpos($model['id'], 'gpt') === 0 || 
                    strpos($model['id'], 'o1') === 0 || 
                    strpos($model['id'], 'o3') === 0) {
                    $models[$model['id']] = $this->format_model_name($model['id']);
                }
            }
        }

        // Sort models by name
        asort($models);

        // Cache the results for 1 hour
        set_transient('slmm_openai_models', $models, HOUR_IN_SECONDS);

        return $models;
    }

    /**
     * Get fallback models when API call fails
     */
    private function get_fallback_models() {
        return array(
            'gpt-4o' => 'GPT-4o',
            'gpt-4o-mini' => 'GPT-4o Mini',
            'gpt-4-turbo' => 'GPT-4 Turbo',
            'gpt-4' => 'GPT-4',
            'gpt-3.5-turbo' => 'GPT-3.5 Turbo',
            'o1-preview' => 'o1 Preview',
            'o1-mini' => 'o1 Mini',
            'o3-mini' => 'o3 Mini'
        );
    }

    /**
     * Format model name for display
     */
    private function format_model_name($model_id) {
        $formatted_names = array(
            'gpt-4o' => 'GPT-4o',
            'gpt-4o-mini' => 'GPT-4o Mini',
            'gpt-4o-2024-08-06' => 'GPT-4o (2024-08-06)',
            'gpt-4o-2024-05-13' => 'GPT-4o (2024-05-13)',
            'gpt-4o-mini-2024-07-18' => 'GPT-4o Mini (2024-07-18)',
            'gpt-4-turbo' => 'GPT-4 Turbo',
            'gpt-4-turbo-2024-04-09' => 'GPT-4 Turbo (2024-04-09)',
            'gpt-4-turbo-preview' => 'GPT-4 Turbo Preview',
            'gpt-4-0125-preview' => 'GPT-4 (0125 Preview)',
            'gpt-4-1106-preview' => 'GPT-4 (1106 Preview)',
            'gpt-4' => 'GPT-4',
            'gpt-4-0613' => 'GPT-4 (0613)',
            'gpt-3.5-turbo' => 'GPT-3.5 Turbo',
            'gpt-3.5-turbo-0125' => 'GPT-3.5 Turbo (0125)',
            'gpt-3.5-turbo-1106' => 'GPT-3.5 Turbo (1106)',
            'o1-preview' => 'o1 Preview',
            'o1-preview-2024-09-12' => 'o1 Preview (2024-09-12)',
            'o1-mini' => 'o1 Mini',
            'o1-mini-2024-09-12' => 'o1 Mini (2024-09-12)',
            'o3-mini' => 'o3 Mini',
            'o3-mini-2025-01-31' => 'o3 Mini (2025-01-31)'
        );

        if (isset($formatted_names[$model_id])) {
            return $formatted_names[$model_id];
        }

        // Convert kebab-case to Title Case for unknown models
        return ucwords(str_replace('-', ' ', $model_id));
    }

    /**
     * Render model dropdown with provider selection and search
     */
    private function render_model_dropdown($field, $current_value) {
        // Include OpenRouter integration
        require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
        $openrouter = new SLMM_OpenRouter_Integration();
        
        // Get current provider setting
        $options = get_option('chatgpt_generator_options', array());
        $provider_field = $field . '_provider';
        $current_provider = isset($options[$provider_field]) ? $options[$provider_field] : 'openai';
        
        // Set default value if none selected
        if (empty($current_value)) {
            $current_value = ($current_provider === 'openrouter') ? 'google/gemini-2.0-flash-exp:free' : 'gpt-4o';
        }

        // Provider Selection Section
        echo "<div class='slmm-provider-section'>";
        echo "<label><strong>AI Provider</strong></label>";
        echo "<select id='{$provider_field}' name='chatgpt_generator_options[{$provider_field}]' class='provider-select' data-field='{$field}'>";
        echo "<option value='openai'" . selected($current_provider, 'openai', false) . ">OpenAI</option>";
        if ($openrouter->is_configured()) {
            echo "<option value='openrouter'" . selected($current_provider, 'openrouter', false) . ">OpenRouter</option>";
        }
        echo "</select>";
        
        // Show note if OpenRouter is not configured
        if (!$openrouter->is_configured()) {
            echo "<p class='slmm-provider-note'>";
            echo "<em>💡 Tip: Add your OpenRouter API key above to access 200+ additional AI models</em>";
            echo "</p>";
        }
        echo "</div>";

        // Model Search Section
        echo "<div class='slmm-search-section'>";
        echo "<label><strong>Search Models</strong></label>";
        echo "<input type='text' id='model-search-{$field}' placeholder='Type to search models...' />";
        echo "</div>";

        // Model Selection Section
        echo "<div class='slmm-selection-section'>";
        echo "<label><strong>Available Models</strong></label>";
        echo "<select id='$field' name='chatgpt_generator_options[$field]' class='model-select' data-field='{$field}' size='6'>";
        
        // Get models based on current provider
        if ($current_provider === 'openrouter' && $openrouter->is_configured()) {
            $models = $openrouter->get_models();
            $description = "Choose from OpenRouter's diverse collection of AI models including GPT-4, Claude, Gemini, and more.";
        } else {
            $models = $this->get_openai_models();
            $description = "Select from OpenAI's latest models including GPT-4o, o1, and other advanced models.";
        }
        
        foreach ($models as $model_id => $model_name) {
            $selected = selected($current_value, $model_id, false);
            echo "<option value='" . esc_attr($model_id) . "' $selected>" . esc_html($model_name) . "</option>";
        }
        
        echo "</select>";
        echo "</div>";
        
        // Actions Section
        echo "<div class='slmm-actions-section'>";
        echo "<button type='button' class='refresh-models-btn' data-field='$field'>";
        echo "<span>🔄</span> Refresh Models";
        echo "</button>";
        echo "<span class='model-count' id='model-count-{$field}'>" . count($models) . " models available</span>";
        echo "</div>";
        
        echo "<p class='description'>$description <a href='#' class='clear-model-cache'>Clear cache</a> to refresh.</p>";
        
        // Enqueue model selector assets
        wp_enqueue_style('slmm-model-selector', plugin_dir_url(__FILE__) . '../../assets/css/slmm-model-selector.css', array(), '1.1');
        wp_enqueue_script('slmm-model-selector', plugin_dir_url(__FILE__) . '../../assets/js/slmm-model-selector.js', array('jquery'), '1.1', true);
        
        // Localize script with AJAX data
        wp_localize_script('slmm-model-selector', 'slmmModelSelector', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('slmm_model_selector')
        ));
        

    }

    public function seo_overview_post_types_callback() {
        echo '<p class="description">' . __('Select which post types will show the SEO Overview meta box.', 'chatgpt-generator') . '</p>';
        $allowed_types = get_option('seo_overview_post_types', array('post', 'page'));
        $post_types = get_post_types(array('public' => true), 'objects');
        foreach ($post_types as $post_type) {
            $checked = in_array($post_type->name, $allowed_types) ? 'checked' : '';
            echo "<label><input type='checkbox' name='seo_overview_post_types[]' value='" . esc_attr($post_type->name) . "' $checked> " . esc_html($post_type->label) . "</label><br>";
        }
    }

    public function enable_page_delete_callback() {
        $enable_page_delete = get_option('enable_page_delete', false);
        $auto_empty_trash = get_option('auto_empty_trash', false);
        ?>
        <input type="checkbox" id="enable_page_delete" name="enable_page_delete" value="1" <?php checked($enable_page_delete, true); ?>>
        <label for="enable_page_delete"><?php _e('Turn on Page Delete', 'chatgpt-generator'); ?></label>
        <p class="description"><?php _e('Adds a "Send to Trash" button in the admin bar when viewing pages/posts.', 'chatgpt-generator'); ?></p>
        
        <br>
        <input type="checkbox" id="auto_empty_trash" name="auto_empty_trash" value="1" <?php checked($auto_empty_trash, true); ?>>
        <label for="auto_empty_trash"><?php _e('Turn On Empty Trash', 'chatgpt-generator'); ?></label>
        <p class="description"><?php _e('Enables the Empty Trash button next to the Delete button.', 'chatgpt-generator'); ?></p>
        <?php
    }

    public function enable_keyboard_shortcuts_callback() {
        $enable_keyboard_shortcuts = get_option('enable_keyboard_shortcuts', false);
        ?>
        <input type="checkbox" id="enable_keyboard_shortcuts" name="enable_keyboard_shortcuts" value="1" <?php checked($enable_keyboard_shortcuts, true); ?>>
        <label for="enable_keyboard_shortcuts"><?php _e('Enable Keyboard Shortcuts', 'chatgpt-generator'); ?></label>
        <p class="description"><?php _e('Enables keyboard shortcuts (Ctrl+S / Cmd+S) for saving posts and pages from anywhere on the page.', 'chatgpt-generator'); ?></p>
        <?php
    }

    public function enable_debug_logging_callback() {
        $options = get_option('chatgpt_generator_options', array());
        $enable_debug_logging = isset($options['enable_debug_logging']) ? $options['enable_debug_logging'] : false;
        
        // Default categories (matches JavaScript debugConfig.categories)
        $default_categories = array(
            'Direct Editor' => true,
            'Dashboard' => true,
            'ACF Title' => true,
            'Regular Title' => true,
            'Link Tracking' => true,
            'Important Pages' => true,
            'Silo Nav' => true,
            'Copy Links' => true,
            'Delete Semantic' => true,
            'Importance' => true,
            'Copy' => true,
            'Popup Management' => true,
            'Resize' => true,
            'ACF Direct Editor' => true,
            'Link Popup' => true,
            'ACF Integration' => true,
            'Initialization' => true,
            'Tree Loading' => true,
            'Mouseover' => true,
            'Completion' => true,
            'Surgical Update' => true,
            'Link Overlay' => true,
            'Notes' => true,
            'Visual Highlighting' => true,
            'Link Connections' => true,
            'Fallback Matching' => true,
            'Debug Analysis' => true,
            'Lorem Detector' => true,
            'Bricks Builder' => true,
            'GPT Prompt' => true,
            'Broken Link Detector' => true,
            'SEO Overview' => true,
            'SEO Tools' => true,
            'Prompt Execution' => true,
            'Notes Diagnostics' => true,
            'QuickBulk D3' => true
        );
        
        $debug_categories = isset($options['debug_logging_categories']) ? $options['debug_logging_categories'] : $default_categories;
        ?>
        <div class="slmm-debug-logging-wrapper">
            <input type="checkbox" id="enable_debug_logging" name="chatgpt_generator_options[enable_debug_logging]" value="1" <?php checked($enable_debug_logging, true); ?>>
            <label for="enable_debug_logging"><?php _e('Enable Debug Logging', 'chatgpt-generator'); ?></label>
            <p class="description"><?php _e('Enables detailed console logging for troubleshooting. Should be OFF in production for better performance.', 'chatgpt-generator'); ?></p>
            
            <div class="slmm-debug-categories-wrapper" style="margin-top: 15px; <?php echo $enable_debug_logging ? '' : 'display: none;'; ?>">
                <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 10px;">
                    <label style="font-weight: 600; margin: 0;"><?php _e('Debug Categories:', 'chatgpt-generator'); ?></label>
                    <div style="display: flex; gap: 4px;">
                        <button type="button" 
                                id="slmm-select-all-categories" 
                                style="font-size: 10px; padding: 1px 6px; height: 20px; line-height: 1; border: 1px solid #374151; background: #1f2937; color: #f3f4f6; border-radius: 3px; cursor: pointer;">
                            <?php _e('All', 'chatgpt-generator'); ?>
                        </button>
                        <button type="button" 
                                id="slmm-deselect-all-categories" 
                                style="font-size: 10px; padding: 1px 6px; height: 20px; line-height: 1; border: 1px solid #374151; background: #1f2937; color: #f3f4f6; border-radius: 3px; cursor: pointer;">
                            <?php _e('None', 'chatgpt-generator'); ?>
                        </button>
                    </div>
                </div>
                <div class="slmm-debug-categories-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 8px; max-width: 800px;">
                    <?php foreach ($default_categories as $category => $default_enabled): ?>
                        <?php 
                        $category_enabled = isset($debug_categories[$category]) ? $debug_categories[$category] : $default_enabled;
                        $category_id = 'debug_category_' . sanitize_key($category);
                        ?>
                        <label style="display: flex; align-items: center; font-size: 13px; margin: 0;">
                            <input type="checkbox" 
                                   id="<?php echo esc_attr($category_id); ?>" 
                                   name="chatgpt_generator_options[debug_logging_categories][<?php echo esc_attr($category); ?>]" 
                                   value="1" 
                                   <?php checked($category_enabled, true); ?>
                                   style="margin-right: 6px;">
                            <?php echo esc_html($category); ?>
                        </label>
                    <?php endforeach; ?>
                </div>
                <p class="description" style="margin-top: 10px; font-size: 12px; color: #666;">
                    <?php _e('Select which debug categories to log. Uncheck categories you don\'t need to reduce console noise.', 'chatgpt-generator'); ?>
                </p>
            </div>
        </div>
        
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Show/hide categories section when debug logging is toggled
            $('#enable_debug_logging').change(function() {
                var categoriesWrapper = $('.slmm-debug-categories-wrapper');
                if ($(this).is(':checked')) {
                    categoriesWrapper.slideDown(200);
                } else {
                    categoriesWrapper.slideUp(200);
                }
            });
            
            // Select all categories
            $('#slmm-select-all-categories').click(function(e) {
                e.preventDefault();
                $('.slmm-debug-categories-grid input[type="checkbox"]').prop('checked', true);
            });
            
            // Deselect all categories
            $('#slmm-deselect-all-categories').click(function(e) {
                e.preventDefault();
                $('.slmm-debug-categories-grid input[type="checkbox"]').prop('checked', false);
            });
            
            // Add hover effects to dark buttons
            $('#slmm-select-all-categories, #slmm-deselect-all-categories').hover(
                function() {
                    $(this).css('background', '#374151');
                },
                function() {
                    $(this).css('background', '#1f2937');
                }
            );
        });
        </script>
        <?php
    }

    public function enable_notes_callback() {
        $options = get_option('chatgpt_generator_options', array());
        $enable_notes = isset($options['enable_notes']) ? $options['enable_notes'] : true;
        ?>
        <input type="checkbox" id="enable_notes" name="chatgpt_generator_options[enable_notes]" value="1" <?php checked($enable_notes, true); ?>>
        <label for="enable_notes"><?php _e('Enable Project Notes', 'chatgpt-generator'); ?></label>
        <p class="description"><?php _e('Adds a notes feature to the admin bar for taking project notes across all admin pages with resizable popup and auto-save.', 'chatgpt-generator'); ?></p>
        <?php
    }

    public function enable_development_mode_callback() {
        $options = get_option('chatgpt_generator_options', array());
        $enable_development_mode = isset($options['enable_development_mode']) ? $options['enable_development_mode'] : false;
        $development_mode_color = isset($options['development_mode_color']) ? $options['development_mode_color'] : '#7a39e8';
        ?>
        <div class="slmm-development-mode-wrapper">
            <input type="checkbox" id="enable_development_mode" name="chatgpt_generator_options[enable_development_mode]" value="1" <?php checked($enable_development_mode, true); ?>>
            <label for="enable_development_mode"><?php _e('Enable Development Mode', 'chatgpt-generator'); ?></label>
            <p class="description"><?php _e('Colorizes the WordPress admin bar to help distinguish between different sites in multiple tabs.', 'chatgpt-generator'); ?></p>
            
            <div class="slmm-color-picker-wrapper" style="margin-top: 15px;">
                <label for="development_mode_color"><?php _e('Admin Bar Color:', 'chatgpt-generator'); ?></label>
                <input type="text" id="development_mode_color" name="chatgpt_generator_options[development_mode_color]" value="<?php echo esc_attr($development_mode_color); ?>" class="slmm-color-picker" />
            </div>
        </div>
        <?php
    }



    public function page_delete_post_types_callback() {
        echo '<p class="description">' . __('Select which post types can be deleted using the quick delete feature.', 'chatgpt-generator') . '</p>';
        $allowed_types = get_option('page_delete_post_types', array('post', 'page'));
        $post_types = get_post_types(array('public' => true), 'objects');
        
        foreach ($post_types as $post_type) {
            $checked = in_array($post_type->name, $allowed_types) ? 'checked' : '';
            echo "<label><input type='checkbox' name='page_delete_post_types[]' value='" . esc_attr($post_type->name) . "' $checked> " . esc_html($post_type->label) . "</label><br>";
        }
    }

    public function page_delete_rate_limit_callback() {
        $rate_limit = get_option('page_delete_rate_limit', 10);
        echo "<input type='number' id='page_delete_rate_limit' name='page_delete_rate_limit' value='" . esc_attr($rate_limit) . "' min='1' max='100'>";
    }

    public function visibility_enabled_callback() {
        $options = get_option('chatgpt_generator_options', array());
        $visibility_enabled = isset($options['visibility_enabled']) ? $options['visibility_enabled'] : false;
        ?>
        <input type="checkbox" id="visibility_enabled" name="chatgpt_generator_options[visibility_enabled]" value="1" <?php checked($visibility_enabled, true); ?>>
        <label for="visibility_enabled"><?php _e('Hide plugin from all users except authorized admins', 'chatgpt-generator'); ?></label>
        
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 10px 0;">
            <strong>⚠️ Important: Plugin Visibility Settings</strong>
            <p style="margin: 8px 0 0 0;">
                <strong>What this does:</strong> When enabled, this will hide the SLMM SEO menu and settings pages from users who are not in the authorized admin list below. 
                <br><br>
                <strong>Key Points:</strong>
                <br>• <strong>Hides UI only:</strong> The plugin functionality (SEO features, content generation, etc.) will continue to work for all users
                <br>• <strong>Settings access:</strong> Only authorized admins can access and modify plugin settings
                <br>• <strong>Purpose:</strong> Useful for hiding SLMM settings from other administrators while maintaining functionality site-wide
                <br>• <strong>Safety:</strong> Ensure you include your own username in the authorized list below!
                <br><br>
                <strong>Emergency Access:</strong> If you get locked out, use the secure emergency URL below. This URL only works if you are already logged in as an administrator - it cannot be used by unauthorized users.
                <br><br>
                <strong>Emergency URL:</strong> <span id="emergency-url" style="background: #f0f0f1; padding: 4px 8px; border-radius: 3px; cursor: pointer; user-select: all; display: inline-block; margin: 5px 0;" onclick="copyEmergencyUrl()" title="Click to copy"><?php echo esc_url(slmm_seo_get_emergency_full_url()); ?></span>
                <small style="color: #666; margin-left: 8px; font-style: italic;">← Click to copy</small>
                <span id="copy-feedback" style="color: #46b450; margin-left: 10px; opacity: 0; transition: opacity 0.3s;">Copied!</span>
                <br><br>
                <button type="button" id="regenerate-emergency-url" style="background: #2c3e50; color: #ecf0f1; border: 1px solid #34495e; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 12px; margin-top: 10px; transition: all 0.3s ease;">
                    🔄 Regenerate Emergency URL
                </button>
                <span id="regenerate-feedback" style="color: #46b450; margin-left: 10px; opacity: 0; transition: opacity 0.3s;">URL regenerated! Previous URL is now invalid.</span>
                <br><span style="color: #d63638; font-size: 12px;">⚠️ Secure: This URL only works for logged-in administrators and cannot be shared or used externally!</span>
                
                <script>
                function copyEmergencyUrl() {
                    const urlElement = document.getElementById('emergency-url');
                    const feedback = document.getElementById('copy-feedback');
                    
                    // Create a temporary textarea to copy the text
                    const tempTextArea = document.createElement('textarea');
                    tempTextArea.value = urlElement.textContent;
                    document.body.appendChild(tempTextArea);
                    tempTextArea.select();
                    
                    try {
                        document.execCommand('copy');
                        feedback.style.opacity = '1';
                        setTimeout(() => {
                            feedback.style.opacity = '0';
                        }, 2000);
                    } catch (err) {
                        console.error('Failed to copy URL:', err);
                    }
                    
                    document.body.removeChild(tempTextArea);
                }

                function regenerateEmergencyUrl() {
                    const button = document.getElementById('regenerate-emergency-url');
                    const feedback = document.getElementById('regenerate-feedback');
                    const urlElement = document.getElementById('emergency-url');

                    button.disabled = true;
                    button.textContent = '🔄 Regenerating...';

                    // AJAX call to regenerate key
                    jQuery.post(ajaxurl, {
                        action: 'slmm_regenerate_emergency_key',
                        nonce: '<?php echo wp_create_nonce("slmm_regenerate_emergency"); ?>'
                    }, function(response) {
                        if (response.success) {
                            urlElement.textContent = response.data.new_url;
                            feedback.style.opacity = '1';
                            setTimeout(() => feedback.style.opacity = '0', 3000);
                        }
                        button.disabled = false;
                        button.textContent = '🔄 Regenerate Emergency URL';
                    });
                }

                // Add click handler when page loads
                document.addEventListener('DOMContentLoaded', function() {
                    const regenerateBtn = document.getElementById('regenerate-emergency-url');
                    if (regenerateBtn) {
                        regenerateBtn.addEventListener('click', regenerateEmergencyUrl);
                    }
                });
                </script>
            </p>
        </div>
        
        <?php
    }

    public function authorized_admins_callback() {
        $options = get_option('chatgpt_generator_options', array());
        $authorized_admins = isset($options['authorized_admins']) ? $options['authorized_admins'] : array();
        $current_user = wp_get_current_user();
        
        // Ensure current user is included by default
        if (!in_array($current_user->user_login, $authorized_admins)) {
            $authorized_admins[] = $current_user->user_login;
        }
        ?>
        <div id="authorized-admins-container">
            <?php foreach ($authorized_admins as $index => $username): ?>
                <div class="authorized-admin-row" style="margin-bottom: 5px;">
                    <input type="text" name="chatgpt_generator_options[authorized_admins][]" value="<?php echo esc_attr($username); ?>" placeholder="Enter admin username" style="margin-right: 10px;">
                    <button type="button" class="button remove-admin-button">Remove</button>
                </div>
            <?php endforeach; ?>
        </div>
        <button type="button" id="add-admin-button" class="button button-secondary">Add Admin</button>
        <p class="description"><?php _e('Enter the usernames (not emails) of the admins who should have access to this plugin. Make sure to include your own username! At least one valid username is required.', 'chatgpt-generator'); ?></p>
        
        <script>
        jQuery(document).ready(function($) {
            $('#add-admin-button').on('click', function() {
                var newRow = '<div class="authorized-admin-row" style="margin-bottom: 5px;">' +
                           '<input type="text" name="chatgpt_generator_options[authorized_admins][]" value="" placeholder="Enter admin username" style="margin-right: 10px;">' +
                           '<button type="button" class="button remove-admin-button">Remove</button>' +
                           '</div>';
                $('#authorized-admins-container').append(newRow);
            });
            
            $(document).on('click', '.remove-admin-button', function() {
                $(this).parent().remove();
            });
        });
        </script>
        <?php
    }

    private function log_deletion_attempt($post_id, $success, $error_message = '') {
        if (!file_exists(WP_CONTENT_DIR . '/deletion-logs')) {
            wp_mkdir_p(WP_CONTENT_DIR . '/deletion-logs');
        }

        $log_file = WP_CONTENT_DIR . '/deletion-logs/deletion-log-' . date('Y-m') . '.log';
        $user = wp_get_current_user();
        $post = get_post($post_id);
        $log_entry = sprintf(
            "[%s] User: %s (ID: %d) | Post: %s (ID: %d) | Type: %s | Status: %s | %s\n",
            current_time('mysql'),
            $user->user_login,
            $user->ID,
            $post ? $post->post_title : 'Unknown',
            $post_id,
            $post ? $post->post_type : 'Unknown',
            $success ? 'Success' : 'Failed',
            $error_message
        );

        error_log($log_entry, 3, $log_file);
    }

    private function check_rate_limit($user_id) {
        global $wpdb;
        $rate_limit = get_option('page_delete_rate_limit', 10);
        $one_hour_ago = date('Y-m-d H:i:s', strtotime('-1 hour'));
        
        // Get deletion count in the last hour from logs
        $log_file = WP_CONTENT_DIR . '/deletion-logs/deletion-log-' . date('Y-m') . '.log';
        if (!file_exists($log_file)) {
            return true;
        }

        $log_content = file_get_contents($log_file);
        $pattern = "/\[([^\]]+)\] User: [^\(]+ \(ID: " . $user_id . "\)/";
        preg_match_all($pattern, $log_content, $matches, PREG_SET_ORDER);

        $count = 0;
        foreach ($matches as $match) {
            $log_time = strtotime($match[1]);
            if ($log_time > strtotime('-1 hour')) {
                $count++;
            }
        }

        return $count < $rate_limit;
    }

    private function empty_trash_for_post_type($post_type) {
        global $wpdb;

        try {
            // Use a transaction for data integrity
            $wpdb->query('START TRANSACTION');

            // First, get all trashed posts of this type
            $trashed_posts = $wpdb->get_col($wpdb->prepare("
                SELECT ID FROM {$wpdb->posts} 
                WHERE post_type = %s 
                AND post_status = 'trash'
            ", $post_type));

            if (empty($trashed_posts)) {
                $wpdb->query('COMMIT');
                return true;
            }

            // Delete each post properly using WordPress functions
            foreach ($trashed_posts as $post_id) {
                // Delete attachments first
                $attachments = $wpdb->get_col($wpdb->prepare("
                    SELECT ID FROM {$wpdb->posts} 
                    WHERE post_type = 'attachment' 
                    AND post_parent = %d
                ", $post_id));

                foreach ($attachments as $attachment_id) {
                    wp_delete_attachment($attachment_id, true);
                }

                // Force delete the post (skip trash)
                wp_delete_post($post_id, true);
            }

            // Clean up orphaned metadata
            $wpdb->query("
                DELETE pm
                FROM {$wpdb->postmeta} pm
                LEFT JOIN {$wpdb->posts} posts ON pm.post_id = posts.ID
                WHERE posts.ID IS NULL
            ");

            // Clean up orphaned relationships
            $wpdb->query("
                DELETE tr
                FROM {$wpdb->term_relationships} tr
                LEFT JOIN {$wpdb->posts} posts ON tr.object_id = posts.ID
                WHERE posts.ID IS NULL
            ");

            $wpdb->query('COMMIT');
            wp_cache_flush();
            return true;
        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            throw $e;
        }
    }

    public function init_page_delete() {
        // Early exit if feature is not enabled or user is not logged in
        if (!get_option('enable_page_delete', false) || !is_user_logged_in()) {
            return;
        }

        // Add Empty Trash button to admin bar
        add_action('admin_bar_menu', function ($admin_bar) {
            // Only show buttons to authorized users
            if (!current_user_can('delete_posts')) {
                return;
            }

            // Get current post type
            $post_type = null;
            
            if (is_singular()) {
                $post_id = get_the_ID();
                $post_type = get_post_type($post_id);
            } elseif (is_admin()) {
                // In admin area, get post type from screen
                $screen = get_current_screen();
                if ($screen && in_array($screen->base, ['edit', 'post'])) {
                    $post_type = $screen->post_type;
                }
            }

            // If we have a valid post type and it's allowed
            if ($post_type) {
                $allowed_types = get_option('page_delete_post_types', array('post', 'page'));
                if (in_array($post_type, $allowed_types)) {
                    $auto_empty_trash = get_option('auto_empty_trash', false);
                    
                    // Add Empty Trash button if enabled and not on trash view
                    if ($auto_empty_trash && (!isset($_GET['post_status']) || $_GET['post_status'] !== 'trash')) {
                        $empty_nonce = wp_create_nonce('empty-trash-' . $post_type);
                        $admin_bar->add_node([
                            'id'    => 'empty-trash',
                            'title' => __('Empty Trash', 'chatgpt-generator'),
                            'href'  => esc_url(add_query_arg(array(
                                'action' => 'empty-trash',
                                'post_type' => $post_type,
                                '_wpnonce' => $empty_nonce,
                            ), admin_url('admin-post.php'))),
                            'meta'  => [
                                'title' => esc_attr(__('Empty Trash', 'chatgpt-generator')),
                                'target' => '_self',
                                'onclick' => 'return confirm("' . esc_js(__('Are you sure you want to permanently delete all trashed items of this type?', 'chatgpt-generator')) . '");'
                            ],
                        ]);
                    }

                    // Only add Send to Trash button on singular pages
                    if (is_singular()) {
                        $post_id = get_the_ID();
                        if (get_post_status($post_id) !== 'trash' && current_user_can('delete_post', $post_id)) {
                            $nonce = wp_create_nonce('trash-post_' . $post_id);
                            $admin_bar->add_node([
                                'id'    => 'send-to-trash',
                                'title' => __('Send to Trash', 'chatgpt-generator'),
                                'href'  => esc_url(add_query_arg(array(
                                    'post' => $post_id,
                                    'action' => 'trash',
                                    '_wpnonce' => $nonce,
                                    'confirm' => '1'
                                ), admin_url('post.php'))),
                                'meta'  => [
                                    'title' => esc_attr(__('Send to Trash', 'chatgpt-generator')),
                                    'target' => '_self',
                                    'onclick' => 'return confirm("' . esc_js(__('Are you sure you want to move this item to trash?', 'chatgpt-generator')) . '");'
                                ],
                            ]);
                        }
                    }
                }
            }
        }, 100);

        // Add Empty Trash button to list view
        add_action('restrict_manage_posts', function() {
            $screen = get_current_screen();
            if (!$screen) return;

            // Don't show the button on trash view
            if (isset($_GET['post_status']) && $_GET['post_status'] === 'trash') {
                return;
            }

            $post_type = $screen->post_type;
            $allowed_types = get_option('page_delete_post_types', array('post', 'page'));
            $auto_empty_trash = get_option('auto_empty_trash', false);

            if (in_array($post_type, $allowed_types) && $auto_empty_trash && current_user_can('delete_posts')) {
                $empty_nonce = wp_create_nonce('empty-trash-' . $post_type);
                $url = add_query_arg(array(
                    'action' => 'empty-trash',
                    'post_type' => $post_type,
                    '_wpnonce' => $empty_nonce,
                ), admin_url('admin-post.php'));
                
                echo '<a href="' . esc_url($url) . '" class="button" onclick="return confirm(\'' . 
                    esc_js(__('Are you sure you want to permanently delete all trashed items of this type?', 'chatgpt-generator')) . 
                    '\');">' . esc_html__('Empty Trash', 'chatgpt-generator') . '</a>';
            }
        });

        // Handle Empty Trash action
        add_action('admin_post_empty-trash', function() {
            if (!isset($_GET['post_type'], $_GET['_wpnonce'])) {
                wp_die(__('Invalid request.', 'chatgpt-generator'));
            }

            $post_type = sanitize_text_field($_GET['post_type']);
            $nonce = sanitize_text_field($_GET['_wpnonce']);

            if (!wp_verify_nonce($nonce, 'empty-trash-' . $post_type)) {
                wp_die(__('Security check failed.', 'chatgpt-generator'));
            }

            if (!current_user_can('delete_posts')) {
                wp_die(__('You do not have permission to empty trash.', 'chatgpt-generator'));
            }

            try {
                $this->empty_trash_for_post_type($post_type);
                $redirect_url = admin_url('edit.php' . ($post_type !== 'post' ? '?post_type=' . $post_type : ''));
                wp_safe_redirect(add_query_arg('trashed', 'emptied', $redirect_url));
                exit;
            } catch (Exception $e) {
                wp_die(__('Error emptying trash: ', 'chatgpt-generator') . esc_html($e->getMessage()));
            }
        });

        // Handle regular trash action
        add_action('template_redirect', function () {
            // Check if we're on a trashed confirmation page
            if (isset($_GET['trashed']) && isset($_GET['ids'])) {
                $post_id = absint($_GET['ids']);
                $post_type = get_post_type($post_id);
                if (!$post_type) {
                    $post_type = 'post'; // fallback to post if type can't be determined
                }
                // Redirect to the post type list view
                $redirect_url = admin_url('edit.php' . ($post_type !== 'post' ? '?post_type=' . $post_type : ''));
                wp_safe_redirect($redirect_url);
                exit;
            }

            // Only process on singular pages and for logged-in users
            if (!is_singular() || !is_user_logged_in()) {
                return;
            }

            // Verify all required parameters are present
            if (!isset($_GET['action'], $_GET['post'], $_GET['_wpnonce'], $_GET['confirm'])) {
                return;
            }

            $post_id = absint($_GET['post']);
            $nonce = sanitize_text_field($_GET['_wpnonce']);
            $action = sanitize_text_field($_GET['action']);
            $user_id = get_current_user_id();
            
            // Check rate limit
            if (!$this->check_rate_limit($user_id)) {
                $this->log_deletion_attempt($post_id, false, 'Rate limit exceeded');
                wp_die(__('Rate limit exceeded. Please try again later.', 'chatgpt-generator'));
            }

            // Verify post exists and is not already in trash
            if (!$post_id || !get_post($post_id) || get_post_status($post_id) === 'trash') {
                $this->log_deletion_attempt($post_id, false, 'Invalid post');
                wp_die(__('Invalid post.', 'chatgpt-generator'));
            }

            // Check post type restrictions
            $post_type = get_post_type($post_id);
            $allowed_types = get_option('page_delete_post_types', array('post', 'page'));
            if (!in_array($post_type, $allowed_types)) {
                $this->log_deletion_attempt($post_id, false, 'Post type not allowed');
                wp_die(__('This post type cannot be deleted using quick delete.', 'chatgpt-generator'));
            }

            if (current_user_can('delete_post', $post_id) && wp_verify_nonce($nonce, 'trash-post_' . $post_id)) {
                try {
                    // Move to trash
                    $trashed = wp_trash_post($post_id);
                    if (!$trashed) {
                        throw new Exception('Failed to move post to trash');
                    }

                    $this->log_deletion_attempt($post_id, true, 'Moved to trash');

                    // Always redirect to the post type list view
                    $redirect_url = admin_url('edit.php' . ($post_type !== 'post' ? '?post_type=' . $post_type : ''));
                    wp_safe_redirect($redirect_url);
                    exit;
                } catch (Exception $e) {
                    $this->log_deletion_attempt($post_id, false, $e->getMessage());
                    wp_die(__('Error: ', 'chatgpt-generator') . esc_html($e->getMessage()));
                }
            } else {
                $this->log_deletion_attempt($post_id, false, 'Permission denied or invalid nonce');
                wp_die(__('You are not allowed to perform this action.', 'chatgpt-generator'));
            }
        });

        // Add success message for emptied trash
        add_action('admin_notices', function() {
            if (isset($_GET['trashed']) && $_GET['trashed'] === 'emptied') {
                ?>
                <div class="notice notice-success is-dismissible">
                    <p><?php _e('Trash has been emptied successfully.', 'chatgpt-generator'); ?></p>
                </div>
                <?php
            }
        });
    }

    public function export_settings() {
        $all_settings = array(
            'chatgpt_generator_options' => get_option('chatgpt_generator_options'),
            'slmm_gpt_prompts' => get_option('slmm_gpt_prompts')
        );

        // Remove API keys
        unset($all_settings['chatgpt_generator_options']['openai_api_key']);

        return json_encode($all_settings);
    }

    public function import_settings($json_data) {
        $imported_settings = json_decode($json_data, true);

        if (json_last_error() === JSON_ERROR_NONE) {
            if (isset($imported_settings['chatgpt_generator_options'])) {
                $current_options = get_option('chatgpt_generator_options', array());
                $merged_options = array_merge($current_options, $imported_settings['chatgpt_generator_options']);
                update_option('chatgpt_generator_options', $merged_options);
            }
            if (isset($imported_settings['slmm_gpt_prompts'])) {
                update_option('slmm_gpt_prompts', $imported_settings['slmm_gpt_prompts']);
            }
            return true;
        }
        return false;
    }

    public function handle_export_import() {
        if (!current_user_can('manage_options')) {
            return;
        }

        if (isset($_POST['export_settings']) && check_admin_referer('slmm_export_import_settings', 'slmm_export_import_nonce')) {
            $export_data = array();

            if (isset($_POST['export_chatgpt_generator'])) {
                $export_data['chatgpt_generator_options'] = get_option('chatgpt_generator_options');
                // Remove API keys
                unset($export_data['chatgpt_generator_options']['openai_api_key']);
                
                // Remove protected words if not explicitly requested
                if (!isset($_POST['export_protected_words'])) {
                    unset($export_data['chatgpt_generator_options']['protected_words_list']);
                }
            }

            if (isset($_POST['export_slmm_gpt_prompts'])) {
                $export_data['slmm_gpt_prompts'] = get_option('slmm_gpt_prompts');
            }

            $json_data = json_encode($export_data);
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename=slmm_seo_settings_export.json');
            echo $json_data;
            exit;
        }

        if (isset($_POST['import_settings']) && check_admin_referer('slmm_export_import_settings', 'slmm_export_import_nonce')) {
            if (isset($_FILES['import_file']) && $_FILES['import_file']['error'] == 0) {
                $json_data = file_get_contents($_FILES['import_file']['tmp_name']);
                $import_result = $this->import_settings($json_data);

                if ($import_result) {
                    add_settings_error('slmm_seo_settings', 'settings_updated', 'Settings imported successfully.', 'updated');
                } else {
                    add_settings_error('slmm_seo_settings', 'import_error', 'Error importing settings. Please check the file format.', 'error');
                }
            } else {
                add_settings_error('slmm_seo_settings', 'import_error', 'Error uploading file. Please try again.', 'error');
            }
        }
    }

    public function render_settings_page() {
        $this->enqueue_settings_assets();
        ?>
        <div class="wrap slmm-settings-wrap">
            <div class="slmm-settings-header">
                <h1 class="slmm-settings-title">
                    <span class="slmm-logo">⚡</span>
                    <?php echo esc_html(get_admin_page_title()); ?>
                </h1>
                <p class="slmm-settings-subtitle">Configure your SLMM SEO Bundle settings</p>
            </div>
            
            <?php settings_errors(); ?>

            <div class="slmm-settings-container">
                <div class="slmm-tabs-wrapper">
                    <nav class="slmm-tabs-nav">
                        <button class="slmm-tab-button active" data-tab="interlinking-suite">
                            <span class="slmm-tab-icon"></span>
                            Interlinking Suite
                        </button>
                        <button class="slmm-tab-button" data-tab="api-keys">
                            <span class="slmm-tab-icon"></span>
                            API Keys
                        </button>
                        <button class="slmm-tab-button" data-tab="prompts">
                            <span class="slmm-tab-icon"></span>
                            Prompts
                        </button>
                        <button class="slmm-tab-button" data-tab="business-info">
                            <span class="slmm-tab-icon"></span>
                            Business Info
                        </button>
                        <button class="slmm-tab-button" data-tab="models">
                            <span class="slmm-tab-icon"></span>
                            AI Models
                        </button>
                        <button class="slmm-tab-button" data-tab="protected-words">
                            <span class="slmm-tab-icon"></span>
                            Protected Words
                        </button>
                        <button class="slmm-tab-button" data-tab="search-replace">
                            <span class="slmm-tab-icon"></span>
                            Search & Replace
                        </button>
                        <button class="slmm-tab-button" data-tab="features">
                            <span class="slmm-tab-icon"></span>
                            Features
                        </button>
                        <button class="slmm-tab-button" data-tab="lorem-detector">
                            <span class="slmm-tab-icon"></span>
                            Lorem Ipsum Detector
                        </button>
                        <button class="slmm-tab-button" data-tab="export-import">
                            <span class="slmm-tab-icon"></span>
                            Export/Import
                        </button>
                    </nav>

                    <div class="slmm-tabs-content">
                        <form action="options.php" method="post" class="slmm-settings-form">
                            <?php settings_fields('chatgpt_generator_settings'); ?>
                            
                            <!-- API Keys Tab -->
                            <div class="slmm-tab-pane active" id="api-keys">
                                <div class="slmm-tab-header">
                                    <h2>API Keys</h2>
                                    <p>Enter your API keys for AI services</p>
                                </div>
                                <div class="slmm-form-grid">
                                    <?php $this->render_tab_fields('api_keys'); ?>
                                </div>
                            </div>

                            <!-- Prompts Tab -->
                            <div class="slmm-tab-pane" id="prompts">
                                <div class="slmm-tab-header">
                                    <h2>Prompts</h2>
                                    <p>Configure your prompts for different content types</p>
                                </div>
                                <div class="slmm-form-grid">
                                    <?php $this->render_tab_fields('prompts'); ?>
                                </div>
                            </div>

                            <!-- Business Info Tab -->
                            <div class="slmm-tab-pane" id="business-info">
                                <div class="slmm-tab-header">
                                    <h2>Business Information</h2>
                                    <p>Enter your business details</p>
                                </div>
                                <div class="slmm-form-grid">
                                    <?php $this->render_tab_fields('business_info'); ?>
                                </div>
                            </div>

                            <!-- AI Models Tab -->
                            <div class="slmm-tab-pane" id="models">
                                <div class="slmm-tab-header">
                                    <h2>AI Models</h2>
                                    <p>Select AI models for different content types</p>
                                </div>
                                
                                <!-- Selected Models Section -->
                                <?php $this->render_selected_models_section(); ?>
                                
                                <div class="slmm-form-grid">
                                    <?php $this->render_tab_fields('models'); ?>
                                </div>
                            </div>

                            <!-- Protected Words Tab -->
                            <div class="slmm-tab-pane" id="protected-words">
                                <div class="slmm-tab-header">
                                    <h2>Protected Words</h2>
                                    <p>Words that should always maintain their capitalization</p>
                                </div>
                                <div class="slmm-form-grid">
                                    <?php $this->render_tab_fields('protected_words'); ?>
                                </div>
                            </div>

                            <!-- Features Tab -->
                            <div class="slmm-tab-pane" id="features">
                                <div class="slmm-tab-header">
                                    <h2>Plugin Features</h2>
                                    <p>Enable or disable specific plugin features</p>
                                </div>
                                <div class="slmm-form-grid">
                                    <?php $this->render_tab_fields('features'); ?>
                                    <?php $this->render_feature_callbacks(); ?>
                                </div>
                            </div>

                            <!-- Search & Replace Tab -->
                            <div class="slmm-tab-pane" id="search-replace">
                                <div class="slmm-tab-header">
                                    <h2>Search & Replace</h2>
                                    <p>Perform database-wide search and replace operations with safety checks</p>
                                </div>
                                <div class="slmm-search-replace-content">
                                    <div class="slmm-search-replace-info">
                                        <h3>Database Search & Replace</h3>
                                        <p>Search and replace text across your entire WordPress database. This tool handles serialized data properly and includes safety features.</p>
                                        <p><strong>Warning:</strong> Always backup your database before performing search & replace operations.</p>
                                    </div>
                                    
                                    <div id="slmm-search-replace-form-placeholder">
                                        <!-- Search & Replace form will be rendered outside the main form -->
                                    </div>
                                </div>
                            </div>

                            <!-- Interlinking Suite Tab -->
                            <div class="slmm-tab-pane" id="interlinking-suite">
                                <div class="slmm-tab-header">
                                    <h2>Interlinking Suite</h2>
                                    <p>Configure automatic content segmentation settings</p>
                                </div>
                                <div class="slmm-interlinking-suite-content">
                                    <!-- Placeholder Reference Section -->
                                    <div class="slmm-placeholder-reference-section">
                                        <h3>Placeholder Reference</h3>
                                        <p>Available placeholders for AI prompts. Click to copy to clipboard.</p>
                                        
                                        <div class="slmm-placeholder-grid">
                                            <div class="slmm-placeholder-item">
                                                <div class="slmm-placeholder-header">
                                                    <code class="slmm-placeholder-code">{content}</code>
                                                    <button class="slmm-copy-placeholder" data-placeholder="{content}" title="Copy to clipboard">📋</button>
                                                </div>
                                                <p class="slmm-placeholder-description">The full text content of the page being analyzed</p>
                                            </div>
                                            
                                            <div class="slmm-placeholder-item">
                                                <div class="slmm-placeholder-header">
                                                    <code class="slmm-placeholder-code">{target_page}</code>
                                                    <button class="slmm-copy-placeholder" data-placeholder="{target_page}" title="Copy to clipboard">📋</button>
                                                </div>
                                                <p class="slmm-placeholder-description">URL of the target page for linking context</p>
                                            </div>
                                            
                                            <div class="slmm-placeholder-item">
                                                <div class="slmm-placeholder-header">
                                                    <code class="slmm-placeholder-code">{linking_rules}</code>
                                                    <button class="slmm-copy-placeholder" data-placeholder="{linking_rules}" title="Copy to clipboard">📋</button>
                                                </div>
                                                <p class="slmm-placeholder-description">Current interlinking rules and guidelines</p>
                                            </div>
                                            
                                            <div class="slmm-placeholder-item">
                                                <div class="slmm-placeholder-header">
                                                    <code class="slmm-placeholder-code">{article_summary}</code>
                                                    <button class="slmm-copy-placeholder" data-placeholder="{article_summary}" title="Copy to clipboard">📋</button>
                                                </div>
                                                <p class="slmm-placeholder-description">AI-generated summary of the current page (Page Summarization feature)</p>
                                            </div>
                                            
                                            <div class="slmm-placeholder-item">
                                                <div class="slmm-placeholder-header">
                                                    <code class="slmm-placeholder-code">{parent_page}</code>
                                                    <button class="slmm-copy-placeholder" data-placeholder="{parent_page}" title="Copy to clipboard">📋</button>
                                                </div>
                                                <p class="slmm-placeholder-description">Parent page title and URL from silo structure</p>
                                            </div>
                                            
                                            <div class="slmm-placeholder-item">
                                                <div class="slmm-placeholder-header">
                                                    <code class="slmm-placeholder-code">{h1_current_page}</code>
                                                    <button class="slmm-copy-placeholder" data-placeholder="{h1_current_page}" title="Copy to clipboard">📋</button>
                                                </div>
                                                <p class="slmm-placeholder-description">H1 heading of the current page being edited</p>
                                            </div>
                                            
                                            <div class="slmm-placeholder-item">
                                                <div class="slmm-placeholder-header">
                                                    <code class="slmm-placeholder-code">{h1_target_page}</code>
                                                    <button class="slmm-copy-placeholder" data-placeholder="{h1_target_page}" title="Copy to clipboard">📋</button>
                                                </div>
                                                <p class="slmm-placeholder-description">H1 heading of the target page for linking</p>
                                            </div>

                                            <div class="slmm-placeholder-item">
                                                <div class="slmm-placeholder-header">
                                                    <code class="slmm-placeholder-code">{website_url}</code>
                                                    <button class="slmm-copy-placeholder" data-placeholder="{website_url}" title="Copy to clipboard">📋</button>
                                                </div>
                                                <p class="slmm-placeholder-description">The current website URL</p>
                                            </div>
                                        </div>
                                        
                                        <style>
                                        .slmm-placeholder-reference-section {
                                            background: #1a1a1a;
                                            border: 1px solid #333;
                                            border-radius: 8px;
                                            padding: 20px;
                                            margin-bottom: 30px;
                                        }
                                        
                                        .slmm-placeholder-grid {
                                            display: grid;
                                            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                                            gap: 15px;
                                            margin-top: 15px;
                                        }
                                        
                                        .slmm-placeholder-item {
                                            background: #2a2a2a;
                                            border: 1px solid #444;
                                            border-radius: 6px;
                                            padding: 15px;
                                        }
                                        
                                        .slmm-placeholder-header {
                                            display: flex;
                                            align-items: center;
                                            justify-content: space-between;
                                            margin-bottom: 8px;
                                        }
                                        
                                        .slmm-placeholder-code {
                                            background: #374151;
                                            border: 1px solid #4b5563;
                                            color: #f3f4f6;
                                            padding: 4px 8px;
                                            border-radius: 4px;
                                            font-family: monospace;
                                            font-size: 13px;
                                        }
                                        
                                        .slmm-copy-placeholder {
                                            background: #8b5cf6;
                                            color: white;
                                            border: none;
                                            padding: 4px 8px;
                                            border-radius: 4px;
                                            cursor: pointer;
                                            font-size: 12px;
                                            min-width: 32px;
                                            display: inline-block;
                                            text-align: center;
                                        }
                                        
                                        .slmm-copy-placeholder:hover {
                                            background: #7c3aed;
                                        }
                                        
                                        .slmm-copy-placeholder.copied {
                                            background: #10b981;
                                        }
                                        
                                        .slmm-placeholder-description {
                                            color: #9ca3af;
                                            font-size: 13px;
                                            margin: 0;
                                            line-height: 1.4;
                                        }
                                        </style>
                                        
                                        <script>
                                        jQuery(document).ready(function($) {
                                            $('.slmm-copy-placeholder').on('click', function(e) {
                                                e.preventDefault();
                                                const placeholder = $(this).data('placeholder');
                                                const button = $(this);
                                                
                                                // Copy to clipboard
                                                if (navigator.clipboard && window.isSecureContext) {
                                                    navigator.clipboard.writeText(placeholder).then(function() {
                                                        // Show success feedback with CSS class
                                                        button.addClass('copied').text('✓');
                                                        setTimeout(function() {
                                                            button.removeClass('copied').text('📋');
                                                        }, 2000);
                                                    });
                                                } else {
                                                    // Fallback for older browsers
                                                    const textArea = document.createElement('textarea');
                                                    textArea.value = placeholder;
                                                    document.body.appendChild(textArea);
                                                    textArea.focus();
                                                    textArea.select();
                                                    try {
                                                        document.execCommand('copy');
                                                        // Show success feedback with CSS class
                                                        button.addClass('copied').text('✓');
                                                        setTimeout(function() {
                                                            button.removeClass('copied').text('📋');
                                                        }, 2000);
                                                    } catch (err) {
                                                        console.error('Failed to copy text: ', err);
                                                    }
                                                    document.body.removeChild(textArea);
                                                }
                                            });
                                        });
                                        </script>
                                    </div>

                                    <!-- Automatic Interlinking Section -->
                                    <div class="slmm-automatic-interlinking-section">
                                        <h3>Automatic Interlinking Settings</h3>
                                        <p>Configure automatic sibling interlinking for Custom Post Types. Links will be inserted at the bottom of posts in a closed-loop pattern.</p>

                                        <div class="slmm-interlinking-form-grid">
                                            <div class="slmm-form-field">
                                                <label class="slmm-field-label" for="auto_interlinking_section_text">Interlinking Section Text</label>
                                                <input type="text"
                                                       id="auto_interlinking_section_text"
                                                       name="chatgpt_generator_options[auto_interlinking_section_text]"
                                                       value="<?php $options = get_option('chatgpt_generator_options', array()); echo esc_attr(isset($options['auto_interlinking_section_text']) ? $options['auto_interlinking_section_text'] : 'Check out our other articles'); ?>"
                                                       class="slmm-text-input"
                                                       placeholder="e.g., Check out our other articles">
                                                <p class="slmm-field-description">Text displayed above the automatic interlinking section</p>
                                            </div>

                                            <div class="slmm-form-field">
                                                <label class="slmm-field-label" for="auto_interlinking_heading_size">Heading Size</label>
                                                <select id="auto_interlinking_heading_size"
                                                        name="chatgpt_generator_options[auto_interlinking_heading_size]"
                                                        class="slmm-select-input">
                                                    <?php
                                                    $current_heading = isset($options['auto_interlinking_heading_size']) ? $options['auto_interlinking_heading_size'] : 'h3';
                                                    $heading_options = array('h3' => 'H3', 'h4' => 'H4', 'h5' => 'H5');
                                                    foreach ($heading_options as $value => $label) {
                                                        echo '<option value="' . esc_attr($value) . '"' . selected($current_heading, $value, false) . '>' . esc_html($label) . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                                <p class="slmm-field-description">HTML heading size for the interlinking section</p>
                                            </div>

                                            <div class="slmm-form-field">
                                                <label class="slmm-field-label" for="auto_interlinking_article_count">Number of Articles to Link</label>
                                                <select id="auto_interlinking_article_count"
                                                        name="chatgpt_generator_options[auto_interlinking_article_count]"
                                                        class="slmm-select-input">
                                                    <?php
                                                    $current_count = isset($options['auto_interlinking_article_count']) ? intval($options['auto_interlinking_article_count']) : 3;
                                                    for ($i = 1; $i <= 10; $i++) {
                                                        echo '<option value="' . $i . '"' . selected($current_count, $i, false) . '>' . $i . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                                <p class="slmm-field-description">Number of sibling articles to link (1-10)</p>
                                            </div>

                                            <div class="slmm-form-field">
                                                <label class="slmm-field-label">
                                                    <input type="checkbox"
                                                           id="auto_interlinking_enabled"
                                                           name="chatgpt_generator_options[auto_interlinking_enabled]"
                                                           value="1"
                                                           class="slmm-checkbox"
                                                           <?php checked(isset($options['auto_interlinking_enabled']) ? $options['auto_interlinking_enabled'] : false, 1); ?>>
                                                    Enable Automatic Interlinking
                                                </label>
                                                <p class="slmm-field-description">Enable automatic sibling interlinking for all Custom Post Types</p>
                                            </div>
                                        </div>

                                        <div class="slmm-interlinking-preview">
                                            <h4>Preview</h4>
                                            <div class="slmm-preview-content">
                                                <div id="auto-interlinking-preview">
                                                    <div class="preview-heading">
                                                        <span id="preview-heading-tag">H3</span>:
                                                        <span id="preview-section-text">Check out our other articles</span>
                                                    </div>
                                                    <ul class="preview-links">
                                                        <li><a href="#">Article 1 - Next in sequence</a></li>
                                                        <li><a href="#">Article 2 - Following article</a></li>
                                                        <li><a href="#">Article 3 - Links back to first</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>

                                        <style>
                                        .slmm-automatic-interlinking-section {
                                            background: #1a1a1a;
                                            border: 1px solid #333;
                                            border-radius: 8px;
                                            padding: 20px;
                                            margin-bottom: 30px;
                                        }

                                        .slmm-automatic-interlinking-section h3 {
                                            color: #f3f4f6;
                                            margin-top: 0;
                                            margin-bottom: 8px;
                                        }

                                        .slmm-automatic-interlinking-section p {
                                            color: #9ca3af;
                                            margin-bottom: 20px;
                                        }

                                        .slmm-interlinking-form-grid {
                                            display: grid;
                                            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                                            gap: 20px;
                                            margin-bottom: 25px;
                                        }

                                        .slmm-form-field {
                                            display: flex;
                                            flex-direction: column;
                                        }

                                        .slmm-field-label {
                                            color: #f3f4f6;
                                            font-weight: 500;
                                            margin-bottom: 8px;
                                            display: flex;
                                            align-items: center;
                                        }

                                        .slmm-text-input,
                                        .slmm-select-input {
                                            background: #2a2a2a;
                                            border: 1px solid #444;
                                            border-radius: 6px;
                                            color: #f3f4f6;
                                            padding: 10px 12px;
                                            font-size: 14px;
                                            min-height: 40px;
                                        }

                                        .slmm-text-input:focus,
                                        .slmm-select-input:focus {
                                            outline: none;
                                            border-color: #8b5cf6;
                                            box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
                                        }

                                        .slmm-checkbox {
                                            appearance: none;
                                            width: 18px;
                                            height: 18px;
                                            border: 2px solid #d1d5db;
                                            border-radius: 4px;
                                            background: #2a2a2a;
                                            cursor: pointer;
                                            margin-right: 8px;
                                            position: relative;
                                        }

                                        .slmm-checkbox:checked {
                                            background: #8b5cf6;
                                            border-color: #8b5cf6;
                                        }

                                        .slmm-checkbox:checked::after {
                                            content: '✓';
                                            color: white;
                                            position: absolute;
                                            top: 50%;
                                            left: 50%;
                                            transform: translate(-50%, -50%);
                                            font-size: 12px;
                                            font-weight: bold;
                                            line-height: 1;
                                        }

                                        .slmm-field-description {
                                            color: #6b7280;
                                            font-size: 12px;
                                            margin: 5px 0 0 0;
                                            line-height: 1.4;
                                        }

                                        .slmm-interlinking-preview {
                                            background: #2a2a2a;
                                            border: 1px solid #444;
                                            border-radius: 6px;
                                            padding: 15px;
                                            margin-top: 20px;
                                        }

                                        .slmm-interlinking-preview h4 {
                                            color: #f3f4f6;
                                            margin: 0 0 10px 0;
                                            font-size: 14px;
                                        }

                                        .slmm-preview-content {
                                            background: #1a1a1a;
                                            border: 1px solid #333;
                                            border-radius: 4px;
                                            padding: 15px;
                                        }

                                        .preview-heading {
                                            color: #f3f4f6;
                                            font-weight: 600;
                                            margin-bottom: 10px;
                                        }

                                        #preview-heading-tag {
                                            color: #8b5cf6;
                                            font-family: monospace;
                                        }

                                        .preview-links {
                                            margin: 0;
                                            padding-left: 20px;
                                        }

                                        .preview-links li {
                                            margin-bottom: 5px;
                                        }

                                        .preview-links a {
                                            color: #60a5fa;
                                            text-decoration: none;
                                        }

                                        .preview-links a:hover {
                                            color: #93c5fd;
                                            text-decoration: underline;
                                        }

                                        /* Search and Replace Preview Highlighting */
                                        .slmm-highlight-search {
                                            background-color: #9333ea !important;
                                            color: white !important;
                                            padding: 2px 4px;
                                            border-radius: 3px;
                                            font-weight: bold;
                                        }

                                        .slmm-highlight-replacement {
                                            background-color: #059669 !important;
                                            color: white !important;
                                            padding: 2px 4px;
                                            border-radius: 3px;
                                            font-weight: bold;
                                        }

                                        .slmm-preview-dropdown {
                                            background: #1a1a1a;
                                            border: 1px solid #333;
                                            border-radius: 6px;
                                            padding: 15px;
                                            margin-top: 8px;
                                            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                                            position: absolute;
                                            z-index: 1000;
                                            max-width: 500px;
                                            word-wrap: break-word;
                                        }

                                        .slmm-change-item {
                                            margin-bottom: 15px;
                                            padding-bottom: 15px;
                                            border-bottom: 1px solid #333;
                                        }

                                        .slmm-change-item:last-child {
                                            border-bottom: none;
                                            margin-bottom: 0;
                                        }

                                        .slmm-change-before,
                                        .slmm-change-after {
                                            margin: 8px 0;
                                            font-family: monospace;
                                            font-size: 13px;
                                            line-height: 1.5;
                                            color: #d1d5db;
                                        }
                                        </style>

                                        <script>
                                        jQuery(document).ready(function($) {
                                            // Update preview when settings change
                                            function updatePreview() {
                                                const sectionText = $('#auto_interlinking_section_text').val() || 'Check out our other articles';
                                                const headingSize = $('#auto_interlinking_heading_size').val() || 'h3';
                                                const articleCount = parseInt($('#auto_interlinking_article_count').val()) || 3;

                                                $('#preview-section-text').text(sectionText);
                                                $('#preview-heading-tag').text(headingSize.toUpperCase());

                                                // Update preview links count
                                                const $previewLinks = $('.preview-links');
                                                $previewLinks.empty();

                                                for (let i = 1; i <= articleCount; i++) {
                                                    let linkText = 'Article ' + i;
                                                    if (i === articleCount && articleCount > 1) {
                                                        linkText += ' - Links back to first (closed loop)';
                                                    } else if (i < articleCount) {
                                                        linkText += ' - Next in sequence';
                                                    }
                                                    $previewLinks.append('<li><a href="#">' + linkText + '</a></li>');
                                                }
                                            }

                                            // Bind preview update to form changes
                                            $('#auto_interlinking_section_text, #auto_interlinking_heading_size, #auto_interlinking_article_count').on('input change', updatePreview);

                                            // Initial preview update
                                            updatePreview();
                                        });
                                        </script>
                                    </div>

                                    <div class="slmm-auto-segmentation-section">
                                        <h3>Auto-Segmentation Settings</h3>
                                        <p>Set percentage-based positions for automatic content segmentation. The system will find the nearest paragraph breaks to these positions.</p>
                                        
                                        <div class="slmm-form-field">
                                            <label class="slmm-field-label" for="auto_segment_top_percent">Top Section End Position</label>
                                            <div class="slmm-percentage-input-wrapper">
                                                <input type="number" 
                                                       id="auto_segment_top_percent" 
                                                       name="chatgpt_generator_options[auto_segment_top_percent]" 
                                                       value="<?php $options = get_option('chatgpt_generator_options', array()); echo esc_attr(isset($options['auto_segment_top_percent']) ? $options['auto_segment_top_percent'] : 15); ?>" 
                                                       min="10" 
                                                       max="90" 
                                                       step="1" 
                                                       class="slmm-percentage-input">
                                                <span class="slmm-percentage-unit">%</span>
                                                <span class="slmm-percentage-label">from top</span>
                                            </div>
                                            <p class="slmm-field-description">Where the TOP section should end (default: 15%)</p>
                                        </div>
                                        
                                        <div class="slmm-form-field">
                                            <label class="slmm-field-label" for="auto_segment_bottom_percent">Bottom Section Start Position</label>
                                            <div class="slmm-percentage-input-wrapper">
                                                <input type="number" 
                                                       id="auto_segment_bottom_percent" 
                                                       name="chatgpt_generator_options[auto_segment_bottom_percent]" 
                                                       value="<?php $options = get_option('chatgpt_generator_options', array()); echo esc_attr(isset($options['auto_segment_bottom_percent']) ? $options['auto_segment_bottom_percent'] : 75); ?>" 
                                                       min="10" 
                                                       max="90" 
                                                       step="1" 
                                                       class="slmm-percentage-input">
                                                <span class="slmm-percentage-unit">%</span>
                                                <span class="slmm-percentage-label">from top</span>
                                            </div>
                                            <p class="slmm-field-description">Where the BOTTOM section should start (default: 75%)</p>
                                        </div>

                                    </div>

                                    <!-- CSV Import Configuration Section -->
                                    <div class="slmm-csv-import-config-section">
                                        <h3>CSV Import Configuration</h3>
                                        <p>Configure batch processing settings for CSV data imports</p>

                                        <div class="slmm-form-field-group">
                                            <div class="slmm-form-field">
                                                <label class="slmm-field-label" for="csv_import_batch_size">Import Batch Size</label>
                                                <div class="slmm-input-wrapper">
                                                    <input type="number"
                                                           id="csv_import_batch_size"
                                                           name="chatgpt_generator_options[csv_import_batch_size]"
                                                           value="<?php $options = get_option('chatgpt_generator_options', array()); echo esc_attr(isset($options['csv_import_batch_size']) ? $options['csv_import_batch_size'] : 10); ?>"
                                                           min="5"
                                                           max="50"
                                                           step="1"
                                                           class="slmm-number-input">
                                                    <span class="slmm-input-unit">posts per batch</span>
                                                </div>
                                                <p class="slmm-field-description">Number of posts to process per batch (5-50). Lower values reduce server load.</p>
                                            </div>

                                            <div class="slmm-form-field">
                                                <label class="slmm-field-label" for="csv_import_processing_delay">Processing Delay</label>
                                                <div class="slmm-input-wrapper">
                                                    <input type="number"
                                                           id="csv_import_processing_delay"
                                                           name="chatgpt_generator_options[csv_import_processing_delay]"
                                                           value="<?php $options = get_option('chatgpt_generator_options', array()); echo esc_attr(isset($options['csv_import_processing_delay']) ? $options['csv_import_processing_delay'] : 1); ?>"
                                                           min="0.5"
                                                           max="3"
                                                           step="0.1"
                                                           class="slmm-number-input">
                                                    <span class="slmm-input-unit">seconds</span>
                                                </div>
                                                <p class="slmm-field-description">Delay between batches (0.5-3 seconds) to prevent server overload.</p>
                                            </div>

                                            <div class="slmm-form-field">
                                                <label class="slmm-field-label" for="csv_import_request_timeout">Request Timeout</label>
                                                <div class="slmm-input-wrapper">
                                                    <input type="number"
                                                           id="csv_import_request_timeout"
                                                           name="chatgpt_generator_options[csv_import_request_timeout]"
                                                           value="<?php $options = get_option('chatgpt_generator_options', array()); echo esc_attr(isset($options['csv_import_request_timeout']) ? $options['csv_import_request_timeout'] : 30); ?>"
                                                           min="15"
                                                           max="90"
                                                           step="5"
                                                           class="slmm-number-input">
                                                    <span class="slmm-input-unit">seconds</span>
                                                </div>
                                                <p class="slmm-field-description">Maximum time to wait for each batch request (15-90 seconds).</p>
                                            </div>

                                            <div class="slmm-form-field">
                                                <label class="slmm-checkbox-label">
                                                    <input type="checkbox"
                                                           id="csv_import_debug_mode"
                                                           name="chatgpt_generator_options[csv_import_debug_mode]"
                                                           value="1"
                                                           <?php $options = get_option('chatgpt_generator_options', array()); checked(isset($options['csv_import_debug_mode']) ? $options['csv_import_debug_mode'] : false, true); ?>>
                                                    <span class="slmm-checkmark"></span>
                                                    Enable Debug Logging
                                                </label>
                                                <p class="slmm-field-description">Log detailed information about CSV import operations for troubleshooting.</p>
                                            </div>
                                        </div>

                                        <!-- Field Documentation -->
                                        <div class="slmm-csv-field-docs" style="margin-top: 20px; background: #1a1a1a; border: 1px solid #333; border-radius: 8px; padding: 20px;">
                                            <h4 style="color: #fff; margin: 0 0 15px 0;">CSV Field Reference</h4>
                                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                                <div>
                                                    <h5 style="color: #4caf50; margin: 0 0 10px 0;">✅ EDITABLE Fields</h5>
                                                    <p style="color: #ddd; margin: 0 0 8px 0; font-size: 13px;">These fields can be modified in CSV and will be updated on import:</p>
                                                    <ul style="color: #ccc; font-size: 12px; margin: 0; padding-left: 15px;">
                                                        <li>Title, ACF Title, Status</li>
                                                        <li>Difficulty, Importance, Target Keyword</li>
                                                        <li>Slug, Parent URL, Menu Order</li>
                                                        <li>Summary, Semantic Links</li>
                                                    </ul>
                                                </div>
                                                <div>
                                                    <h5 style="color: #f44336; margin: 0 0 10px 0;">🔒 PROTECTED Fields</h5>
                                                    <p style="color: #ddd; margin: 0 0 8px 0; font-size: 13px;">These fields are automatically generated and ignored on import:</p>
                                                    <ul style="color: #ccc; font-size: 12px; margin: 0; padding-left: 15px;">
                                                        <li>ID, Internal Links, External Links</li>
                                                        <li>Last Modified, Edit URL, View URL</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Page Summarization Settings Section -->
                                    <?php $this->render_page_summarization_settings(); ?>
                                </div>

                                <!-- AI Interlinking Prompts Section -->
                                    <div class="slmm-ai-interlinking-section">
                                        <h3>AI Interlinking Prompts</h3>
                                        <p>Configure AI prompts for generating anchor text suggestions based on hierarchical linking types</p>
                                        
                                        <div id="slmm-interlinking-prompts-container">
                                            <?php
                                            // Get AI interlinking prompts
                                            $interlinking_prompts = get_option('slmm_interlinking_prompts', $this->get_default_interlinking_prompts());
                                            
                                            // Include OpenRouter integration for model selection
                                            require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
                                            $openrouter = new SLMM_OpenRouter_Integration();
                                            
                                            foreach ($interlinking_prompts as $type => $prompt_data) {
                                                $this->render_interlinking_prompt_box($type, $prompt_data, $openrouter);
                                            }
                                            ?>
                                        </div>
                                        
                                        <!-- Linking Rules Configuration -->
                                        <div class="slmm-linking-rules-section" style="margin-top: 2rem;">
                                            <h4>Interlinking Rules Configuration</h4>
                                            <p>Define comprehensive linking rules for hierarchical SEO strategy</p>
                                            <textarea name="slmm_interlinking_rules[prompt]" 
                                                      placeholder="Enter linking rules configuration..." 
                                                      class="slmm-gpt-prompt-content"><?php 
                                                $rules_data = get_option('slmm_interlinking_rules', array());
                                                echo esc_textarea($rules_data['prompt'] ?? $this->get_default_linking_rules()); 
                                            ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Lorem Ipsum Detector Tab -->
                            <div class="slmm-tab-pane" id="lorem-detector">
                                <div class="slmm-tab-header">
                                    <h2>Lorem Ipsum Detector</h2>
                                    <p>Scan your website for Lorem Ipsum placeholder text</p>
                                </div>
                                <div class="slmm-lorem-detector-content">
                                    <div class="slmm-lorem-scanner">
                                        <div class="slmm-lorem-info">
                                            <h3>What does this do?</h3>
                                            <p>This tool scans all your published posts, drafts, and private content to find Lorem Ipsum placeholder text that should be replaced with real content.</p>
                                            <p><strong>Keywords detected:</strong> lorem, ipsum, consectetur, adipiscing, eiusmod, incididunt</p>
                                        </div>
                                        
                                        <div class="slmm-lorem-controls">
                                            <button type="button" id="slmm-start-lorem-scan" class="button button-primary slmm-lorem-scan-btn">
                                                <span class="slmm-scan-text">Start Scan</span>
                                                <span class="slmm-scan-loading" style="display: none;">
                                                    <span class="slmm-spinner"></span>
                                                    Scanning...
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div id="slmm-lorem-results" class="slmm-lorem-results-container">
                                        <!-- Results will be loaded here via AJAX -->
                                    </div>
                                </div>
                            </div>

                            <div class="slmm-form-actions">
                                <?php submit_button('Save Settings', 'primary slmm-save-button', 'submit', false, array('id' => 'slmm-save-settings')); ?>
                            </div>
                        </form>

                        <!-- Search & Replace Form (Outside Main Form) -->
                        <div class="slmm-search-replace-form-container" style="display: none;">
                            <form id="slmm-search-replace-form" class="slmm-search-replace-form" method="POST" action="#">
                                <?php wp_nonce_field('slmm_search_replace', 'slmm_search_replace_nonce'); ?>
                                
                                <div class="slmm-form-row">
                                    <div class="slmm-form-group">
                                        <label for="search_text">Search for:</label>
                                        <input type="text" id="search_text" name="search_text" class="slmm-form-input" placeholder="Enter text to search for..." required>
                                    </div>
                                    
                                    <div class="slmm-form-group">
                                        <label for="replace_text">Replace with:</label>
                                        <input type="text" id="replace_text" name="replace_text" class="slmm-form-input" placeholder="Enter replacement text...">
                                    </div>
                                </div>
                                
                                <div class="slmm-form-row">
                                    <div class="slmm-checkbox-group">
                                        <label class="slmm-checkbox-label">
                                            <input type="checkbox" id="case_insensitive" name="case_insensitive">
                                            <span class="slmm-checkmark"></span>
                                            Case insensitive search
                                        </label>
                                        
                                        <label class="slmm-checkbox-label">
                                            <input type="checkbox" id="dry_run" name="dry_run" checked>
                                            <span class="slmm-checkmark"></span>
                                            Dry run (preview changes only)
                                        </label>
                                        
                                        <label class="slmm-checkbox-label">
                                            <input type="checkbox" id="whole_words" name="whole_words">
                                            <span class="slmm-checkmark"></span>
                                            Match whole words only
                                        </label>

                                        <label class="slmm-checkbox-label">
                                            <input type="checkbox" id="page_content_only" name="page_content_only">
                                            <span class="slmm-checkmark"></span>
                                            Page content only (Classic Editor, Gutenberg, Page Builders)
                                        </label>

                                    </div>
                                </div>

                                <!-- Help Accordion for Search Options -->
                                <div class="slmm-help-accordion">
                                    <button type="button" class="slmm-accordion-toggle" id="search-options-help">
                                        <span class="slmm-help-icon">?</span>
                                        What do these options mean?
                                        <span class="slmm-accordion-arrow">▼</span>
                                    </button>
                                    <div class="slmm-accordion-content" style="display: none;">
                                        <div class="slmm-help-sections">
                                            <div class="slmm-help-section">
                                                <h4>Case insensitive search</h4>
                                                <p>When enabled, the search will match text regardless of uppercase/lowercase. For example, searching for "WordPress" will also find "wordpress", "WORDPRESS", and "WordPress".</p>
                                            </div>
                                            <div class="slmm-help-section">
                                                <h4>Dry run (preview changes only)</h4>
                                                <p>Enables preview mode that shows what changes would be made without actually modifying your database. Always recommended to run this first to verify your search and replace is working correctly.</p>
                                            </div>
                                            <div class="slmm-help-section">
                                                <h4>Match whole words only</h4>
                                                <p>Only matches complete words separated by spaces. For example, searching for "test" will match "test case" but NOT "testing" or "contest". Useful for avoiding unintended partial word replacements.</p>
                                            </div>
                                            <div class="slmm-help-section">
                                                <h4>Page content only (Classic Editor, Gutenberg, Page Builders)</h4>
                                                <p>Restricts the search to page and post content created with any editor type - Classic Editor, Gutenberg blocks, or page builder content. Excludes system data, settings, and metadata.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="slmm-form-row">
                                    <div class="slmm-form-group">
                                        <label>Select Tables to Search:</label>
                                        <div class="slmm-table-selection">
                                            <!-- Table Search Input -->
                                            <div class="slmm-table-search">
                                                <input type="text" id="table-search-input" placeholder="Search tables..." class="slmm-table-search-input" />
                                                <span class="slmm-search-icon">🔍</span>
                                            </div>

                                            <!-- Table Selection Buttons -->
                                            <div class="slmm-table-buttons">
                                                <button type="button" id="select-frontend-tables" class="button button-primary">Front End Only</button>
                                                <button type="button" id="select-classic-editor" class="button button-secondary">Classic Editor Only</button>
                                                <button type="button" id="select-all-tables" class="button button-secondary">Select All</button>
                                                <button type="button" id="select-core-tables" class="button button-secondary">Core Tables Only</button>
                                                <button type="button" id="deselect-all-tables" class="button button-secondary">Deselect All</button>
                                            </div>

                                            <div id="table-list" class="slmm-table-list">
                                                <!-- Table list will be populated via AJAX -->
                                            </div>

                                            <!-- Help Accordion for Table Selection -->
                                            <div class="slmm-help-accordion">
                                                <button type="button" class="slmm-accordion-toggle" id="table-selection-help">
                                                    <span class="slmm-help-icon">?</span>
                                                    What do these table selection options mean?
                                                    <span class="slmm-accordion-arrow">▼</span>
                                                </button>
                                                <div class="slmm-accordion-content" style="display: none;">
                                                    <div class="slmm-help-sections">
                                                        <div class="slmm-help-section">
                                                            <h4>Front End Only</h4>
                                                            <p>Selects tables that affect what visitors see on your website: posts, pages, comments, categories, tags, and theme options. Excludes admin settings, user data, and backend-only tables.</p>
                                                        </div>
                                                        <div class="slmm-help-section">
                                                            <h4>Classic Editor Only</h4>
                                                            <p>Searches only the posts table content created with WordPress Classic Editor. Excludes Gutenberg blocks, page builder content, and other post metadata.</p>
                                                        </div>
                                                        <div class="slmm-help-section">
                                                            <h4>Select All</h4>
                                                            <p>Selects every database table for comprehensive search and replace. Use with caution as this includes system tables, settings, and all plugin data.</p>
                                                        </div>
                                                        <div class="slmm-help-section">
                                                            <h4>Core Tables Only</h4>
                                                            <p>Selects only standard WordPress tables (posts, users, comments, options, etc.). Excludes custom plugin tables and third-party extensions.</p>
                                                        </div>
                                                        <div class="slmm-help-section">
                                                            <h4>Deselect All</h4>
                                                            <p>Clears all table selections. Use this to start fresh or to manually select only specific tables you need.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="slmm-search-replace-actions">
                                    <button type="button" id="slmm-start-search-replace" class="button button-primary slmm-search-replace-btn">
                                        <span class="slmm-search-text">Start Search & Replace</span>
                                        <span class="slmm-search-loading" style="display: none;">
                                            <span class="slmm-spinner"></span>
                                            Processing...
                                        </span>
                                    </button>
                                </div>
                            </form>
                            
                            <div id="slmm-search-replace-results" class="slmm-search-replace-results-container">
                                <!-- Results will be loaded here via AJAX -->
                            </div>
                        </div>

                        <!-- Export/Import Tab -->
                        <div class="slmm-tab-pane" id="export-import">
                            <div class="slmm-tab-header">
                                <h2>Export/Import Settings</h2>
                                <p>Backup and restore your settings</p>
                            </div>
                            
                            <form method="post" enctype="multipart/form-data" class="slmm-export-import-form">
                                <?php wp_nonce_field('slmm_export_import_settings', 'slmm_export_import_nonce'); ?>
                                
                                <div class="slmm-export-section">
                                    <h3 class="slmm-section-title">Export Settings</h3>
                                    <div class="slmm-checkbox-group">
                                        <label class="slmm-checkbox-label">
                                            <input type="checkbox" name="export_chatgpt_generator" checked>
                                            <span class="slmm-checkmark"></span>
                                            Export ChatGPT Generator Settings
                                        </label>
                                        <label class="slmm-checkbox-label">
                                            <input type="checkbox" name="export_slmm_gpt_prompts" checked>
                                            <span class="slmm-checkmark"></span>
                                            Export SLMM GPT Prompts
                                        </label>
                                        <label class="slmm-checkbox-label">
                                            <input type="checkbox" name="export_protected_words" checked>
                                            <span class="slmm-checkmark"></span>
                                            Export Protected Words List
                                        </label>
                                    </div>
                                    <?php submit_button('Export Settings', 'secondary slmm-export-button', 'export_settings', false); ?>
                                </div>

                                <div class="slmm-import-section">
                                    <h3 class="slmm-section-title">Import Settings</h3>
                                    <div class="slmm-file-upload">
                                        <input type="file" name="import_file" accept=".json" id="slmm-import-file">
                                        <label for="slmm-import-file" class="slmm-file-label">
                                            <span class="slmm-file-icon">📁</span>
                                            Choose settings file...
                                        </label>
                                    </div>
                                    <?php submit_button('Import Settings', 'secondary slmm-import-button', 'import_settings', false); ?>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    private function render_tab_fields($section) {
        $fields = array(
            'api_keys' => array(
                'openai_api_key' => 'OpenAI API Key',
                'openrouter_api_key' => 'OpenRouter API Key',
            ),
            'prompts' => array(
                'title_prompt' => 'Title Prompt',
                'description_prompt' => 'Description Prompt',
            ),
            'business_info' => array(
                'business_name' => 'Business Name',
                'phone_number' => 'Phone Number',
            ),
            'models' => array(
                'model_for_title' => 'Title Generation Model',
                'model_for_description' => 'Description Generation Model',
            ),
            'protected_words' => array(
                'protected_words_list' => 'Protected Words List',
            ),
            'features' => array(
                // Features are now handled in render_feature_callbacks with proper grouping
            ),
        );

        if (!isset($fields[$section])) return;

        foreach ($fields[$section] as $field => $label) {
            echo '<div class="slmm-form-field">';
            $this->render_field(array('field' => $field, 'label' => $label));
            echo '</div>';
        }
    }

    private function render_selected_models_section() {
        $options = get_option('chatgpt_generator_options', array());
        $title_model = isset($options['model_for_title']) ? $options['model_for_title'] : 'gpt-4o';
        $title_provider = isset($options['model_for_title_provider']) ? $options['model_for_title_provider'] : 'openai';
        $description_model = isset($options['model_for_description']) ? $options['model_for_description'] : 'gpt-4o';
        $description_provider = isset($options['model_for_description_provider']) ? $options['model_for_description_provider'] : 'openai';
        
        // Format model names for display
        $formatted_title = $this->format_model_name($title_model);
        $formatted_description = $this->format_model_name($description_model);
        
        echo '<div class="slmm-selected-models">';
        echo '<h4>Selected Models</h4>';
        
        echo '<div class="slmm-selected-model-item">';
        echo '<span class="slmm-selected-model-label">Title Generation:</span>';
        echo '<span class="slmm-selected-model-value">' . esc_html($formatted_title) . ' (' . esc_html(ucfirst($title_provider)) . ')</span>';
        echo '</div>';
        
        echo '<div class="slmm-selected-model-item">';
        echo '<span class="slmm-selected-model-label">Description Generation:</span>';
        echo '<span class="slmm-selected-model-value">' . esc_html($formatted_description) . ' (' . esc_html(ucfirst($description_provider)) . ')</span>';
        echo '</div>';
        
        echo '</div>';
    }

    private function render_feature_callbacks() {
        // SEO Features Group
        echo '<div class="slmm-feature-group">';
        echo '<h3 class="slmm-feature-group-title">SEO Features</h3>';
        echo '<div class="slmm-feature-group-content">';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        $this->render_field(array('field' => 'enable_checklist', 'label' => 'Enable SEO Checklist'));
        echo '</div>';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        $this->render_field(array('field' => 'enable_seo_overview', 'label' => 'Enable SEO Overview Meta Box'));
        echo '</div>';
        
        echo '<div class="slmm-form-field">';
        echo '<label class="slmm-field-label">SEO Overview Post Types</label>';
        $this->seo_overview_post_types_callback();
        echo '</div>';
        
        echo '</div></div>';

        // Content Management Features Group
        echo '<div class="slmm-feature-group">';
        echo '<h3 class="slmm-feature-group-title">Content Management</h3>';
        echo '<div class="slmm-feature-group-content">';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        echo '<label class="slmm-field-label">Quick Delete Features</label>';
        $this->enable_page_delete_callback();
        echo '</div>';

        echo '<div class="slmm-form-field">';
        echo '<label class="slmm-field-label">Allowed Post Types for Delete</label>';
        $this->page_delete_post_types_callback();
        echo '</div>';

        echo '<div class="slmm-form-field">';
        echo '<label class="slmm-field-label">Delete Rate Limit</label>';
        echo '<p class="description" style="margin-top: 10px; margin-bottom: 15px;">' . __('Maximum number of deletions allowed per hour per user.', 'chatgpt-generator') . '</p>';
        $this->page_delete_rate_limit_callback();
        echo '</div>';
        
        echo '</div></div>';

        // User Experience Features Group
        echo '<div class="slmm-feature-group">';
        echo '<h3 class="slmm-feature-group-title">User Experience</h3>';
        echo '<div class="slmm-feature-group-content">';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        echo '<label class="slmm-field-label">Keyboard Shortcuts</label>';
        $this->enable_keyboard_shortcuts_callback();
        echo '</div>';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        echo '<label class="slmm-field-label">Debug Logging</label>';
        echo '<p class="description" style="margin-top: 10px; margin-bottom: 15px;">' . __('Enables detailed console logging for troubleshooting. Should be OFF in production for better performance.', 'chatgpt-generator') . '</p>';
        $this->enable_debug_logging_callback();
        echo '</div>';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        echo '<label class="slmm-field-label">Project Notes</label>';
        echo '<p class="description" style="margin-top: 10px; margin-bottom: 15px;">' . __('Add a notes feature to the admin bar for taking project notes across all admin pages.', 'chatgpt-generator') . '</p>';
        $this->enable_notes_callback();
        echo '</div>';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        echo '<label class="slmm-field-label">Development Mode</label>';
        echo '<p class="description" style="margin-top: 10px; margin-bottom: 15px;">' . __('Colorizes the WordPress admin bar to help distinguish between different sites in multiple tabs.', 'chatgpt-generator') . '</p>';
        $this->enable_development_mode_callback();
        echo '</div>';
        
        echo '</div></div>';

        // Security & Access Control Group
        echo '<div class="slmm-feature-group">';
        echo '<h3 class="slmm-feature-group-title">Security & Access Control</h3>';
        echo '<div class="slmm-feature-group-content">';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        echo '<label class="slmm-field-label slmm-visibility-label">Plugin Visibility Settings</label>';
        echo '<p class="description" style="margin-top: 10px; margin-bottom: 15px;">' . __('When enabled, only the specified admin users below will see the plugin in the WordPress admin menu. At least one valid username is required.', 'chatgpt-generator') . '</p>';
        $this->visibility_enabled_callback();
        echo '</div>';

        echo '<div class="slmm-form-field">';
        echo '<label class="slmm-field-label">Authorized Admin Usernames</label>';
        $this->authorized_admins_callback();
        echo '</div>';
        
        echo '</div></div>';
    }

    private function enqueue_settings_assets() {
        // Enqueue Debug Logger first (dependency for all SLMM scripts)
        wp_enqueue_script('slmm-debug-logger', plugin_dir_url(__FILE__) . '../../assets/js/slmm-debug-logger.js', array('jquery'), '1.0.0', true);
        
        // Enqueue WordPress color picker
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');
        
        // Enqueue the custom CSS and JS for the settings page
        wp_enqueue_style('slmm-settings-style', plugin_dir_url(__FILE__) . '../../assets/css/slmm-settings.css', array('wp-color-picker'), '1.0.0');
        wp_enqueue_script('slmm-settings-script', plugin_dir_url(__FILE__) . '../../assets/js/slmm-settings.js', array('jquery', 'wp-color-picker', 'slmm-debug-logger'), '1.0.0', true);
        
        // Enqueue Lorem Ipsum Detector assets
        wp_enqueue_style('slmm-lorem-detector-style', plugin_dir_url(__FILE__) . '../../assets/css/lorem-detector.css', array(), '1.0.0');
        wp_enqueue_script('slmm-lorem-detector-script', plugin_dir_url(__FILE__) . '../../assets/js/lorem-detector.js', array('jquery', 'slmm-debug-logger'), '1.0.0', true);
        
        // Enqueue AI Interlinking Prompts assets (custom design system styling)
        wp_enqueue_style('slmm-interlinking-prompts-style', plugin_dir_url(__FILE__) . '../../assets/css/slmm-interlinking-prompts.css', array(), '1.0.0');
        
        // Enqueue interlinking models handler
        wp_enqueue_script('slmm-interlinking-models', plugin_dir_url(__FILE__) . '../../assets/js/slmm-interlinking-models.js', array('jquery'), '1.0.0', true);
        
        // Localize interlinking models script
        wp_localize_script('slmm-interlinking-models', 'slmmInterlinkingData', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('slmm_model_selector')
        ));
        
        // Enqueue API key validator
        wp_enqueue_script('slmm-api-key-validator', plugin_dir_url(__FILE__) . '../../assets/js/slmm-api-key-validator.js', array('jquery'), '1.0.0', true);
        
        // Localize script for any AJAX calls
        wp_localize_script('slmm-settings-script', 'slmmSettings', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('slmm_settings')
        ));
        
        // Localize Lorem Ipsum Detector script
        wp_localize_script('slmm-lorem-detector-script', 'slmmLoremDetector', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('slmm_lorem_ipsum_scan')
        ));
    }

    public function chatgpt_generator_buttons() {
        ?>
        <div id="chatgpt-title-generator-button" style="margin-bottom: 10px;">
            <button type="button" class="button button-primary generate">Generate Title</button>
            <button type="button" class="button button-secondary auto-populate-titles">Auto-Populate Titles</button>
        </div>
        <div id="chatgpt-description-generator-button" style="margin-bottom: 10px;">
            <button type="button" class="button button-primary generate">Generate Description</button>
            <button type="button" class="button button-secondary auto-populate-descriptions">Auto-Populate Descriptions</button>
            <button type="button" class="button button-secondary prefill-from-clipboard">Prefill From Clipboard</button>
        </div>
        <?php
    }

    public function enqueue_keyboard_shortcuts($hook) {
        // Early return if the setting is not enabled
        if (get_option('enable_keyboard_shortcuts') !== '1') {
            return;
        }
        
        // Register the script
        wp_register_script('slmm-keyboard-shortcuts', 
            plugins_url('/js/slmm-keyboard-shortcuts.js', dirname(dirname(__FILE__))), 
            array('jquery'), 
            SLMM_SEO_VERSION
        );

        // Localize the script with translation data
        $translation_array = array(
            'enabled' => '1' // Pass as string '1' to match the strict comparison in JavaScript
        );
        wp_localize_script('slmm-keyboard-shortcuts', 'SaveWithKeyboard', $translation_array);

        // Enqueue the script
        wp_enqueue_script('slmm-keyboard-shortcuts');
    }

    public function handle_keyboard_shortcut_save() {
        check_ajax_referer('keyboard_shortcut_save', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
        }

        $post_id = intval($_POST['post_id']);
        $result = wp_update_post(array('ID' => $post_id));
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success('Post saved successfully');
        }
    }

    public function handle_refresh_openai_models() {
        check_ajax_referer('refresh_openai_models', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Clear the cached models to force a fresh API call
        delete_transient('slmm_openai_models');
        
        // Fetch fresh models
        $models = $this->get_openai_models();
        
        wp_send_json_success('Models refreshed successfully');
    }

    /**
     * Migrate old model settings to new format
     */
    public function migrate_old_settings() {
        // Check if migration has already been done
        if (get_option('slmm_seo_models_migrated', false)) {
            return;
        }

        $options = get_option('chatgpt_generator_options', array());
        $needs_update = false;

        // Migrate old chatgpt_title_model to model_for_title
        $old_title_model = get_option('chatgpt_title_model');
        if ($old_title_model && !isset($options['model_for_title'])) {
            $options['model_for_title'] = $old_title_model;
            $needs_update = true;
        }

        // Migrate old chatgpt_description_model to model_for_description
        $old_description_model = get_option('chatgpt_description_model');
        if ($old_description_model && !isset($options['model_for_description'])) {
            $options['model_for_description'] = $old_description_model;
            $needs_update = true;
        }

        // Migrate other old settings
        $old_mappings = array(
            'chatgpt_title_prompt' => 'title_prompt',
            'chatgpt_description_prompt' => 'description_prompt',
            'chatgpt_business_name' => 'business_name',
            'chatgpt_phone_number' => 'phone_number'
        );

        foreach ($old_mappings as $old_key => $new_key) {
            $old_value = get_option($old_key);
            if ($old_value && !isset($options[$new_key])) {
                $options[$new_key] = $old_value;
                $needs_update = true;
            }
        }

        // Update the options if needed
        if ($needs_update) {
            update_option('chatgpt_generator_options', $options);
        }

        // Mark migration as complete
        update_option('slmm_seo_models_migrated', true);
    }

    /**
     * Handle AJAX request to get models for a specific provider
     */
    public function handle_get_provider_models() {
        check_ajax_referer('slmm_model_selector', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $provider = isset($_POST['provider']) ? sanitize_text_field($_POST['provider']) : 'openai';
        $field = isset($_POST['field']) ? sanitize_text_field($_POST['field']) : '';
        
        $models = array();
        
        if ($provider === 'openrouter') {
            require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
            $openrouter = new SLMM_OpenRouter_Integration();
            
            if ($openrouter->is_configured()) {
                $models = $openrouter->get_models();
            } else {
                wp_send_json_error('OpenRouter API key not configured');
            }
        } else {
            $models = $this->get_openai_models();
        }
        
        wp_send_json_success(array(
            'models' => $models,
            'provider' => $provider,
            'field' => $field
        ));
    }

    /**
     * Handle AJAX request to refresh models
     */
    public function handle_refresh_models() {
        check_ajax_referer('slmm_model_selector', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $provider = isset($_POST['provider']) ? sanitize_text_field($_POST['provider']) : 'openai';
        $field = isset($_POST['field']) ? sanitize_text_field($_POST['field']) : '';
        
        $models = array();
        
        if ($provider === 'openrouter') {
            require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
            $openrouter = new SLMM_OpenRouter_Integration();
            
            if ($openrouter->is_configured()) {
                // Clear cache first
                $openrouter->clear_model_cache();
                $models = $openrouter->get_models();
            } else {
                wp_send_json_error('OpenRouter API key not configured');
            }
        } else {
            // Clear OpenAI cache first
            delete_transient('slmm_openai_models');
            $models = $this->get_openai_models();
        }
        
        wp_send_json_success(array(
            'models' => $models,
            'provider' => $provider,
            'field' => $field
        ));
    }

    /**
     * Handle AJAX request to clear model cache
     */
    public function handle_clear_model_cache() {
        check_ajax_referer('slmm_model_selector', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        // Clear OpenAI cache
        delete_transient('slmm_openai_models');
        
        // Clear OpenRouter cache
        require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
        $openrouter = new SLMM_OpenRouter_Integration();
        $openrouter->clear_model_cache();
        
        wp_send_json_success('Model cache cleared');
    }

    /**
     * Handle AJAX request to save model selection
     */
    public function handle_save_model_selection() {
        // Log the incoming request for debugging
        error_log('SLMM Save Model Selection - POST data: ' . print_r($_POST, true));
        
        check_ajax_referer('slmm_model_selector', 'nonce');
        
        if (!current_user_can('manage_options')) {
            error_log('SLMM Save Model Selection - Permission denied');
            wp_send_json_error('Insufficient permissions');
        }
        
        // Safely get POST data with null checks
        $field = isset($_POST['field']) ? sanitize_text_field($_POST['field']) : '';
        $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : '';
        $provider = isset($_POST['provider']) ? sanitize_text_field($_POST['provider']) : 'openai';
        
        error_log("SLMM Save Model Selection - Parsed data: field=$field, model=$model, provider=$provider");
        
        // Validate required fields
        if (empty($field) || empty($model)) {
            error_log('SLMM Save Model Selection - Missing required fields');
            wp_send_json_error('Missing required fields: field and model are required');
        }
        
        // Get current options
        $options = get_option('chatgpt_generator_options', array());
        error_log('SLMM Save Model Selection - Current options: ' . print_r($options, true));
        
        // Save model selection
        $options[$field] = $model;
        
        // Save provider selection
        $provider_field = $field . '_provider';
        $options[$provider_field] = $provider;
        
        error_log('SLMM Save Model Selection - Updated options: ' . print_r($options, true));
        
        // Update options
        $updated = update_option('chatgpt_generator_options', $options);
        error_log("SLMM Save Model Selection - Update result: " . ($updated ? 'true' : 'false'));
        
        // Verify the save worked
        $saved_options = get_option('chatgpt_generator_options', array());
        error_log('SLMM Save Model Selection - Saved options verification: ' . print_r($saved_options, true));
        
        if (isset($saved_options[$field]) && $saved_options[$field] === $model) {
            error_log('SLMM Save Model Selection - Success');
            wp_send_json_success(array(
                'message' => 'Model selection saved',
                'field' => $field,
                'model' => $model,
                'provider' => $provider
            ));
        } else {
            error_log('SLMM Save Model Selection - Verification failed');
            wp_send_json_error('Failed to save model selection - verification failed');
        }
    }

    /**
     * Handle AJAX request to get database tables
     */
    public function handle_get_tables() {
        check_ajax_referer('slmm_search_replace', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        global $wpdb;
        
        try {
            // Get all tables with current prefix
            $tables = $wpdb->get_results("SHOW TABLES", ARRAY_N);
            $table_list = array();
            
            foreach ($tables as $table) {
                $table_name = $table[0];
                // Get table row count - sanitize table name to prevent SQL injection
                $table_name_escaped = esc_sql($table_name);
                $count = $wpdb->get_var("SELECT COUNT(*) FROM `{$table_name_escaped}`");
                $table_list[] = array(
                    'name' => $table_name,
                    'rows' => intval($count),
                    'is_core' => $this->is_core_table($table_name)
                );
            }
            
            wp_send_json_success(array(
                'tables' => $table_list
            ));
        } catch (Exception $e) {
            wp_send_json_error('Failed to get table list: ' . $e->getMessage());
        }
    }

    /**
     * Handle AJAX request to regenerate emergency access key
     */
    public function handle_regenerate_emergency_key() {
        // Security checks
        if (!wp_verify_nonce($_POST['nonce'], 'slmm_regenerate_emergency')) {
            wp_die('Security check failed');
        }

        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        // Regenerate key and get new URL
        $new_emergency_key = slmm_seo_regenerate_emergency_key();
        $new_url = slmm_seo_get_emergency_full_url();

        wp_send_json_success(array(
            'new_url' => esc_url($new_url),
            'message' => 'Emergency URL regenerated successfully. Previous URL is now invalid.'
        ));
    }

    /**
     * Handle AJAX request for search and replace operations
     */
    public function handle_search_replace() {
        check_ajax_referer('slmm_search_replace', 'nonce');

        // ENHANCED AUTHORIZATION - Multiple security layers for destructive operations
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        // CRITICAL: WHITELISTED ADMIN VERIFICATION - Only authorized admins can access search/replace
        $current_user = wp_get_current_user();
        $options = get_option('chatgpt_generator_options', array());
        $authorized_admins = isset($options['authorized_admins']) ? (array) $options['authorized_admins'] : array();

        if (!in_array($current_user->user_login, $authorized_admins)) {
            error_log("SLMM Security: Unauthorized search/replace attempt by user: " . $current_user->user_login . " (not whitelisted)");
            wp_send_json_error('Access denied: Only whitelisted administrators can perform database search/replace operations');
        }

        // Additional SLMM authorization check
        if (!slmm_seo_check_visibility_authorization()) {
            wp_send_json_error('SLMM authorization required for database operations');
        }

        // Require explicit confirmation for non-dry-run operations
        $dry_run = isset($_POST['dry_run']) && $_POST['dry_run'] === 'true';
        $confirmed = isset($_POST['confirmed']) && $_POST['confirmed'] === 'true';

        if (!$dry_run && !$confirmed) {
            wp_send_json_error('Destructive database operations require explicit confirmation');
        }


        
        global $wpdb;

        // Enhanced input sanitization and validation with proper error handling
        try {
            $search_text = isset($_POST['search_text']) ? $this->sanitize_search_input($_POST['search_text']) : '';
            $replace_text = isset($_POST['replace_text']) ? $this->sanitize_search_input($_POST['replace_text']) : '';
        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
            return;
        }
        $case_insensitive = isset($_POST['case_insensitive']) && $_POST['case_insensitive'] === 'true';
        $dry_run = isset($_POST['dry_run']) && $_POST['dry_run'] === 'true';
        $whole_words = isset($_POST['whole_words']) && $_POST['whole_words'] === 'true';
        $page_content_only = isset($_POST['page_content_only']) && $_POST['page_content_only'] === 'true';
        $selected_tables = isset($_POST['selected_tables']) ? (array) $_POST['selected_tables'] : array();
        $approved_posts = isset($_POST['approved_posts']) ? array_map('intval', $_POST['approved_posts']) : array();

        
        // Sanitize table names
        $selected_tables = array_map(array($this, 'sanitize_table_name'), $selected_tables);
        
        // Validate input
        if (empty($search_text)) {
            wp_send_json_error('Search text cannot be empty');
        }
        
        if (empty($selected_tables)) {
            wp_send_json_error('Please select at least one table');
        }
        
        try {
            $results = array();
            $total_replacements = 0;
            $total_field_replacements = 0;
            $total_rows_affected = 0;
            $all_affected_posts = array();
            $all_modified_columns = array();
            $all_change_details = array();
            
            foreach ($selected_tables as $table_name) {
                $table_name = sanitize_text_field($table_name);
                
                // Verify table exists
                if (!$this->table_exists($table_name)) {
                    continue;
                }
                
                $table_results = $this->process_table_search_replace(
                    $table_name,
                    $search_text,
                    $replace_text,
                    $case_insensitive,
                    $whole_words,
                    $dry_run,
                    $page_content_only,
                    $approved_posts
                );
                
                if ($table_results['replacements'] > 0 || $table_results['rows_affected'] > 0) {
                    $results[$table_name] = $table_results;
                    $total_replacements += $table_results['replacements'];
                    $total_field_replacements += $table_results['field_replacements'];
                    $total_rows_affected += $table_results['rows_affected'];
                    
                    // Aggregate detailed information
                    $all_affected_posts = array_merge($all_affected_posts, $table_results['affected_posts']);
                    $all_change_details = array_merge($all_change_details, $table_results['change_details']);
                    
                    // Merge column statistics
                    foreach ($table_results['modified_columns'] as $column => $count) {
                        if (!isset($all_modified_columns[$column])) {
                            $all_modified_columns[$column] = 0;
                        }
                        $all_modified_columns[$column] += $count;
                    }
                }
            }
            
            // AUDIT LOGGING - Log all database modifications for security tracking
            if (!$dry_run && $total_rows_affected > 0) {
                $this->log_database_modification(array(
                    'user_id' => $user_id,
                    'user_login' => wp_get_current_user()->user_login,
                    'operation' => 'search_replace',
                    'search_term' => $search_text,
                    'replace_term' => $replace_text,
                    'tables_affected' => array_keys($results),
                    'total_rows_affected' => $total_rows_affected,
                    'total_replacements' => $total_replacements,
                    'affected_posts_count' => count($all_affected_posts),
                    'timestamp' => current_time('mysql'),
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
                ));

            }

            wp_send_json_success(array(
                'results' => $results,
                'total_replacements' => $total_replacements,
                'total_field_replacements' => $total_field_replacements,
                'total_rows_affected' => $total_rows_affected,
                'affected_posts' => $all_affected_posts,
                'modified_columns' => $all_modified_columns,
                'change_details' => $all_change_details,
                'dry_run' => $dry_run,
                'summary' => array(
                    'tables_processed' => count($selected_tables),
                    'tables_with_changes' => count($results),
                    'search_term' => $search_text,
                    'replace_term' => $replace_text,
                    'case_insensitive' => $case_insensitive,
                    'whole_words' => $whole_words
                ),
                // Add search parameters for "Show" button functionality
                'search_params' => array(
                    'search_text' => $search_text,
                    'case_insensitive' => $case_insensitive,
                    'whole_words' => $whole_words
                )
            ));
            
        } catch (Exception $e) {
            wp_send_json_error('Search and replace failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle AJAX request to get matched rows from a specific table
     * Based on devkit plugin's successful implementation
     */
    public function handle_get_matched_rows() {
        check_ajax_referer('slmm_search_replace', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        global $wpdb;

        $table = isset($_POST['table']) ? sanitize_text_field($_POST['table']) : '';
        $search_text = isset($_POST['search']) ? $this->sanitize_search_input($_POST['search']) : '';
        $case_insensitive = isset($_POST['case_insensitive']) && $_POST['case_insensitive'] === 'true';
        $whole_words = isset($_POST['whole_words']) && $_POST['whole_words'] === 'true';

        if (empty($table) || empty($search_text)) {
            wp_send_json_error('Table name and search value cannot be empty.');
        }

        // Get all columns for the current table
        $columns = $wpdb->get_results("SHOW COLUMNS FROM `$table`", ARRAY_A);
        $matched_rows = array();
        $processed_ids = array();

        foreach ($columns as $column) {
            $column_name = $column['Field'];
            $column_type = strtolower($column['Type']);

            // Only process text-based columns
            if (strpos($column_type, 'char') !== false ||
                strpos($column_type, 'text') !== false ||
                strpos($column_type, 'blob') === false) { // Exclude blob types

                // Build the WHERE clause based on search options
                $where_clause = $this->build_search_where_clause($column_name, $search_text, $case_insensitive, $whole_words);

                // Get rows with matched values
                $query = "SELECT * FROM `$table` WHERE $where_clause";
                $rows = $wpdb->get_results($query, ARRAY_A);

                foreach ($rows as $row) {
                    // Use primary key or unique identifier to avoid duplicates
                    $row_id = isset($row['ID']) ? $row['ID'] :
                             (isset($row['id']) ? $row['id'] :
                             (isset($row['post_id']) ? $row['post_id'] :
                             md5(serialize($row))));

                    if (!isset($processed_ids[$row_id])) {
                        $highlighted_row = array();

                        foreach ($row as $key => $value) {
                            // Highlight matching text in all columns
                            if (is_string($value) && !empty($value)) {
                                $highlighted_value = $this->highlight_search_term($value, $search_text, $case_insensitive, $whole_words);
                                $highlighted_row[$key] = $highlighted_value;
                            } else {
                                $highlighted_row[$key] = $value;
                            }
                        }

                        $matched_rows[] = $highlighted_row;
                        $processed_ids[$row_id] = true;
                    }
                }
            }
        }

        // Generate HTML table for display
        $html = $this->generate_matched_rows_html($table, $matched_rows, $columns);

        wp_send_json_success(array(
            'html' => $html,
            'count' => count($matched_rows)
        ));
    }

    /**
     * Build WHERE clause for search based on options
     */
    private function build_search_where_clause($column_name, $search_text, $case_insensitive, $whole_words) {
        global $wpdb;

        if ($whole_words) {
            // Handle multi-word phrases for whole word matching
            if (strpos($search_text, ' ') !== false) {
                // Multi-word phrase: use REGEXP for word boundaries
                $escaped_text = preg_quote($search_text, '/');
                $escaped_text = str_replace(' ', '[[:space:]]+', $escaped_text);
                $pattern = '[[:<:]]' . $escaped_text . '[[:>:]]';

                if ($case_insensitive) {
                    return "`$column_name` REGEXP " . $wpdb->prepare('%s', $pattern);
                } else {
                    return "`$column_name` REGEXP BINARY " . $wpdb->prepare('%s', $pattern);
                }
            } else {
                // Single word: use REGEXP word boundaries
                $pattern = '[[:<:]]' . preg_quote($search_text, '/') . '[[:>:]]';

                if ($case_insensitive) {
                    return "`$column_name` REGEXP " . $wpdb->prepare('%s', $pattern);
                } else {
                    return "`$column_name` REGEXP BINARY " . $wpdb->prepare('%s', $pattern);
                }
            }
        } else {
            // Regular LIKE search
            return $wpdb->prepare("`$column_name` LIKE %s", '%' . $wpdb->esc_like($search_text) . '%');
        }
    }

    /**
     * Highlight search terms in text
     */
    private function highlight_search_term($text, $search_text, $case_insensitive, $whole_words) {
        if ($whole_words) {
            // Handle multi-word phrases
            if (strpos($search_text, ' ') !== false) {
                $escaped_text = preg_quote($search_text, '/');
                $escaped_text = preg_replace('/\s+/', '\\s+', $escaped_text);
                $pattern = '/\b' . $escaped_text . '\b/';
            } else {
                $pattern = '/\b' . preg_quote($search_text, '/') . '\b/';
            }

            if ($case_insensitive) {
                $pattern .= 'i';
            }

            return preg_replace($pattern, '<span class="slmm-highlight">$0</span>', $text);
        } else {
            // Simple replace for non-whole-word searches
            if ($case_insensitive) {
                return preg_replace('/' . preg_quote($search_text, '/') . '/i', '<span class="slmm-highlight">$0</span>', $text);
            } else {
                return str_replace($search_text, '<span class="slmm-highlight">' . $search_text . '</span>', $text);
            }
        }
    }

    /**
     * Generate HTML table for matched rows
     */
    private function generate_matched_rows_html($table_name, $rows, $columns) {
        if (empty($rows)) {
            return '<p>No matches found in table: ' . esc_html($table_name) . '</p>';
        }

        $html = '<div class="slmm-matched-rows-container">';
        $html .= '<h3>Matched Rows in Table: ' . esc_html($table_name) . ' (' . count($rows) . ' matches)</h3>';

        // Display each row as a vertical table
        $row_index = 1;
        foreach ($rows as $row) {
            $html .= '<div class="slmm-matched-row-item">';
            $html .= '<h4>Row ' . $row_index . '</h4>';
            $html .= '<table class="slmm-vertical-table wp-list-table widefat striped">';
            $html .= '<tbody>';

            // Display each field as a row (field name -> value)
            foreach ($columns as $column) {
                $column_name = $column['Field'];
                $column_type = $column['Type'];
                $value = isset($row[$column_name]) ? $row[$column_name] : '';

                $html .= '<tr>';
                $html .= '<th scope="row" style="width: 200px; font-weight: 600;">' . esc_html($column_name) . '</th>';
                $html .= '<td>';

                // Handle different value types
                if (empty($value) || $value === null) {
                    $html .= '<span style="color: #999; font-style: italic;">NULL</span>';
                } elseif (strpos($column_type, 'text') !== false || strpos($column_type, 'blob') !== false) {
                    // For text/blob fields, truncate if too long (unless it has highlights)
                    if (strlen($value) > 500 && strpos($value, 'slmm-highlight') === false) {
                        $html .= substr($value, 0, 500) . '...';
                    } else {
                        $html .= $value;
                    }
                } elseif (strpos($column_name, 'date') !== false || strpos($column_name, 'time') !== false) {
                    // Format dates/times
                    $html .= '<code>' . esc_html($value) . '</code>';
                } else {
                    $html .= $value;
                }

                $html .= '</td>';
                $html .= '</tr>';
            }

            $html .= '</tbody>';
            $html .= '</table>';
            $html .= '</div>';

            $row_index++;
        }

        $html .= '</div>';

        // Add CSS for highlighting and vertical table layout
        $html .= '<style>
            .slmm-highlight {
                background-color: yellow;
                font-weight: bold;
                padding: 1px 2px;
            }
            .slmm-matched-rows-container {
                margin: 20px 0;
                padding: 15px;
                background: #2a2a2a;
                border: 1px solid #4a4a4a;
                border-radius: 8px;
                max-height: 600px;
                overflow-y: auto;
                color: #f1f5f9;
            }
            .slmm-matched-row-item {
                background: #2a2a2a;
                border: 1px solid #4a4a4a;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
                color: #f1f5f9;
            }
            .slmm-matched-row-item h4 {
                margin-top: 0;
                color: #f1f5f9;
                font-size: 14px;
                font-weight: 600;
                border-bottom: 1px solid #4a4a4a;
                padding-bottom: 8px;
                margin-bottom: 10px;
            }
            .slmm-vertical-table {
                margin: 0;
                border: none;
            }
            .slmm-vertical-table th {
                background: #f9f9f9;
                text-align: left;
                padding: 8px 10px;
                border-bottom: 1px solid #eee;
                vertical-align: top;
            }
            .slmm-vertical-table td {
                padding: 8px 10px;
                border-bottom: 1px solid #eee;
                word-break: break-word;
            }
            .slmm-vertical-table tr:last-child th,
            .slmm-vertical-table tr:last-child td {
                border-bottom: none;
            }
            .slmm-matched-rows-container h3 {
                margin-top: 0;
                color: #f1f5f9;
            }
        </style>';

        return $html;
    }

    /**
     * Log database modifications for security audit trail
     */
    private function log_database_modification($operation_data) {
        // Log to WordPress error log for persistent tracking
        error_log('SLMM Database Modification: ' . json_encode($operation_data));

        // Store in WordPress options for admin dashboard viewing
        $log_entries = get_option('slmm_database_audit_log', array());

        // Keep only last 100 entries to prevent database bloat
        if (count($log_entries) >= 100) {
            $log_entries = array_slice($log_entries, -99);
        }

        $log_entries[] = $operation_data;
        update_option('slmm_database_audit_log', $log_entries);

        // Send critical operation alerts for large changes
        if ($operation_data['total_rows_affected'] > 100) {
            $this->send_critical_operation_alert($operation_data);
        }
    }

    /**
     * Send alert for critical database operations
     */
    private function send_critical_operation_alert($operation_data) {
        $admin_email = get_option('admin_email');
        $site_name = get_option('blogname');

        $subject = sprintf('[%s] Critical Database Operation Alert', $site_name);
        $message = sprintf(
            "A large database operation was performed on your site:\n\n" .
            "User: %s (ID: %s)\n" .
            "Operation: Search & Replace\n" .
            "Rows Affected: %s\n" .
            "Tables: %s\n" .
            "Search Term: %s\n" .
            "Replace Term: %s\n" .
            "Timestamp: %s\n" .
            "IP Address: %s\n\n" .
            "If this was not authorized, please review your site security immediately.",
            $operation_data['user_login'],
            $operation_data['user_id'],
            $operation_data['total_rows_affected'],
            implode(', ', $operation_data['tables_affected']),
            $operation_data['search_term'],
            $operation_data['replace_term'],
            $operation_data['timestamp'],
            $operation_data['ip_address']
        );

        wp_mail($admin_email, $subject, $message);
    }

    /**
     * Check if a table is a WordPress core table
     */
    private function is_core_table($table_name) {
        global $wpdb;
        
        $core_tables = array(
            $wpdb->posts,
            $wpdb->postmeta,
            $wpdb->users,
            $wpdb->usermeta,
            $wpdb->comments,
            $wpdb->commentmeta,
            $wpdb->terms,
            $wpdb->term_taxonomy,
            $wpdb->term_relationships,
            $wpdb->options
        );
        
        return in_array($table_name, $core_tables);
    }

    /**
     * Check if a table exists in the database
     */
    private function table_exists($table_name) {
        global $wpdb;

        $result = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table_name));
        return $result === $table_name;
    }

    /**
     * Process search and replace for a single table
     */
    private function process_table_search_replace($table_name, $search_text, $replace_text, $case_insensitive, $whole_words, $dry_run, $page_content_only = false, $approved_posts = array()) {
        global $wpdb;
        
        $table_name = esc_sql($table_name);
        $replacements = 0; // Now counts actual string replacements
        $field_replacements = 0; // Counts field-level changes
        $rows_affected = 0;
        $affected_posts = array(); // Track specific affected posts
        $modified_columns = array(); // Track which columns were modified
        $change_details = array(); // Detailed change information
        
        // Get table structure - table name already escaped with esc_sql() above
        $columns = $wpdb->get_results("DESCRIBE `{$table_name}`", ARRAY_A);
        $primary_key = null;
        
        foreach ($columns as $column) {
            if ($column['Key'] === 'PRI') {
                $primary_key = $column['Field'];
                break;
            }
        }
        
        if (!$primary_key) {
            return array(
                'replacements' => 0,
                'rows_affected' => 0,
                'error' => 'No primary key found'
            );
        }
        
        // Build search pattern
        if ($whole_words) {
            $search_pattern = $case_insensitive ? 
                "[[:<:]]{$search_text}[[:>:]]" : 
                "[[:<:]]{$search_text}[[:>:]]";
        } else {
            $search_pattern = $search_text;
        }
        
        // Process rows in batches (BSR standard: 20,000 rows)
        $batch_size = 20000;
        $offset = 0;
        
        do {
            // Build table-specific query with filtering
            $query = $this->build_filtered_query($table_name, $batch_size, $offset);
            $rows = $wpdb->get_results($query, ARRAY_A);
            
            foreach ($rows as $row) {
                $updated_row = $row;
                $row_changed = false;

                // Skip posts not in approved list if approval filtering is enabled
                if (!empty($approved_posts) && $table_name === $wpdb->posts && isset($row['ID'])) {
                    if (!in_array($row['ID'], $approved_posts)) {
                        continue; // Skip unapproved posts
                    }
                }

                // Page content only filtering
                $columns_to_process = array();
                if ($page_content_only && $table_name === $wpdb->posts) {
                    // Only process post_content column for page content only mode
                    foreach ($columns as $col) {
                        if ($col['Field'] === 'post_content') {
                            $columns_to_process[] = $col;
                        }
                    }
                } else if ($table_name === $wpdb->posts && $this->should_skip_post_row($row)) {
                    // Row-level protection - skip entire row if it's unwanted content (fallback)
                    continue; // Skip this entire row
                } else {
                    // Process all columns normally
                    $columns_to_process = $columns;
                }

                foreach ($columns_to_process as $column) {
                    $column_name = $column['Field'];
                    $column_value = $row[$column_name];
                    
                    if (!is_string($column_value) || empty($column_value)) {
                        continue;
                    }
                    
                    // WordPress core data protection for wp_options table
                    if ($column_name === 'option_name' && isset($row['option_name'])) {
                        if ($this->should_skip_core_option($row['option_name'], $table_name)) {
                            continue;
                        }
                    }
                    
                    // Count actual string replacements before processing
                    $original_replacement_count = $this->count_string_replacements($column_value, $search_text, $case_insensitive, $whole_words);
                    
                    // Use enhanced recursive search and replace for all data types
                    $updated_value = $this->recursive_search_replace(
                        $column_value,
                        $search_text,
                        $replace_text,
                        $case_insensitive,
                        $whole_words,
                        false // Let the method detect if it's serialized
                    );
                    
                    if ($updated_value !== $column_value) {
                        $updated_row[$column_name] = $updated_value;
                        $row_changed = true;
                        $field_replacements++; // Count field-level changes
                        $replacements += $original_replacement_count; // Count actual string replacements
                        
                        // Track modified columns for detailed reporting
                        if (!isset($modified_columns[$column_name])) {
                            $modified_columns[$column_name] = 0;
                        }
                        $modified_columns[$column_name]++;
                        
                        // Store change details for specific posts
                        if ($table_name === $wpdb->posts && isset($row['ID'])) {
                            $change_details[] = array(
                                'post_id' => $row['ID'],
                                'post_title' => isset($row['post_title']) ? $row['post_title'] : '',
                                'column' => $column_name,
                                'replacements' => $original_replacement_count,
                                'preview' => $this->generate_change_preview($column_value, $updated_value, $search_text, $replace_text, $whole_words, $case_insensitive)
                            );
                        }
                    }
                }
                
                // Update the row if changed and not dry run
                if ($row_changed) {
                    $rows_affected++;
                    
                    // ENHANCED DRY-RUN PREVIEW - Track affected posts with clickable edit links
                    if ($table_name === $wpdb->posts && isset($row['ID'])) {
                        $post_id = $row['ID'];
                        $post_title = isset($row['post_title']) ? $row['post_title'] : 'Untitled';
                        $post_type = isset($row['post_type']) ? $row['post_type'] : 'post';
                        $post_status = isset($row['post_status']) ? $row['post_status'] : 'publish';

                        // Generate edit and view URLs
                        $edit_url = admin_url('post.php?post=' . $post_id . '&action=edit');
                        $view_url = get_permalink($post_id);

                        // Find which columns were changed and show preview of changes
                        $column_changes = array();
                        foreach ($columns as $column_info) {
                            $column = $column_info['Field'];
                            if (isset($updated_row[$column]) && $updated_row[$column] !== $row[$column]) {
                                // Generate enhanced preview with highlighting data
                                $preview_data = $this->generate_change_preview($row[$column], $updated_row[$column], $search_text, $replace_text, $whole_words, $case_insensitive);

                                $column_change = array(
                                    'column' => $column,
                                    'original_preview' => substr($row[$column], 0, 200) . (strlen($row[$column]) > 200 ? '...' : ''),
                                    'new_preview' => substr($updated_row[$column], 0, 200) . (strlen($updated_row[$column]) > 200 ? '...' : '')
                                );

                                // Add enhanced preview data if available
                                if (isset($preview_data['before_text'])) {
                                    $column_change['before_text'] = $preview_data['before_text'];
                                    $column_change['match_text'] = $preview_data['match_text'];
                                    $column_change['after_text'] = $preview_data['after_text'];
                                    $column_change['before_text_updated'] = $preview_data['before_text_updated'];
                                    $column_change['replacement_text'] = $preview_data['replacement_text'];
                                    $column_change['after_text_updated'] = $preview_data['after_text_updated'];
                                }

                                $column_changes[] = $column_change;
                            }
                        }

                        $affected_posts[] = array(
                            'ID' => $post_id,
                            'post_title' => $post_title,
                            'post_type' => $post_type,
                            'post_status' => $post_status,
                            'edit_url' => $edit_url,
                            'view_url' => $view_url ?: '',
                            'table' => $table_name,
                            'column_changes' => $column_changes,
                            'total_changes' => count($column_changes)
                        );
                    } elseif ($table_name === $wpdb->postmeta && isset($row['post_id'])) {
                        // Handle post meta changes
                        $post_id = $row['post_id'];
                        $post = get_post($post_id);
                        if ($post) {
                            $edit_url = admin_url('post.php?post=' . $post_id . '&action=edit');
                            $affected_posts[] = array(
                                'ID' => $post_id,
                                'post_title' => $post->post_title,
                                'post_type' => $post->post_type,
                                'edit_url' => $edit_url,
                                'view_url' => get_permalink($post_id),
                                'table' => $table_name,
                                'meta_key' => isset($row['meta_key']) ? $row['meta_key'] : '',
                                'change_type' => 'Post Meta'
                            );
                        }
                    } elseif ($table_name === $wpdb->options && isset($row['option_name'])) {
                        $affected_posts[] = array(
                            'option_name' => $row['option_name'],
                            'option_value_preview' => substr($row['option_value'], 0, 150) . '...',
                            'table' => $table_name,
                            'change_type' => 'Site Option',
                            'edit_url' => admin_url('options-general.php'), // Generic settings page
                            'original_length' => strlen($row['option_value']),
                            'new_length' => strlen($updated_row['option_value'])
                        );
                    } else {
                        // Handle other table types with generic information
                        $primary_key_value = isset($row[$primary_key]) ? $row[$primary_key] : 'Unknown';
                        $affected_posts[] = array(
                            'table' => $table_name,
                            'primary_key' => $primary_key,
                            'primary_key_value' => $primary_key_value,
                            'change_type' => 'Database Record',
                            'changes_count' => count(array_diff_assoc($updated_row, $row))
                        );
                    }
                    
                    if (!$dry_run) {
                        $where = array($primary_key => $row[$primary_key]);
                        unset($updated_row[$primary_key]);
                        $wpdb->update($table_name, $updated_row, $where);
                    }
                }
            }
            
            $offset += $batch_size;
        } while (count($rows) === $batch_size);
        
        return array(
            'replacements' => $replacements, // Actual string replacements
            'field_replacements' => $field_replacements, // Field-level changes
            'rows_affected' => $rows_affected, // Rows modified
            'affected_posts' => $affected_posts, // Specific affected items
            'modified_columns' => $modified_columns, // Column modification stats
            'change_details' => $change_details // Detailed change information
        );
    }

    /**
     * Safe unserialization method to prevent code injection
     */
    /**
     * Enhanced safe unserialization with BSR-standard security
     * Implements protection against code injection and malicious data
     */
    private function safe_unserialize($serialized_string) {
        if (!is_serialized($serialized_string)) {
            return false;
        }

        $serialized_string = trim($serialized_string);
        
        // Enhanced security checks before unserialization
        if (!$this->validate_serialized_data($serialized_string)) {
            return false;
        }

        try {
            // Use safe unserialization to prevent code injection
            if (PHP_VERSION_ID >= 70000) {
                // PHP 7.0+ with allowed_classes restriction
                $unserialized = @unserialize($serialized_string, array('allowed_classes' => false));
            } else {
                // Enhanced fallback for older PHP versions
                $unserialized = $this->safe_unserialize_legacy($serialized_string);
            }
            
            // Additional post-unserialization security checks
            if ($unserialized !== false && $this->contains_unsafe_objects($unserialized)) {
                error_log('SLMM Search Replace: Unsafe object detected in unserialized data');
                return false;
            }
            
            return $unserialized;
        } catch (Exception $e) {
            // Log error and return false on failure
            error_log('SLMM Search Replace: Unserialization failed - ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Validate serialized data for security issues before unserialization
     * Based on BSR security patterns
     */
    private function validate_serialized_data($data) {
        // Check for obviously malicious patterns
        $dangerous_patterns = array(
            '/O:\d+:"/', // Object serialization (we're blocking all objects)
            '/C:\d+:"/', // Custom class serialization
            '/R:\d+;/',  // Reference serialization
            '/r:\d+;/',  // Lowercase reference
        );
        
        foreach ($dangerous_patterns as $pattern) {
            if (preg_match($pattern, $data)) {
                error_log('SLMM Search Replace: Dangerous serialization pattern detected');
                return false;
            }
        }
        
        // Check data length to prevent DoS attacks
        if (strlen($data) > 10000000) { // 10MB limit
            error_log('SLMM Search Replace: Serialized data too large');
            return false;
        }
        
        return true;
    }
    
    /**
     * Legacy safe unserialization for PHP < 7.0
     * Implements BSR-style security without Brumann polyfill
     */
    private function safe_unserialize_legacy($serialized_string) {
        // Pre-process to remove object serializations
        $cleaned_data = preg_replace('/O:\d+:"[^"]*":\d+:\{[^}]*\}/', 'N;', $serialized_string);
        $cleaned_data = preg_replace('/C:\d+:"[^"]*":\d+:\{[^}]*\}/', 'N;', $cleaned_data);
        
        // Attempt unserialization on cleaned data
        $result = @unserialize($cleaned_data);
        
        // Additional validation
        if ($result === false && $serialized_string !== 'b:0;') {
            return false;
        }
        
        return $result;
    }
    
    /**
     * Check if unserialized data contains unsafe objects
     * Additional security layer after unserialization
     */
    private function contains_unsafe_objects($data) {
        if (is_object($data)) {
            return true; // We don't allow any objects
        }
        
        if (is_array($data)) {
            foreach ($data as $value) {
                if ($this->contains_unsafe_objects($value)) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Enhanced recursive search and replace for nested data structures
     * Based on Better Search Replace security standards
     */
    private function recursive_search_replace($data, $search_text, $replace_text, $case_insensitive, $whole_words, $serialised = false) {
        try {
            // BSR-style early exit optimization for strings without matches
            if (is_string($data)) {
                $has_match = $case_insensitive ? 
                    false !== stripos($data, $search_text) : 
                    false !== strpos($data, $search_text);
                    
                if (!$has_match) {
                    return $data; // No matches found, return unchanged
                }
            }

            // Handle serialized strings with enhanced security
            if (is_string($data) && !is_serialized_string($data) && is_serialized($data)) {
                $unserialized = $this->safe_unserialize($data);
                if ($unserialized !== false) {
                    $data = $this->recursive_search_replace($unserialized, $search_text, $replace_text, $case_insensitive, $whole_words, true);
                }
            }
            // Handle arrays with memory optimization
            elseif (is_array($data)) {
                $_tmp = array();
                foreach ($data as $key => $value) {
                    // Process array keys as well (BSR feature)
                    $new_key = $key;
                    if (is_string($key)) {
                        $new_key = $this->recursive_search_replace($key, $search_text, $replace_text, $case_insensitive, $whole_words, false);
                    }
                    
                    $_tmp[$new_key] = $this->recursive_search_replace($value, $search_text, $replace_text, $case_insensitive, $whole_words, false);
                }
                $data = $_tmp;
                unset($_tmp); // Explicit memory cleanup
            }
            // Handle objects with enhanced protected property filtering
            elseif (is_object($data)) {
                // Enhanced object security check
                if (!$this->is_safe_object($data)) {
                    return $data; // Skip unsafe objects
                }
                
                // Clone object to avoid modifying original
                $cloned_data = clone $data;
                
                foreach ($cloned_data as $key => $value) {
                    // Enhanced protected property detection (BSR security feature)
                    if ($this->is_protected_property($key)) {
                        continue;
                    }
                    
                    $cloned_data->$key = $this->recursive_search_replace($value, $search_text, $replace_text, $case_insensitive, $whole_words, false);
                }
                $data = $cloned_data;
            }
            // Handle serialized strings specifically
            elseif (is_serialized_string($data)) {
                $unserialized = $this->safe_unserialize($data);
                if ($unserialized !== false) {
                    $data = $this->recursive_search_replace($unserialized, $search_text, $replace_text, $case_insensitive, $whole_words, true);
                }
            }
            // Handle regular strings with optimized replacement
            else {
                if (is_string($data)) {
                    $data = $this->perform_string_replacement($data, $search_text, $replace_text, $case_insensitive, $whole_words);
                }
            }

            // Re-serialize if this was originally serialized data with validation
            if ($serialised) {
                $serialized_result = serialize($data);
                // Validate serialization didn't fail
                if ($serialized_result === false) {
                    error_log('SLMM Search Replace: Serialization failed after replacement');
                    return $data; // Return unserialized data if serialization fails
                }
                return $serialized_result;
            }

        } catch (Exception $error) {
            // Log error and return original data on failure
            error_log('SLMM Search Replace: Recursive processing failed - ' . $error->getMessage());
            return $data;
        }

        return $data;
    }
    
    /**
     * Check if an object is safe to process
     * Enhanced security check for objects (BSR standard)
     */
    private function is_safe_object($object) {
        if (!is_object($object)) {
            return false;
        }
        
        // Get object class name
        $class_name = get_class($object);
        
        // List of potentially unsafe object types
        $unsafe_classes = array(
            'Closure',
            'ReflectionClass',
            'ReflectionFunction',
            'ReflectionMethod',
            'ReflectionProperty',
            'SplFileObject',
            'DirectoryIterator',
            'FilesystemIterator',
            'GlobIterator',
            'RecursiveDirectoryIterator'
        );
        
        // Check against unsafe classes
        foreach ($unsafe_classes as $unsafe_class) {
            if ($class_name === $unsafe_class || is_subclass_of($object, $unsafe_class)) {
                return false;
            }
        }
        
        // Additional checks for WordPress-specific objects
        if (strpos($class_name, 'WP_') === 0) {
            // Allow most WordPress objects but be cautious with file system related ones
            $wp_unsafe_patterns = array('WP_Filesystem', 'WP_Upgrader');
            foreach ($wp_unsafe_patterns as $pattern) {
                if (strpos($class_name, $pattern) !== false) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * Enhanced protected property detection (BSR security standard)
     * Detects protected and private properties more reliably
     */
    private function is_protected_property($key) {
        if (!is_string($key)) {
            return false;
        }
        
        // BSR-style protected property detection
        // Protected properties are prefixed with null bytes
        if (1 === preg_match("/^(\\\\0).+/im", preg_quote($key))) {
            return true;
        }
        
        // Additional patterns for protected properties
        if (strpos($key, "\0*\0") !== false) { // Protected property marker
            return true;
        }
        
        if (strpos($key, "\0") !== false) { // Any null byte indicates protected/private
            return true;
        }
        
        // WordPress-specific protected property patterns
        $protected_patterns = array(
            '_wp_',
            '__',
            'private_',
            'protected_'
        );
        
        foreach ($protected_patterns as $pattern) {
            if (strpos($key, $pattern) === 0) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Optimized string replacement with enhanced performance
     * Implements BSR-style replacement patterns
     */
    private function perform_string_replacement($data, $search_text, $replace_text, $case_insensitive, $whole_words) {
        if (!is_string($data) || empty($search_text)) {
            return $data;
        }

        // Performance optimization: skip if no potential matches
        $has_match = $case_insensitive ?
            false !== stripos($data, $search_text) :
            false !== strpos($data, $search_text);

        if (!$has_match) {
            return $data;
        }

        // Perform replacement based on options
        if ($whole_words) {
            // Handle multi-word phrases properly for whole word matching
            if (strpos($search_text, ' ') !== false) {
                // Multi-word phrase: replace spaces with flexible whitespace and add boundaries at start/end
                $escaped_text = preg_quote($search_text, '/');
                $escaped_text = preg_replace('/\s+/', '\\s+', $escaped_text);
                $pattern = '/\b' . $escaped_text . '\b/' . ($case_insensitive ? 'i' : '');
                $result = preg_replace($pattern, $replace_text, $data);
            } else {
                // Single word: use regex for space-separated whole word matching
                $pattern = '/(^|\s)' . preg_quote($search_text, '/') . '(\s|$)/' . ($case_insensitive ? 'i' : '');
                $result = preg_replace($pattern, '$1' . $replace_text . '$2', $data);
            }

            // Validate regex replacement succeeded
            if ($result === null) {
                error_log('SLMM Search Replace: Regex replacement failed for whole words');
                return $data;
            }

            return $result;
        } else {
            // Use optimized string replacement
            return $case_insensitive ?
                str_ireplace($search_text, $replace_text, $data) :
                str_replace($search_text, $replace_text, $data);
        }
    }

    /**
     * Count actual string replacements in data (including serialized data)
     * This provides accurate replacement counts matching BSR behavior
     */
    private function count_string_replacements($data, $search_text, $case_insensitive, $whole_words) {
        if (!is_string($data) || empty($data) || empty($search_text)) {
            return 0;
        }
        
        $count = 0;
        
        // Handle serialized data by unserializing and counting recursively
        if (is_serialized($data)) {
            $unserialized = $this->safe_unserialize($data);
            if ($unserialized !== false) {
                $count += $this->count_replacements_recursive($unserialized, $search_text, $case_insensitive, $whole_words);
            }
        } else {
            // Count replacements in regular string
            $count += $this->count_replacements_in_string($data, $search_text, $case_insensitive, $whole_words);
        }
        
        return $count;
    }
    
    /**
     * Count replacements recursively in unserialized data
     */
    private function count_replacements_recursive($data, $search_text, $case_insensitive, $whole_words) {
        $count = 0;
        
        if (is_string($data)) {
            $count += $this->count_replacements_in_string($data, $search_text, $case_insensitive, $whole_words);
        } elseif (is_array($data)) {
            foreach ($data as $value) {
                $count += $this->count_replacements_recursive($value, $search_text, $case_insensitive, $whole_words);
            }
        } elseif (is_object($data)) {
            foreach ($data as $key => $value) {
                // Skip protected/private properties
                if (is_string($key) && 1 === preg_match("/^(\\\0).+/im", preg_quote($key))) {
                    continue;
                }
                $count += $this->count_replacements_recursive($value, $search_text, $case_insensitive, $whole_words);
            }
        }
        
        return $count;
    }
    
    /**
     * Count replacements in a regular string
     */
    private function count_replacements_in_string($string, $search_text, $case_insensitive, $whole_words) {
        if (empty($string) || empty($search_text)) {
            return 0;
        }
        
        if ($whole_words) {
            // Handle multi-word phrases properly for whole word counting
            if (strpos($search_text, ' ') !== false) {
                // Multi-word phrase: replace spaces with flexible whitespace and add boundaries at start/end
                $escaped_text = preg_quote($search_text, '/');
                $escaped_text = preg_replace('/\s+/', '\\s+', $escaped_text);
                $pattern = '/\b' . $escaped_text . '\b/' . ($case_insensitive ? 'i' : '');
                return preg_match_all($pattern, $string);
            } else {
                // Single word: use regex for space-separated whole word matching
                $pattern = '/(^|\s)' . preg_quote($search_text, '/') . '(\s|$)/' . ($case_insensitive ? 'i' : '');
                return preg_match_all($pattern, $string);
            }
        } else {
            if ($case_insensitive) {
                return substr_count(strtolower($string), strtolower($search_text));
            } else {
                return substr_count($string, $search_text);
            }
        }
    }
    
    /**
     * Generate a preview of changes for detailed reporting
     */
    private function generate_change_preview($original, $updated, $search_text, $replace_text = '', $whole_words = false, $case_insensitive = false) {
        $preview = array(
            'original_length' => strlen($original),
            'updated_length' => strlen($updated),
            'search_term' => $search_text
        );

        // Find the first occurrence based on whole words setting
        $pos = false;
        if ($whole_words) {
            // Use regex pattern for whole word matching
            $pattern = '/(^|\s)' . preg_quote($search_text, '/') . '(\s|$)/' . ($case_insensitive ? 'i' : '');
            if (preg_match($pattern, $original, $matches, PREG_OFFSET_CAPTURE)) {
                // Position is where the actual word starts (after any leading space)
                $pos = $matches[0][1];
                if (isset($matches[1][0]) && $matches[1][0] === ' ') {
                    $pos += 1; // Skip the leading space
                }
            }
        } else {
            // Find the first occurrence of the search text (try both case sensitive and insensitive based on setting)
            if ($case_insensitive) {
                $pos = stripos($original, $search_text);
            } else {
                $pos = strpos($original, $search_text);
            }
        }

        if ($pos !== false) {
            // Show 200 characters before and after the match
            $context_chars = 200;
            $search_length = strlen($search_text);

            // Calculate context boundaries for original text
            $start = max(0, $pos - $context_chars);
            $end = min(strlen($original), $pos + $search_length + $context_chars);

            // Extract before, match, and after segments for original
            $before_text = substr($original, $start, $pos - $start);
            $match_text = substr($original, $pos, $search_length);
            $after_text = substr($original, $pos + $search_length, $end - ($pos + $search_length));

            $preview['before_text'] = $before_text;
            $preview['match_text'] = $match_text;
            $preview['after_text'] = $after_text;

            // Use the actual replacement text if provided, otherwise try to detect it
            $replacement_text = !empty($replace_text) ? $replace_text : $match_text;

            $preview['before_text_updated'] = $before_text; // Same before text
            $preview['replacement_text'] = $replacement_text;
            $preview['after_text_updated'] = $after_text; // Same after text

            // Full context strings for backward compatibility
            $preview['context_original'] = $before_text . $match_text . $after_text;
            $preview['context_updated'] = $before_text . $replacement_text . $after_text;
        }

        return $preview;
    }

    /**
     * Enhanced input sanitization for search/replace text
     */
    /**
     * Enhanced input sanitization with TEXT-ONLY processing and comprehensive security validation
     *
     * @param string $input Raw input to sanitize
     * @return string Sanitized input (TEXT ONLY - no code execution possible)
     * @throws Exception If input contains dangerous patterns or exceeds limits
     */
    private function sanitize_search_input($input) {
        // Basic sanitization - remove null bytes and trim
        $input = str_replace("\0", "", $input);
        $input = trim($input);

        // STRICT LENGTH VALIDATION - 500 character maximum
        if (strlen($input) > 500) {
            throw new Exception('Search/replace input too long (maximum 500 characters)');
        }

        // COMPREHENSIVE DANGEROUS PATTERN DETECTION - block ANY potentially dangerous content
        $dangerous_patterns = [
            '/(?:union\s+select|select\s+.*\s+from)/i',          // SQL injection
            '/(?:drop\s+table|delete\s+from|truncate)/i',        // Destructive SQL
            '/(?:exec\s*\(|execute\s*\(|eval\s*\()/i',          // Code execution
            '/(?:<script|javascript:|vbscript:|onload=)/i',      // XSS attempts
            '/(?:\$\{|<%|%>|<\?php|\?>)/i',                     // Server-side code
            '/(?:file_get_contents|readfile|include|require)/i', // File inclusion
            '/(?:<iframe|<object|<embed|<applet)/i',             // Dangerous HTML elements
            '/(?:expression\s*\(|@import|behavior:)/i',          // CSS injection
            '/(?:data:.*base64|data:.*javascript)/i',            // Data URI exploits
        ];

        foreach ($dangerous_patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                // Log security attempt with details
                error_log("SLMM Security Alert: Blocked dangerous pattern in search/replace input from user " . get_current_user_id() . " - IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
                throw new Exception('Input contains potentially dangerous content and has been blocked for security');
            }
        }

        // BINARY DATA DETECTION - ensure valid UTF-8 text only
        if (!mb_check_encoding($input, 'UTF-8')) {
            throw new Exception('Input must be valid UTF-8 text only');
        }

        // COMPREHENSIVE TEXT-ONLY SANITIZATION - strip ALL HTML/XML tags and entities
        $input = wp_strip_all_tags($input, true); // Remove all HTML tags
        $input = html_entity_decode($input, ENT_QUOTES | ENT_HTML5, 'UTF-8'); // Decode entities to prevent bypass
        $input = wp_strip_all_tags($input, true); // Second pass after entity decode

        // CONTROL CHARACTER FILTERING - remove all control chars except basic whitespace
        $input = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $input);

        // FINAL SANITIZATION - ensure it's treated as pure text
        $input = sanitize_textarea_field($input); // WordPress text sanitization
        $input = stripslashes($input); // Remove any escaping

        return $input;
    }

    /**
     * Sanitize table names to prevent SQL injection
     */
    private function sanitize_table_name($table_name) {
        // Remove any characters that aren't alphanumeric, underscore, or hyphen
        $table_name = preg_replace('/[^a-zA-Z0-9_-]/', '', $table_name);
        
        // Additional sanitization
        $table_name = sanitize_text_field($table_name);
        
        return $table_name;
    }

    /**
     * Enhanced WordPress core data protection (BSR security standard)
     * Comprehensive protection for critical WordPress options and data
     */
    private function should_skip_core_option($option_name, $table_name) {
        global $wpdb;
        
        // Skip our own plugin's data to prevent corruption
        $plugin_options = array(
            'chatgpt_generator_options',
            'slmm_gpt_prompts',
            'slmm_search_replace_results'
        );
        
        if (in_array($option_name, $plugin_options)) {
            return true;
        }
        
        // Comprehensive WordPress core options protection (BSR standard)
        $critical_options = array(
            // Core WordPress settings
            'active_plugins',
            'current_theme',
            'stylesheet',
            'template',
            'admin_email',
            'users_can_register',
            'default_role',
            'wp_user_roles',
            'cron',
            'rewrite_rules',
            
            // Database and installation critical settings
            'db_version',
            'db_upgraded',
            'secret_key',
            'auth_key',
            'secure_auth_key',
            'logged_in_key',
            'nonce_key',
            'auth_salt',
            'secure_auth_salt',
            'logged_in_salt',
            'nonce_salt',
            'recovery_keys',
            
            // Security-related options
            'auto_core_update_notified',
            'auto_updater.lock',
            'core_updater.lock',
            'plugin_updater.lock',
            'theme_updater.lock',
            'recovery_mode_email_last_sent',
            'wp_force_ssl_admin',
            
            // Multisite network settings
            'ms_files_rewriting',
            'WPLANG',
            'new_admin_email',
            'upload_path',
            'upload_url_path',
            
            // Theme and customization
            'theme_switched',
            'sidebars_widgets',
            'widget_*',
            'customize_*',
            
            // Plugin management
            'recently_activated',
            'uninstall_plugins',
            'auto_plugin_theme_update_emails',
            
            // Caching and optimization
            'can_compress_scripts',
            'advanced_edit',
            
            // WordPress.com specific (if applicable)
            'jetpack_*',
            'vaultpress_*',
            'akismet_*'
        );
        
        // Pattern-based protection for options
        $critical_patterns = array(
            '_transient_',           // WordPress transients
            '_site_transient_',      // Site transients
            'cron',                  // Cron-related options
            'user_roles',            // User role definitions
            '_user_roles',           // Legacy user roles
            'widget_',               // Widget configurations
            'theme_mods_',           // Theme modifications
            'customize_',            // Customizer settings
            '_backup_',              // Backup-related options
            'recovery_',             // Recovery mode options
            'auto_updater',          // Auto-update related
            'upgrade_',              // Upgrade process options
            'db_upgraded',           // Database upgrade flags
        );
        
        // Only apply to wp_options table
        if ($table_name === $wpdb->options) {
            // Check exact matches
            if (in_array($option_name, $critical_options)) {
                return true;
            }
            
            // Check pattern matches
            foreach ($critical_patterns as $pattern) {
                if (strpos($option_name, $pattern) !== false) {
                    return true;
                }
            }
            
            // BSR-specific options protection
            if (strpos($option_name, '_transient_bsr_') !== false ||
                strpos($option_name, 'bsr_') !== false ||
                strpos($option_name, 'better_search_replace') !== false) {
                return true;
            }
        }
        
        // Additional table-specific protections
        return $this->should_skip_table_specific_data($option_name, $table_name);
    }
    
    /**
     * Table-specific data protection beyond wp_options
     * Protects critical data in other WordPress tables
     */
    private function should_skip_table_specific_data($identifier, $table_name) {
        global $wpdb;
        
        // Protect critical user data
        if ($table_name === $wpdb->users) {
            // Never modify user login, password, or activation keys
            $user_critical_fields = array('user_login', 'user_pass', 'user_activation_key', 'user_status');
            if (in_array($identifier, $user_critical_fields)) {
                return true;
            }
        }
        
        // Protect critical user meta
        if ($table_name === $wpdb->usermeta) {
            $critical_user_meta = array(
                'wp_capabilities',
                'wp_user_level',
                'session_tokens',
                'password_reset_key',
                'default_password_nag',
                'use_ssl',
                'show_admin_bar_front'
            );
            if (in_array($identifier, $critical_user_meta)) {
                return true;
            }
        }
        
        // Protect critical post data
        if ($table_name === $wpdb->posts) {
            // Skip system posts and critical post types
            if (isset($identifier['post_type'])) {
                $critical_post_types = array('nav_menu_item', 'revision', 'attachment', 'customize_changeset');
                if (in_array($identifier['post_type'], $critical_post_types)) {
                    return true;
                }
            }
        }
        
        // Protect critical comment meta
        if ($table_name === $wpdb->commentmeta) {
            $critical_comment_meta = array('akismet_*', 'wp_*');
            foreach ($critical_comment_meta as $pattern) {
                if (strpos($identifier, str_replace('*', '', $pattern)) !== false) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Build table-specific query with filtering to exclude unwanted content
     * Addresses the issue where revisions and other unwanted post types are included
     */
    private function build_filtered_query($table_name, $batch_size, $offset) {
        global $wpdb;
        
        $table_name_escaped = esc_sql($table_name);
        
        // Default query for most tables
        $base_query = "SELECT * FROM `{$table_name_escaped}`";
        
        // Add table-specific filtering
        $where_clauses = $this->get_table_where_clauses($table_name);
        
        if (!empty($where_clauses)) {
            $base_query .= " WHERE " . implode(" AND ", $where_clauses);
        }
        
        // Add pagination - ensure values are integers
        $batch_size = intval($batch_size);
        $offset = intval($offset);
        $base_query .= " LIMIT {$batch_size} OFFSET {$offset}";
        
        return $base_query;
    }
    
    /**
     * Get WHERE clauses for specific tables to filter out unwanted content
     * Primary focus: exclude WordPress revisions and other unwanted post types
     */
    private function get_table_where_clauses($table_name) {
        global $wpdb;
        
        $where_clauses = array();
        
        // WordPress Posts table filtering - exclude revisions and unwanted post types
        if ($table_name === $wpdb->posts) {
            $where_clauses[] = "post_type NOT IN ('revision', 'nav_menu_item', 'customize_changeset', 'oembed_cache', 'user_request', 'wp_block', 'wp_template', 'wp_template_part', 'wp_global_styles', 'wp_navigation')";
            $where_clauses[] = "post_status NOT IN ('inherit', 'auto-draft')";
            // Include published, draft, private, pending, future posts
            $where_clauses[] = "post_status IN ('publish', 'draft', 'private', 'pending', 'future')";
        }
        
        // WordPress Post Meta table filtering - exclude meta for revisions and unwanted posts
        elseif ($table_name === $wpdb->postmeta) {
            $where_clauses[] = "post_id IN (
                SELECT ID FROM {$wpdb->posts} 
                WHERE post_type NOT IN ('revision', 'nav_menu_item', 'customize_changeset', 'oembed_cache', 'user_request', 'wp_block', 'wp_template', 'wp_template_part', 'wp_global_styles', 'wp_navigation')
                AND post_status NOT IN ('inherit', 'auto-draft')
                AND post_status IN ('publish', 'draft', 'private', 'pending', 'future')
            )";
        }
        
        // WordPress Term Relationships - exclude relationships for revisions
        elseif ($table_name === $wpdb->term_relationships) {
            $where_clauses[] = "object_id IN (
                SELECT ID FROM {$wpdb->posts} 
                WHERE post_type NOT IN ('revision', 'nav_menu_item', 'customize_changeset', 'oembed_cache', 'user_request', 'wp_block', 'wp_template', 'wp_template_part', 'wp_global_styles', 'wp_navigation')
                AND post_status NOT IN ('inherit', 'auto-draft')
                AND post_status IN ('publish', 'draft', 'private', 'pending', 'future')
            )";
        }
        
        // WordPress Comments - exclude comments on revisions and unwanted posts
        elseif ($table_name === $wpdb->comments) {
            $where_clauses[] = "comment_post_ID IN (
                SELECT ID FROM {$wpdb->posts} 
                WHERE post_type NOT IN ('revision', 'nav_menu_item', 'customize_changeset', 'oembed_cache', 'user_request', 'wp_block', 'wp_template', 'wp_template_part', 'wp_global_styles', 'wp_navigation')
                AND post_status NOT IN ('inherit', 'auto-draft')
                AND post_status IN ('publish', 'draft', 'private', 'pending', 'future')
            ) OR comment_post_ID = 0";
        }
        
        // WordPress Comment Meta - exclude meta for comments on revisions
        elseif ($table_name === $wpdb->commentmeta) {
            $where_clauses[] = "comment_id IN (
                SELECT comment_ID FROM {$wpdb->comments} 
                WHERE comment_post_ID IN (
                    SELECT ID FROM {$wpdb->posts} 
                    WHERE post_type NOT IN ('revision', 'nav_menu_item', 'customize_changeset', 'oembed_cache', 'user_request', 'wp_block', 'wp_template', 'wp_template_part', 'wp_global_styles', 'wp_navigation')
                    AND post_status NOT IN ('inherit', 'auto-draft')
                    AND post_status IN ('publish', 'draft', 'private', 'pending', 'future')
                ) OR comment_post_ID = 0
            )";
        }
        
        return $where_clauses;
    }
    
    /**
     * Row-level fallback protection for wp_posts table
     * Skip revisions and unwanted post types that might slip through SQL filtering
     */
    private function should_skip_post_row($row) {
        // Check if this is a revision or unwanted post type
        $unwanted_post_types = array(
            'revision',
            'nav_menu_item', 
            'customize_changeset',
            'oembed_cache',
            'user_request',
            'wp_block',
            'wp_template',
            'wp_template_part',
            'wp_global_styles',
            'wp_navigation'
        );
        
        // Skip if post_type is unwanted
        if (isset($row['post_type']) && in_array($row['post_type'], $unwanted_post_types)) {
            return true;
        }
        
        // Skip if post_status indicates revision or auto-draft
        $unwanted_statuses = array('inherit', 'auto-draft');
        if (isset($row['post_status']) && in_array($row['post_status'], $unwanted_statuses)) {
            return true;
        }
        
        // Additional check: if post_type is 'revision' and post_status is 'inherit'
        if (isset($row['post_type']) && isset($row['post_status'])) {
            if ($row['post_type'] === 'revision' && $row['post_status'] === 'inherit') {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Darken a hex color by a given percentage
     * @param string $hex The hex color (e.g., #7a39e8)
     * @param int $percent The percentage to darken (0-100)
     * @return string The darkened hex color
     */
    private function darken_color($hex, $percent) {
        // Remove # if present
        $hex = str_replace('#', '', $hex);
        
        // Convert hex to RGB
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        // Calculate darker values
        $factor = (100 - $percent) / 100;
        $r = floor($r * $factor);
        $g = floor($g * $factor);
        $b = floor($b * $factor);
        
        // Ensure values are within bounds
        $r = max(0, min(255, $r));
        $g = max(0, min(255, $g));
        $b = max(0, min(255, $b));
        
        // Convert back to hex
        return sprintf('#%02x%02x%02x', $r, $g, $b);
    }
    
    /**
     * Lighten a hex color by a given percentage
     * @param string $hex The hex color (e.g., #7a39e8)
     * @param int $percent The percentage to lighten (0-100)
     * @return string The lightened hex color
     */
    private function lighten_color($hex, $percent) {
        // Remove # if present
        $hex = str_replace('#', '', $hex);
        
        // Convert hex to RGB
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        // Calculate lighter values
        $factor = $percent / 100;
        $r = floor($r + (255 - $r) * $factor);
        $g = floor($g + (255 - $g) * $factor);
        $b = floor($b + (255 - $b) * $factor);
        
        // Ensure values are within bounds
        $r = max(0, min(255, $r));
        $g = max(0, min(255, $g));
        $b = max(0, min(255, $b));
        
        // Convert back to hex
        return sprintf('#%02x%02x%02x', $r, $g, $b);
    }
    
    /**
     * Calculate the luminance of a hex color to determine if it's light or dark
     * @param string $hex The hex color (e.g., #7a39e8)
     * @return float The luminance value (0-1, where 1 is white)
     */
    private function get_color_luminance($hex) {
        // Remove # if present
        $hex = str_replace('#', '', $hex);
        
        // Convert hex to RGB
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        // Convert to 0-1 range
        $r = $r / 255;
        $g = $g / 255;
        $b = $b / 255;
        
        // Apply gamma correction
        $r = ($r <= 0.03928) ? $r / 12.92 : pow(($r + 0.055) / 1.055, 2.4);
        $g = ($g <= 0.03928) ? $g / 12.92 : pow(($g + 0.055) / 1.055, 2.4);
        $b = ($b <= 0.03928) ? $b / 12.92 : pow(($b + 0.055) / 1.055, 2.4);
        
        // Calculate luminance using WCAG formula
        return 0.2126 * $r + 0.7152 * $g + 0.0722 * $b;
    }
    
    /**
     * Determine if a color is light based on its luminance
     * @param string $hex The hex color
     * @return bool True if the color is light, false if dark
     */
    private function is_light_color($hex) {
        $luminance = $this->get_color_luminance($hex);
        // Threshold of 0.5 - colors above this are considered light
        return $luminance > 0.5;
    }

    /**
     * Add Development Mode styling to admin bar
     */
    public function add_development_mode_styling() {
        $options = get_option('chatgpt_generator_options', array());
        
        // Check if development mode is enabled
        if (empty($options['enable_development_mode'])) {
            return;
        }
        
        // Get the color (with fallback)
        $color = isset($options['development_mode_color']) ? $options['development_mode_color'] : '#7a39e8';
        $color = sanitize_hex_color($color) ?: '#7a39e8';
        
        // Determine if the color is light or dark
        $is_light = $this->is_light_color($color);
        $text_color = $is_light ? '#000000' : '#ffffff';
        $text_color_hover = $is_light ? '#333333' : '#ffffff';
        
        // Generate darker or lighter color for gradients and hover states
        if ($is_light) {
            $accent_color = $this->darken_color($color, 20);
        } else {
            $accent_color = $this->darken_color($color, 15);
        }
        
        // Create inline CSS for admin bar styling
        $css = "
        #wpadminbar {
            background: {$color} !important;
            background-image: linear-gradient(135deg, {$color} 0%, {$accent_color} 100%) !important;
        }
        #wpadminbar .ab-item, 
        #wpadminbar a.ab-item, 
        #wpadminbar .ab-item:before,
        #wpadminbar .ab-item:after,
        #wpadminbar .ab-label,
        #wpadminbar .screen-reader-text,
        #wpadminbar .display-name {
            color: {$text_color} !important;
        }
        #wpadminbar .ab-top-menu > li:hover > .ab-item,
        #wpadminbar .ab-top-menu > li.hover > .ab-item,
        #wpadminbar .ab-top-menu > li:focus > .ab-item {
            background: {$accent_color} !important;
            color: {$text_color_hover} !important;
        }
        #wpadminbar .ab-top-secondary .ab-top-menu > li:hover > .ab-item,
        #wpadminbar .ab-top-secondary .ab-top-menu > li.hover > .ab-item,
        #wpadminbar .ab-top-secondary .ab-top-menu > li:focus > .ab-item {
            background: {$accent_color} !important;
            color: {$text_color_hover} !important;
        }
        #wpadminbar .ab-submenu {
            background: {$color} !important;
            border-color: {$accent_color} !important;
        }
        #wpadminbar .ab-submenu .ab-item {
            color: {$text_color} !important;
        }
        #wpadminbar .ab-submenu .ab-item:hover,
        #wpadminbar .ab-submenu .ab-item:focus {
            background: {$accent_color} !important;
            color: {$text_color_hover} !important;
        }
        /* Specific styling for notification counters and badges */
        #wpadminbar .ab-label,
        #wpadminbar .count,
        #wpadminbar .pending-count,
        #wpadminbar .update-plugins {
            color: {$text_color} !important;
        }
        /* Avatar and user info styling */
        #wpadminbar .avatar {
            border-color: {$text_color} !important;
        }
        /* Icon styling */
        #wpadminbar .ab-icon:before,
        #wpadminbar .ab-icon:after {
            color: {$text_color} !important;
        }
        ";
        
        // Add the inline CSS
        wp_add_inline_style('admin-bar', $css);
        
        // If admin-bar style is not enqueued, enqueue it
        if (!wp_style_is('admin-bar', 'enqueued')) {
            wp_enqueue_style('admin-bar');
        }
    }

    /**
     * Render AI interlinking prompt box
     */
    private function render_interlinking_prompt_box($type, $prompt_data, $openrouter) {
        $type_labels = array(
            'parent' => 'Parent Links',
            'child' => 'Child Links', 
            'sibling' => 'Sibling Links',
            'semantic' => 'Semantic Links'
        );
        
        $current_provider = $prompt_data['provider'] ?? 'openai';
        
        // Get models based on provider
        if ($current_provider === 'openrouter' && $openrouter->is_configured()) {
            $models = $openrouter->get_models();
            $default_model = 'google/gemini-2.0-flash-exp:free';
        } else {
            $models = $this->get_openai_models();
            $default_model = 'gpt-4o';
        }
        
        $current_model = $prompt_data['model'] ?? $default_model;
        ?>
        <div class="slmm-interlinking-prompt" data-type="<?php echo esc_attr($type); ?>">
            <div class="slmm-gpt-prompt-header">
                <input type="text" 
                       name="slmm_interlinking_prompts[<?php echo esc_attr($type); ?>][title]" 
                       value="<?php echo esc_attr($type_labels[$type]); ?>" 
                       readonly
                       class="slmm-gpt-prompt-title">
                
                <div class="slmm-model-selector">
                    <div>
                        <select name="slmm_interlinking_prompts[<?php echo esc_attr($type); ?>][provider]" 
                                class="slmm-interlinking-provider" 
                                data-type="<?php echo esc_attr($type); ?>">
                            <option value="openai" <?php selected($current_provider, 'openai'); ?>>OpenAI</option>
                            <?php if ($openrouter->is_configured()) : ?>
                                <option value="openrouter" <?php selected($current_provider, 'openrouter'); ?>>OpenRouter</option>
                            <?php endif; ?>
                        </select>
                        
                        <select name="slmm_interlinking_prompts[<?php echo esc_attr($type); ?>][model]" 
                                class="slmm-gpt-prompt-model" 
                                data-type="<?php echo esc_attr($type); ?>">
                            <?php foreach ($models as $model_id => $model_name) : ?>
                                <option value="<?php echo esc_attr($model_id); ?>" <?php selected($current_model, $model_id); ?>>
                                    <?php echo esc_html($model_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <button type="button" 
                                class="slmm-refresh-interlinking-models" 
                                data-type="<?php echo esc_attr($type); ?>" 
                                title="Refresh Models">↻</button>
                    </div>
                    
                    <?php if (!$openrouter->is_configured()) : ?>
                        <div>
                            <em>Note: Enter OpenRouter API key in main settings to enable OpenRouter models.</em>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <textarea name="slmm_interlinking_prompts[<?php echo esc_attr($type); ?>][prompt]" 
                      placeholder="Enter your AI interlinking prompt here..." 
                      class="slmm-gpt-prompt-content"><?php echo esc_textarea($prompt_data['prompt'] ?? ''); ?></textarea>
            
            <div class="slmm-gpt-prompt-footer">
                <label>Temperature: 
                    <input type="number" 
                           step="0.1" 
                           min="0" 
                           max="1" 
                           name="slmm_interlinking_prompts[<?php echo esc_attr($type); ?>][temperature]" 
                           value="<?php echo esc_attr($prompt_data['temperature'] ?? '0.7'); ?>" 
                           class="slmm-gpt-prompt-temperature">
                </label>
                <label>Max Tokens: 
                    <input type="number" 
                           step="1" 
                           min="1" 
                           max="4096" 
                           name="slmm_interlinking_prompts[<?php echo esc_attr($type); ?>][max_tokens]" 
                           value="<?php echo esc_attr($prompt_data['max_tokens'] ?? '1000'); ?>" 
                           class="slmm-gpt-prompt-max-tokens">
                </label>
            </div>
        </div>
        <?php
    }

    /**
     * Get default interlinking prompts
     */
    private function get_default_interlinking_prompts() {
        return array(
            'parent' => array(
                'title' => 'Parent Links',
                'provider' => 'openai',
                'model' => 'gpt-4o',
                'temperature' => '0.7',
                'max_tokens' => '4096',
                'prompt' => 'Analyze the following content and create 3 variations of anchor text for linking FROM child pages TO parent pages (upward hierarchy linking for SEO authority flow). Focus on semantic relevance and natural integration.

Content to analyze: {content}
Target parent page: {target_page}
Page hierarchy: {page_hierarchy}
Linking rules: {linking_rules}

Create 3 anchor text variations:
1. Direct Entity: Exact keyword/entity match
2. Descriptive Phrase: Natural explanatory context 
3. LSI-Rich: Semantic variants with related keywords

Format as JSON with surrounding context for each suggestion.'
            ),
            'child' => array(
                'title' => 'Child Links',
                'provider' => 'openai',
                'model' => 'gpt-4o',
                'temperature' => '0.7',
                'max_tokens' => '4096',
                'prompt' => 'Analyze the following content and create 3 variations of anchor text for linking FROM parent pages TO child pages (downward hierarchy linking for topic exploration). Focus on topic expansion and user journey.

Content to analyze: {content}
Target child page: {target_page}
Page hierarchy: {page_hierarchy}
Linking rules: {linking_rules}

Create 3 anchor text variations:
1. Direct Entity: Specific topic/keyword match
2. Descriptive Phrase: Exploratory context phrase
3. LSI-Rich: Topic-related semantic variants

Format as JSON with surrounding context for each suggestion.'
            ),
            'sibling' => array(
                'title' => 'Sibling Links',
                'provider' => 'openai',
                'model' => 'gpt-4o',
                'temperature' => '0.7',
                'max_tokens' => '4096',
                'prompt' => 'Analyze the following content and create 3 variations of anchor text for linking BETWEEN sibling pages (same hierarchy level for contextual relevance). Focus on related topics and cross-references.

Content to analyze: {content}
Target sibling page: {target_page}
Page hierarchy: {page_hierarchy}
Linking rules: {linking_rules}

Create 3 anchor text variations:
1. Direct Entity: Related topic match
2. Descriptive Phrase: Contextual bridge phrase
3. LSI-Rich: Cross-topic semantic variants

Format as JSON with surrounding context for each suggestion.'
            ),
            'semantic' => array(
                'title' => 'Semantic Links',
                'provider' => 'openai',
                'model' => 'gpt-4o',
                'temperature' => '0.7',
                'max_tokens' => '4096',
                'prompt' => 'Analyze the following content and create 3 variations of anchor text for semantic/contextual linking (topic-based relevance regardless of hierarchy). Focus on natural language and user intent.

Content to analyze: {content}
Target page: {target_page}
Page hierarchy: {page_hierarchy}
Linking rules: {linking_rules}

Create 3 anchor text variations:
1. Direct Entity: Core concept match
2. Descriptive Phrase: Natural context integration
3. LSI-Rich: Conceptual semantic variants

Format as JSON with surrounding context for each suggestion.'
            )
        );
    }

    /**
     * Get default linking rules
     */
    private function get_default_linking_rules() {
        return 'HIERARCHICAL LINKING RULES:

HIERARCHY STRUCTURE:
- Mother Pages (top level) → Son Pages (second level) → Grandson Pages (third level)

LINK POWER SYSTEM:
- YELLOW (Maximum Power): Child-to-parent links (Grandson → Son → Mother)
  * Placed at beginning for maximum SEO impact
  * First 15% of content preferred
- RED (Medium Power): Parent-to-child links (Mother → Son → Grandson)  
  * Used for semantic connection and topic expansion
  * At least 70% down in content
- BLACK (Neutral): Contextual/sibling links between same-level pages
  * Natural contextual placement throughout content

PLACEMENT RULES:
- Only ONE link per paragraph (between headings)
- Links must integrate naturally within existing content
- NOT in first sentences - avoid appearing promotional
- May add maximum one sentence to accommodate anchor text
- Maintain natural reading flow

CONTENT SECTION MAPPING:
- Important Pages: Scan TOP section only (<!-- SLMM_SEGMENT_TOP_START --> to <!-- SLMM_SEGMENT_TOP_END -->)
- Mother Pages: TOP and MIDDLE sections preferred
- Son Pages: All sections available  
- Grandson Pages: MIDDLE and BOTTOM sections preferred

SEO GUIDELINES:
- Maintain semantic relevance between anchor text and target page
- Use entity-based linking for direct topics
- Include LSI (Latent Semantic Indexing) variants
- Avoid over-optimization - natural language priority
- Support topic clusters and content silos';
    }

    /**
     * Render page summarization settings section
     */
    private function render_page_summarization_settings() {
        $settings = get_option('slmm_page_summarization_settings', $this->get_default_page_summarization_settings());
        
        // Include OpenRouter integration for model selection
        require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
        $openrouter = new SLMM_OpenRouter_Integration();
        
        $current_provider = $settings['provider'];
        
        // Get models based on provider - EXACTLY like interlinking prompts
        if ($current_provider === 'openrouter' && $openrouter->is_configured()) {
            $models = $openrouter->get_models();
            $default_model = 'google/gemini-2.0-flash-exp:free';
        } else {
            $models = $this->get_openai_models();
            $default_model = 'gpt-4o';
        }
        
        $current_model = $settings['model'] ?? $default_model;
        ?>
        <div class="slmm-interlinking-prompt" data-type="summarization">
            <div class="slmm-gpt-prompt-header">
                <input type="text" 
                       name="slmm_page_summarization_settings[title]" 
                       value="Page Summarization" 
                       readonly
                       class="slmm-gpt-prompt-title">
                
                <div class="slmm-model-selector">
                    <div>
                        <select name="slmm_page_summarization_settings[provider]" 
                                class="slmm-interlinking-provider" 
                                data-type="summarization">
                            <option value="openai" <?php selected($current_provider, 'openai'); ?>>OpenAI</option>
                            <?php if ($openrouter->is_configured()) : ?>
                                <option value="openrouter" <?php selected($current_provider, 'openrouter'); ?>>OpenRouter</option>
                            <?php endif; ?>
                        </select>
                        
                        <select name="slmm_page_summarization_settings[model]" 
                                class="slmm-gpt-prompt-model" 
                                data-type="summarization">
                            <?php foreach ($models as $model_id => $model_name) : ?>
                                <option value="<?php echo esc_attr($model_id); ?>" <?php selected($current_model, $model_id); ?>>
                                    <?php echo esc_html($model_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <button type="button" 
                                class="slmm-refresh-interlinking-models" 
                                data-type="summarization" 
                                title="Refresh Models">↻</button>
                    </div>
                    
                    <?php if (!$openrouter->is_configured()) : ?>
                        <div>
                            <em>Note: Enter OpenRouter API key in main settings to enable OpenRouter models.</em>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <textarea name="slmm_page_summarization_settings[prompt]" 
                      placeholder="Enter your page summarization prompt here..." 
                      class="slmm-gpt-prompt-content"><?php echo esc_textarea($settings['prompt']); ?></textarea>
            
            <div class="slmm-gpt-prompt-footer">
                <label>Temperature: 
                    <input type="number" 
                           step="0.1" 
                           min="0" 
                           max="1" 
                           name="slmm_page_summarization_settings[temperature]" 
                           value="<?php echo esc_attr($settings['temperature']); ?>" 
                           class="slmm-gpt-prompt-temperature">
                </label>
                <label>Max Tokens: 
                    <input type="number" 
                           step="1" 
                           min="1" 
                           max="4096" 
                           name="slmm_page_summarization_settings[max_tokens]" 
                           value="<?php echo esc_attr($settings['max_tokens']); ?>" 
                           class="slmm-gpt-prompt-max-tokens">
                </label>
            </div>
        </div>
        <?php
    }

    /**
     * Get default page summarization settings
     */
    private function get_default_page_summarization_settings() {
        return array(
            'title' => 'Page Summarization',
            'provider' => 'openai',
            'model' => 'gpt-4o',
            'temperature' => '0.7',
            'max_tokens' => '4096',
            'prompt' => 'What is this page about? Write a 100 word TLDR summary: {content}'
        );
    }

    /**
     * Get Anthropic models for selection
     */
    private function get_anthropic_models() {
        return array(
            'claude-3-haiku-20240307' => 'Claude 3 Haiku',
            'claude-3-sonnet-20240229' => 'Claude 3 Sonnet',
            'claude-3-opus-20240229' => 'Claude 3 Opus'
        );
    }
}

// Initialize the class
$slmm_general_settings = new SLMM_General_Settings();
$slmm_general_settings->init();

// Add buttons to the edit screen
add_action('edit_form_after_title', array($slmm_general_settings, 'chatgpt_generator_buttons'));
add_action('edit_form_after_editor', array($slmm_general_settings, 'chatgpt_generator_buttons'));