---
task: h-fix-interlinking-large-site-display
branch: fix/interlinking-large-site-display
status: in-progress
created: 2025-01-18
modules: [interlinking, ui, visualization]
---

# Fix Interlinking Visualization Issues for Large Sites

## Problem/Goal
The interlinking visualization interface has critical display and functionality issues when dealing with large sites (200+ posts). Posts are displayed too wide, CPT filtering is not working correctly (mixing regular pages with custom post types), and titles are overflowing without proper truncation.

## Success Criteria
- [ ] Posts display in a grid layout (max 40 units wide) with automatic column/row wrapping for better visibility on large sites
- [ ] CPT filtering is bulletproof - when a CPT is selected, ONLY posts from that CPT are shown (no mixing with regular pages)
- [ ] Strict CPT detection that handles CPTs using "page" capability type correctly
- [ ] Node titles properly truncated with ellipses to prevent overflow in slmm-node-title elements
- [ ] Interface remains performant with 200+ posts displayed

## Context Manifest

### How the Interlinking Visualization Currently Works: D3 Tree Layout System

The interlinking visualization uses a sophisticated D3.js tree layout system centered around a single large file containing the complete visualization logic. The system operates through several key components working together:

**Entry Point and Initialization:**
When users access the interlinking suite, the system initializes through `SLMM_Interlinking_Suite` class in `includes/interlinking/interlinking-suite.php`. This 20,000+ line file contains the entire visualization system (note: this is the only file exempt from the 800-line rule). The initialization creates tabs for different post types by querying `get_post_types(array('public' => true, 'show_ui' => true), 'objects')` and filtering out excluded types via `get_excluded_post_types()`. Each tab represents a different content type (Pages, Posts, Custom Post Types) and triggers separate data loading when clicked.

**CPT Filtering and Data Loading:**
The post type filtering happens in the `ajax_generate_silo_grid()` method which receives a `post_type_filter` parameter from JavaScript. The filtering logic branches into two paths:
- For 'page' post type: Uses `get_pages()` with specific parameters
- For other post types: Uses `get_posts()` with `post_type` parameter set to the specific CPT

However, the current filtering has a critical flaw in the `analyze_wordpress_hierarchy()` method around line 13996. When `$post_type_filter === 'page'`, it loads ALL pages, but when filtering other CPTs, the system doesn't properly exclude pages that might have been registered with 'page' capability type. This is why Glossaries (which use 'page' capability) show up mixed with regular pages.

**Tree Layout and Positioning System:**
The visualization uses D3's tree layout algorithm configured at line 4860-4864:
```javascript
treeLayout = d3.tree()
    .nodeSize([nodeWidth + 40, nodeHeight + 60])
    .separation(function(a, b) {
        return a.parent === b.parent ? 1 : 1.2;
    });
```

The `nodeSize` setting defines spacing as `[width + 40, height + 60]` where nodeWidth and nodeHeight are fixed values. The separation function controls horizontal spacing between nodes. The layout is applied via `treeLayout(treeData)` at line 5747, which calculates X,Y positions for each node based on the hierarchical structure.

**The Wide Display Problem:**
The issue occurs because D3's tree layout naturally spreads nodes horizontally based on the tree structure. With 200+ posts, the algorithm creates an extremely wide tree that extends beyond screen boundaries. The current system uses a fixed `nodeSize` of approximately 200x120 pixels per node with 40px horizontal and 60px vertical spacing. This creates a linear horizontal expansion that becomes unmanageable with large datasets.

**Node Rendering and Title Overflow:**
Node titles are rendered as SVG `<text>` elements with the class `slmm-node-title` at lines 5776-5785. The current truncation logic is:
```javascript
const maxLength = d.data.post_type === 'site' ? 35 : 25;
return d.data.name.length > maxLength
    ? d.data.name.substring(0, maxLength) + '...'
    : d.data.name;
```

However, the CSS styling for `.slmm-node-title` at lines 1993-2001 only sets basic font properties without proper text overflow handling. The SVG text elements don't have width constraints, so long titles still render beyond their intended boundaries despite JavaScript truncation.

**Current Architecture Constraints:**
The system loads all data for a post type at once via AJAX calls to `slmm_generate_silo_grid`. The tree structure is built in memory as a D3 hierarchy, then positioned using the tree layout algorithm. The SVG canvas size is fixed, and panning/zooming is handled through D3 zoom behavior.

### For New Feature Implementation: Grid Layout with CPT Filtering

To fix these issues, we need to implement three distinct solutions that work within the existing D3 tree layout system:

**1. Grid Layout Implementation:**
Instead of relying on D3's natural tree spread, we need to implement a force-directed layout constraint that boxes nodes into a maximum width of 40 units. This requires modifying the tree layout to use a custom positioning algorithm that:
- Calculates how many nodes can fit horizontally in 40 units
- Automatically wraps to new rows when the limit is reached
- Maintains parent-child relationships through visual connectors
- Preserves the existing zoom and pan functionality

The implementation will need to override the default D3 tree positioning after `treeLayout(treeData)` but before node rendering, similar to how the current system applies transformations at line 5710.

**2. Bulletproof CPT Detection:**
The CPT filtering must be enhanced in the `analyze_wordpress_hierarchy()` method to use strict post type matching. The current logic needs modification around line 13996 to:
- Query the specific post type directly using `get_posts(array('post_type' => $specific_cpt))`
- Exclude any posts that don't match the exact post type slug
- Handle edge cases where CPTs use 'page' capability but are distinct post types
- Add additional validation to ensure no cross-contamination between post types

This requires updating both the PHP backend filtering and the JavaScript tab switching logic to pass more specific parameters.

**3. SVG Text Constraint System:**
The title overflow issue requires implementing proper SVG text constraints by:
- Adding `textLength` attributes to SVG `<text>` elements
- Implementing dynamic font-size adjustment based on available width
- Using SVG `<tspan>` elements for true ellipsis handling
- Setting proper viewport constraints for each text element

This involves modifying the node creation logic around lines 5776-5785 and updating the CSS at lines 1993-2001.

### Technical Reference Details

#### Component Interfaces & Signatures

**AJAX Handler:**
```php
public function ajax_generate_silo_grid() {
    $post_type_filter = sanitize_text_field($_POST['post_type_filter']);
    $silo_root_id = intval($_POST['silo_root_id']);
    $hierarchy_data = $this->analyze_wordpress_hierarchy($post_type_filter, $fresh_analysis_requested, $silo_root_id);
}
```

**Tree Layout Configuration:**
```javascript
treeLayout = d3.tree()
    .nodeSize([nodeWidth + 40, nodeHeight + 60])
    .separation(function(a, b) {
        return a.parent === b.parent ? 1 : 1.2;
    });
```

**Node Title Rendering:**
```javascript
nodeEnter.append('text')
    .attr('class', 'slmm-node-title')
    .attr('y', d => d.data.post_type === 'site' ? 0 : -55)
    .text(d => {
        const maxLength = d.data.post_type === 'site' ? 35 : 25;
        return d.data.name.length > maxLength
            ? d.data.name.substring(0, maxLength) + '...'
            : d.data.name;
    });
```

#### Data Structures

**Post Type Filter Structure:**
```javascript
// Sent via AJAX
{
    action: 'slmm_generate_silo_grid',
    post_type_filter: 'glossary',  // Specific CPT slug
    nonce: slmmInterlinkingData.nonce,
    tree_mode: true,
    max_depth: 5,
    include_posts: true
}
```

**Node Data Format:**
```javascript
// D3 node structure
{
    data: {
        id: '123',
        name: 'Page Title',
        post_type: 'glossary',
        post_status: 'publish',
        depth: 2
    },
    x: 100,  // Calculated by D3 tree layout
    y: 150,  // Calculated by D3 tree layout
    parent: parentNode,
    children: [childNode1, childNode2]
}
```

#### Configuration Requirements

**Constants and Settings:**
- `nodeWidth`: ~160px per node
- `nodeHeight`: ~100px per node
- `virtualRootWidth`: ~300px for site root
- `virtualRootHeight`: ~200px for site root
- Grid constraint: 40 units maximum width
- SVG canvas: Responsive based on container

#### File Locations

- **Main implementation:** `includes/interlinking/interlinking-suite.php` (lines 4860-5810 for layout, 13990+ for CPT filtering)
- **Styling:** Inline CSS in the same file (lines 1993-2001 for `.slmm-node-title`)
- **Tab navigation:** JavaScript event handlers around line 4930-4960
- **AJAX endpoints:** Method `ajax_generate_silo_grid()` around line 13720+
- **CPT detection:** Method `analyze_wordpress_hierarchy()` around line 13969+

## User Notes
### Issue 1: Wide Display Problem
- When there are 200+ posts in a CPT, they display too wide
- Need to box them at 40 wide so they auto-align in columns and rows downward
- This will make them easier to see and navigate

### Issue 2: CPT Detection Problem
- Even when "Glossaries" is selected as the CPT, normal pages are being included
- Some CPTs like glossaries are set to "Pages" as capabilities
- Need bulletproof CPT detection to ensure OTHER pages don't get mixed in
- Everything must be much stricter in filtering

### Issue 3: Title Overflow
- Titles in slmm-node-title class are spilling over their containers
- Need proper truncation with ellipses

## Work Log
- [2025-01-18] Task created to address interlinking visualization issues on large sites
- [2025-01-18] ✅ COMPLETED: Title overflow (17 chars), CPT filtering, grid layout, column control
- [2025-01-18] ❌ REMAINING: Initial load title truncation - titles overflow on first load but work correctly on expand/collapse
- [2025-01-19] ✅ COMPLETED: Hierarchical grid layout - Fixed depth preservation when using column width setting
  - Issue: Grid layout was flattening hierarchy, losing parent-child relationships
  - Root cause: Was using D3's calculated depth instead of WordPress hierarchy depth
  - Solution: Modified applyGridLayout() to use node.data.depth (WP hierarchy) exclusively
  - Result: Column width now creates tiered grids that preserve hierarchy levels
  - Documentation: Created comprehensive technical doc at `/doc/interlinking-hierarchical-grid-layout.md`

### Detailed Analysis:
The issue is that something is setting the full text AFTER truncation runs.

**What we discovered:**
1. Truncation works perfectly on node updates (expand/collapse)
2. Fails on initial load even with 500ms delay
3. ACF system might be running after my truncation

**Possible Culprits:**
1. ACF Integration - triggers on slmm_tree_rendering_complete, might override truncation
2. Tooltip System - might be setting both text content and tooltip
3. Multiple Event Handlers - other systems responding to completion events

**Strategies for Next Session:**
1. Listen for ACF completion - run truncation after ACF is done
2. Use MutationObserver - watch for text changes and re-apply truncation
3. Hook into the very end - find absolute last event in loading chain
4. Modify initial creation - fix truncation in original .text() call instead of post-processing

**Key Insight:** Truncation logic is perfect - just need right timing when nothing else will override.