<?php
/**
 * SLMM SEO Plugin - Main Plugin File
 * This file contains the actual plugin functionality
 * and is loaded by plugin.php for backward compatibility
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

// Legacy constants for backward compatibility
define('SLMM_SEO_VERSION', SLMMSEOPLUGIN_VER);
define('SLMM_SEO_PLUGIN_DIR', SLMMSEOPLUGIN_PATH);
define('SLMM_SEO_PLUGIN_URL', plugin_dir_url(SLMMSEOPLUGIN_PATH . 'plugin.php'));

// Initialize plugin
add_action('plugins_loaded', 'slmm_seo_plugin_init');

function slmm_seo_plugin_init() {
    // Always load minimal settings for visibility control
    require_once SLMM_SEO_PLUGIN_DIR . 'includes/settings/slmm-visibility-class.php';
    new _slmm_visibility();

    // Always load general settings so emergency access works
    $general_settings = new SLMM_General_Settings();
    $general_settings->init();

    // Check visibility restrictions AFTER loading basic components but before loading features
    if (!slmm_seo_check_visibility_authorization()) {
        return; // Stop feature initialization if user is not authorized (but menus still load via visibility class)
    }

    // Initialize protected words functionality
    SLMM_Protected_Words::get_instance();

    // Initialize Lorem Ipsum Detector
    require_once SLMM_SEO_PLUGIN_DIR . 'includes/utils/lorem-ipsum-detector.php';
    new SLMM_Lorem_Ipsum_Detector();

    // Initialize URL Renderer AJAX Handler
    require_once SLMM_SEO_PLUGIN_DIR . 'includes/ajax/url-renderer-handler.php';

    // Initialize Page Summarization AJAX Handler
    require_once SLMM_SEO_PLUGIN_DIR . 'includes/features/page-summarization/class-page-summary-manager.php';
    require_once SLMM_SEO_PLUGIN_DIR . 'includes/ajax/page-summarization-handler.php';
    SLMM_Page_Summarization_Handler::get_instance();

    // Setup clean view functionality for URL renderer
    add_action('init', 'slmm_setup_clean_view_handler');

    (new SLMM_Prompt_Settings())->init();

    // Initialize AI integrations
    (new SLMM_OpenAI_Integration())->initialize();
    (new SLMM_Anthropic_Integration())->initialize();

    // Initialize Interlinking Suite
    require_once SLMM_SEO_PLUGIN_DIR . 'includes/interlinking/interlinking-suite.php';
    SLMM_Interlinking_Suite::get_instance();

    // Initialize Memory Leak Tester (development/testing)
    require_once SLMM_SEO_PLUGIN_DIR . 'includes/interlinking/memory-leak-tester.php';
    require_once SLMM_SEO_PLUGIN_DIR . 'includes/interlinking/memory-testing-interface.php';
    if (defined('WP_DEBUG') && WP_DEBUG) {
        new SLMM_Memory_Leak_Tester();
        new SLMM_Memory_Testing_Interface();
    }

    // Initialize Link Popup Handler (for Interlinking Suite link indicators)
    if (class_exists('SLMM_Link_Popup_Handler')) {
        SLMM_Link_Popup_Handler::get_instance();
    }

    // Initialize Direct Post Editor (for Interlinking Suite integration)
    if (class_exists('SLMM_Direct_Editor')) {
        SLMM_Direct_Editor::get_instance();
    }
    
    // Initialize Semantic Links System (for cross-silo interlinking)
    if (class_exists('SLMM_Semantic_Links_DB')) {
        SLMM_Semantic_Links_DB::get_instance();
    }
    
    if (class_exists('SLMM_Semantic_Links_AJAX_Handler')) {
        SLMM_Semantic_Links_AJAX_Handler::get_instance();
    }

    // Initialize QuickBulk System for Canvas Integration
    if (class_exists('SLMM_Bulk_Page_Creator')) {
        SLMM_Bulk_Page_Creator::get_instance();
    }
    
    if (class_exists('SLMM_QuickBulk_AJAX_Handler')) {
        SLMM_QuickBulk_AJAX_Handler::get_instance();
    }

    // Initialize Content Segmentation System (SINGLETON)
    if (class_exists('SLMM_Content_Segmentation')) {
        SLMM_Content_Segmentation::get_instance();
    }

    if (class_exists('SLMM_Segmentation_Handler')) {
        SLMM_Segmentation_Handler::get_instance();
    }

    // Remove the old ChatGPT Generator menu item
    remove_action('admin_menu', array('ChatGPT_Generator', 'add_options_page'));
}

/**
 * Check if the current user is authorized to use the plugin
 * This function is called BEFORE any plugin features load
 */
function slmm_seo_check_visibility_authorization() {
    // If WordPress functions aren't loaded yet, allow everything to prevent WOD
    if (!function_exists('is_admin') || !function_exists('wp_get_current_user') || !function_exists('current_user_can')) {
        return true; // Allow during early WordPress loading
    }

    // If we're not in admin, allow everything (frontend functionality)
    if (!is_admin()) {
        return true;
    }

    // SECURE EMERGENCY ACCESS: Only works for already logged-in admins
    if (isset($_GET["slmm_emergency"]) && function_exists("slmm_seo_verify_emergency_access") && slmm_seo_verify_emergency_access($_GET["slmm_emergency"])) {
        return true; // Authorized emergency access for logged-in admins only
    }

    // Get current user
    $current_user = wp_get_current_user();

    // Get settings from SLMM options
    $settings = function_exists('get_option') ? get_option('chatgpt_generator_options', array()) : array();

    // If visibility settings aren't enabled, allow all administrators
    if (!isset($settings['visibility_enabled']) || $settings['visibility_enabled'] !== true) {
        return current_user_can('administrator');
    }

    // SECURE: Only authorized admins can access - fail secure by default
    if (!current_user_can('administrator')) {
        return false; // Must be admin first
    }

    // Get the authorized admin usernames
    $authorized_admins = isset($settings['authorized_admins']) ? $settings['authorized_admins'] : array();

    // Convert legacy single admin to array if needed
    if (empty($authorized_admins) && isset($settings['authorized_admin']) && !empty($settings['authorized_admin'])) {
        $authorized_admins = array($settings['authorized_admin']);
    }

    // Get valid admin usernames (without hardcoded backdoor)
    $valid_authorized_admins = slmm_seo_get_valid_admin_usernames($authorized_admins);

    // FAIL SECURE: If no valid authorized admins exist, deny access to prevent lockouts
    if (empty($valid_authorized_admins)) {
        return false;
    }

    // Check if the current user is in the list of valid authorized admins
    return in_array($current_user->user_login, $valid_authorized_admins);
}

/**
 * Get valid admin usernames from a list (removed hardcoded backdoor)
 */
function slmm_seo_get_valid_admin_usernames($usernames) {
    $valid_usernames = array();

    // If WordPress functions aren't available, return empty array
    if (!function_exists('username_exists') || !function_exists('get_user_by')) {
        return $valid_usernames;
    }

    if (!is_array($usernames)) {
        return $valid_usernames;
    }

    foreach ($usernames as $username) {
        // Skip empty usernames
        if (empty($username)) {
            continue;
        }

        // Check if the username exists first
        if (!function_exists('username_exists') || username_exists($username) === false) {
            continue;
        }

        // Get user data by login
        $user = get_user_by('login', $username);

        // Check if user exists and is an administrator
        if ($user && in_array('administrator', $user->roles)) {
            $valid_usernames[] = $username;
        }
    }

    return $valid_usernames;
}

// Include necessary files
function slmm_seo_include_files() {
    // Always include these core files for basic functionality
    $core_files = [
        'includes/settings/general-settings.php',
        'includes/settings/slmm-visibility-class.php',
        'WFPCore/WordPressContext.php'
    ];
    
    foreach ($core_files as $file) {
        $file_path = SLMM_SEO_PLUGIN_DIR . $file;
        if (file_exists($file_path)) {
            require_once $file_path;
        }
    }
    
    // Only load feature files if user is authorized
    if (!slmm_seo_check_visibility_authorization()) {
        return; // Stop loading feature files if user is not authorized
    }
    
    $feature_files = [
        'includes/settings/prompt-settings.php',
        'includes/ai-integration/openai-integration.php',
        'includes/ai-integration/openrouter-integration.php',
        'includes/ai-integration/anthropic-integration.php',
        'includes/utils/protected-words.php',
        'includes/utils/broken-links.php',
        'includes/utils/notes.php',
        'includes/bulk-creation/class-slmm-bulk-page-creator.php',
        'includes/bulk-creation/class-slmm-quickbulk-ajax-handler.php',
        'includes/features/direct-editing/class-slmm-direct-editor.php',
        'includes/features/direct-editing/class-slmm-editor-ajax-handler.php',
        'includes/interlinking/class-link-popup-handler.php',
        'includes/interlinking/class-slmm-semantic-links-db.php',
        'includes/interlinking/class-slmm-semantic-links-ajax-handler.php',
        'includes/content-segmentation/class-content-segmentation.php',
        'includes/ajax/segmentation-handler.php',
        'includes/ajax/ai-interlinking-handler.php',
        'snippets/seo_text_helper_2_3.php',
        'snippets/chat_gpt_title_and_description_generator_v2_0.php',
        'snippets/content-freshness.php',
        'snippets/seo_pre_fight_checklist_classic_editor_edition.php',
        'snippets/prompts-repeater.php',
        'src/seo_overview_meta_box.php'
    ];

    foreach ($feature_files as $file) {
        $file_path = SLMM_SEO_PLUGIN_DIR . $file;
        if (file_exists($file_path)) {
            require_once $file_path;
        } else {
            error_log("SLMM SEO Plugin: Unable to include file - $file_path");
        }
    }
}
slmm_seo_include_files();

// Load protected words functionality on all admin pages
function slmm_load_protected_words_admin() {
    // Check visibility authorization before loading any scripts
    if (!slmm_seo_check_visibility_authorization()) {
        return; // Don't load any scripts if user is not authorized
    }
    
    // Load protected words script first with a lower priority
    wp_enqueue_script('slmm-protected-words', SLMM_SEO_PLUGIN_URL . 'js/protected-words.js', array('jquery'), SLMM_SEO_VERSION, false);
    
    // Pass protected words to JavaScript
    $protected_words = SLMM_Protected_Words::get_instance()->get_protected_words_array();
    wp_localize_script('slmm-protected-words', 'slmmProtectedWords', array(
        'words' => $protected_words,
        'ajaxurl' => admin_url('admin-ajax.php')
    ));
}
// Add with high priority (low number) to ensure it loads first
add_action('admin_enqueue_scripts', 'slmm_load_protected_words_admin', 5);

// Enqueue scripts and styles
function slmm_seo_enqueue_scripts($hook) {
    // Check if we're in a post editor OR Bricks Builder context
    $is_bricks_context = isset($_GET['bricks']) && $_GET['bricks'] === 'run';
    
    if (('post.php' != $hook && 'post-new.php' != $hook) && !$is_bricks_context) {
        return;
    }
    
    // Debug logging for script loading
    error_log('SLMM DEBUG: slmm_seo_enqueue_scripts called - Hook: ' . $hook . ', Bricks context: ' . ($is_bricks_context ? 'yes' : 'no'));

    // Remove duplicate protected words script loading
    wp_enqueue_script('slmm-keyword-checker', SLMM_SEO_PLUGIN_URL . 'src/keywordChecker.js', array('jquery', 'slmm-protected-words'), SLMM_SEO_VERSION, true);
    wp_enqueue_script('slmm-prompt-execution', SLMM_SEO_PLUGIN_URL . 'assets/js/slmm-prompt-execution.js', array('jquery', 'slmm-protected-words'), SLMM_SEO_VERSION, true);
    
    // Enqueue QuickBulk Canvas Integration Scripts and Styles
    wp_enqueue_style('slmm-quickbulk-canvas', SLMM_SEO_PLUGIN_URL . 'assets/css/quickbulk-canvas.css', array(), SLMM_SEO_VERSION);
    wp_enqueue_script('slmm-quickbulk-canvas', SLMM_SEO_PLUGIN_URL . 'assets/js/quickbulk-canvas-integration.js', array('jquery'), SLMM_SEO_VERSION, true);
    wp_enqueue_script('slmm-quickbulk-d3-integration', SLMM_SEO_PLUGIN_URL . 'assets/js/quickbulk-d3-integration.js', array('jquery'), SLMM_SEO_VERSION, true);
    
    // Enqueue Content Segmentation Styles
    wp_enqueue_style('slmm-content-segmentation', SLMM_SEO_PLUGIN_URL . 'assets/css/content-segmentation.css', array(), SLMM_SEO_VERSION);
    
    // Localize prompt data for the execution script - ALWAYS localize so shortcuts work
    $prompts = get_option('slmm_gpt_prompts', array());
    
    // Get interlinking prompts and rules for AI suggestions
    $interlinking_prompts = get_option('slmm_interlinking_prompts', array());
    $interlinking_rules = get_option('slmm_interlinking_rules', '');
    
    // Get AI provider settings for interlinking integration
    $chatgpt_options = get_option('chatgpt_generator_options', array());
    $openrouter_api_key = $chatgpt_options['openrouter_api_key'] ?? '';
    $anthropic_api_key = get_option('slmm_anthropic_api_key', '');
    
    $localized_data = array(
        'prompts' => $prompts,
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('slmm_execute_gpt_prompt'),
        'interlinking_nonce' => wp_create_nonce('slmm_ai_interlinking_suggest'),
        'interlinking_prompts' => $interlinking_prompts,
        'interlinking_rules' => $interlinking_rules,
        'ai_providers' => array(
            'openai_api_key' => $chatgpt_options['openai_api_key'] ?? '',
            'openrouter_api_key' => $openrouter_api_key,
            'anthropic_api_key' => $anthropic_api_key,
            'default_provider' => $chatgpt_options['ai_provider'] ?? 'openai',
            'default_model' => $chatgpt_options['model'] ?? 'gpt-4'
        ),
        'is_bricks_context' => $is_bricks_context,
        'hook' => $hook,
        'debug_info' => array(
            'prompts_count' => count($prompts),
            'interlinking_prompts_count' => count($interlinking_prompts),
            'url' => $_SERVER['REQUEST_URI'] ?? '',
            'timestamp' => time()
        )
    );
    
    // Localize QuickBulk data for canvas integration
    $quickbulk_data = array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('slmm_interlinking_nonce'),
        'quickbulk_nonce' => wp_create_nonce('slmm_quickbulk_create_pages'),
        'suggestions_nonce' => wp_create_nonce('slmm_generate_contextual_suggestions'),
        'validate_nonce' => wp_create_nonce('slmm_validate_page_titles'),
        'positions_nonce' => wp_create_nonce('slmm_calculate_grid_positions'),
        'stats_nonce' => wp_create_nonce('slmm_get_bulk_creation_stats'),
        'health_nonce' => wp_create_nonce('slmm_quickbulk_health_check'),
        'is_bricks_context' => $is_bricks_context,
        'current_post_id' => get_the_ID(),
        'user_can_edit_pages' => current_user_can('edit_pages'),
        'debug_mode' => defined('WP_DEBUG') && WP_DEBUG
    );
    
    wp_localize_script('slmm-prompt-execution', 'slmmGptPromptData', $localized_data);
    
    // ALWAYS localize to Direct Editor for AI interlinking - removed conditional check that was failing
    // This ensures interlinking data is available regardless of script registration timing
    wp_localize_script('slmm-direct-editor', 'slmmGptPromptData', $localized_data);
    
    // Also localize to jquery as fallback for Direct Editor context
    wp_localize_script('jquery', 'slmmGptPromptData', $localized_data);
    
    // Also ensure it's available globally via jQuery for the chat_gpt_title_and_description_generator_v2_0.php
    wp_localize_script('jquery', 'slmmGptPromptDataGlobal', $localized_data);
    
    // Localize QuickBulk data for canvas integration scripts
    wp_localize_script('slmm-quickbulk-canvas', 'slmmQuickBulkData', $quickbulk_data);
    wp_localize_script('slmm-quickbulk-d3-integration', 'slmmQuickBulkData', $quickbulk_data);
    
    // Debug logging
    error_log('SLMM DEBUG: Localized slmmGptPromptData with ' . count($prompts) . ' prompts');
    error_log('SLMM DEBUG: QuickBulk Canvas Integration scripts and styles loaded');
}
add_action('admin_enqueue_scripts', 'slmm_seo_enqueue_scripts');


// SEO Text Helper functionality is now included directly in seo_text_helper_2_3.php

// Include Bricks Builder integration
require_once plugin_dir_path(__FILE__) . 'snippets/bricks-builder-integration.php';

function chatgpt_generator_enqueue_scripts_v2($hook) {
    if ($hook === 'post.php' || $hook === 'post-new.php') {
        wp_enqueue_script('jquery');
        
        $plugin_url = plugins_url('', SLMMSEOPLUGIN_PATH . 'plugin.php');

        // Ensure our scripts load after TinyMCE
        $js_files = array(
            'keywordChecker.js',
            'seoTools.js'
        );

        foreach ($js_files as $file) {
            $file_path = $plugin_url . '/src/' . $file;
            $handle = str_replace('.js', '', $file);
            wp_enqueue_script($handle, $file_path, array('jquery', 'tinymce'), SLMM_SEO_VERSION, true);
        }

        // Localize script data
        $localized_data = array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'plugin_url' => $plugin_url,
            'version' => SLMM_SEO_VERSION,
            'debug' => false // Disable debug mode
        );
        wp_localize_script('keywordChecker', 'slmmSeoData', $localized_data);

        // Add inline styles with !important to ensure they take precedence
        wp_add_inline_style('wp-admin', "
            #chatgpt-floating-buttons {
                position: fixed !important;
                top: 40px !important;
                right: 20px !important;
                z-index: 100000 !important;
            }
            .chatgpt-button {
                background-color: #00008B !important;
                color: white !important;
                border-color: #00008B !important;
            }
        ");
    }
}
add_action('admin_enqueue_scripts', 'chatgpt_generator_enqueue_scripts_v2', 100);

function generate_description() {
    check_ajax_referer('chatgpt_generator_nonce', 'security');

    $prompt = isset($_POST['prompt']) ? sanitize_textarea_field($_POST['prompt']) : '';
    $highlighted_text = isset($_POST['highlighted_text']) ? sanitize_text_field($_POST['highlighted_text']) : '';
    $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : 'gpt-3.5-turbo';

    if (empty($prompt) || empty($highlighted_text)) {
        wp_send_json_error('No prompt or highlighted text provided');
        return;
    }

    $options = get_option('chatgpt_generator_options', array());
    $api_key = isset($options['openai_api_key']) ? $options['openai_api_key'] : '';

    if (empty($api_key)) {
        wp_send_json_error('OpenAI API key is not set');
        return;
    }

    // Replace {INSERT} with the highlighted text
    $prompt = str_replace('{INSERT}', $highlighted_text, $prompt);

    $openai = new OpenAI($api_key);
    try {
        $response = $openai->chat([
            'model' => $model,
            'messages' => [
                ['role' => 'system', 'content' => 'You are a helpful assistant that generates SEO descriptions.'],
                ['role' => 'user', 'content' => $prompt]
            ],
            'max_tokens' => 150,
            'temperature' => 0.7,
        ]);

        $description = $response['choices'][0]['message']['content'] ?? '';
        wp_send_json_success(trim($description));
    } catch (Exception $e) {
        wp_send_json_error('Error generating description: ' . $e->getMessage());
    }
}
add_action('wp_ajax_generate_description', 'generate_description');

// Unified AJAX handler for both OpenAI and OpenRouter
function slmm_generate_content() {
    check_ajax_referer('chatgpt_generate_content', 'security');

    $prompt = isset($_POST['prompt']) ? sanitize_textarea_field($_POST['prompt']) : '';
    $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : 'gpt-4o';
    $provider = isset($_POST['provider']) ? sanitize_text_field($_POST['provider']) : 'openai';

    if (empty($prompt)) {
        wp_send_json_error('Prompt is empty');
    }

    if ($provider === 'openrouter') {
        // Use OpenRouter
        require_once plugin_dir_path(__FILE__) . 'includes/ai-integration/openrouter-integration.php';
        $openrouter = new SLMM_OpenRouter_Integration();
        
        if (!$openrouter->is_configured()) {
            wp_send_json_error('OpenRouter API key is not configured');
        }
        
        $result = $openrouter->generate_content($prompt, $model);
        
        if (is_wp_error($result)) {
            wp_send_json_error('Error calling OpenRouter API: ' . $result->get_error_message());
        }
        
        wp_send_json_success($result);
    } else {
        // Use OpenAI
        $options = get_option('chatgpt_generator_options', array());
        $api_key = isset($options['openai_api_key']) ? $options['openai_api_key'] : '';
        
        if (empty($api_key)) {
            wp_send_json_error('OpenAI API key is not configured');
        }

        // Make API call to OpenAI
        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode(array(
                'model' => $model,
                'messages' => array(
                    array('role' => 'user', 'content' => $prompt)
                ),
                'max_tokens' => 1000,
                'temperature' => 0.7,
            )),
            'timeout' => 60,
        ));

        if (is_wp_error($response)) {
            wp_send_json_error('Error calling OpenAI API: ' . $response->get_error_message());
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        if (isset($body['choices'][0]['message']['content'])) {
            wp_send_json_success($body['choices'][0]['message']['content']);
        } else {
            wp_send_json_error('Unexpected response from OpenAI API');
        }
    }
}
add_action('wp_ajax_slmm_generate_content', 'slmm_generate_content');

function generate_vs_snippet() {
    check_ajax_referer('chatgpt_generator_vs_nonce', 'security');

    $prompt = isset($_POST['prompt']) ? sanitize_textarea_field($_POST['prompt']) : '';
    $highlighted_text = isset($_POST['highlighted_text']) ? sanitize_text_field($_POST['highlighted_text']) : '';
    $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : 'gpt-3.5-turbo';

    if (empty($prompt) || empty($highlighted_text)) {
        wp_send_json_error('No prompt or highlighted text provided');
        return;
    }

    $options = get_option('chatgpt_generator_options', array());
    $api_key = isset($options['openai_api_key']) ? $options['openai_api_key'] : '';

    if (empty($api_key)) {
        wp_send_json_error('OpenAI API key is not set');
        return;
    }

    // Replace {INSERT} with the highlighted text
    $prompt = str_replace('{INSERT}', $highlighted_text, $prompt);

    $openai = new OpenAI($api_key);
    try {
        $response = $openai->chat([
            'model' => $model,
            'messages' => [
                ['role' => 'system', 'content' => 'You are a helpful assistant that generates VS snippets.'],
                ['role' => 'user', 'content' => $prompt]
            ],
            'max_tokens' => 150,
            'temperature' => 0.7,
        ]);

        $snippet = $response['choices'][0]['message']['content'] ?? '';
        wp_send_json_success(trim($snippet));
    } catch (Exception $e) {
        wp_send_json_error('Error generating VS snippet: ' . $e->getMessage());
    }
}
add_action('wp_ajax_generate_vs_snippet', 'generate_vs_snippet'); 

/**
 * Setup clean view handler for URL renderer iframe display
 */
function slmm_setup_clean_view_handler() {
    // Check if this is a clean view request for URL renderer
    if (isset($_GET['slmm_clean_view']) && $_GET['slmm_clean_view'] === '1') {
        // Hide admin bar for clean display
        add_filter('show_admin_bar', '__return_false');
        
        // Remove unnecessary WordPress overhead for iframe display
        remove_action('wp_head', '_admin_bar_bump_cb');
        
        // Add custom CSS to hide any remaining admin elements
        add_action('wp_head', 'slmm_clean_view_styles');
        
        // Remove WordPress admin bar inline styles
        add_action('wp_print_styles', function() {
            wp_dequeue_style('admin-bar');
        }, 100);
        
        // Remove admin bar scripts
        add_action('wp_print_scripts', function() {
            wp_dequeue_script('admin-bar');
        }, 100);
    }
}

/**
 * Add custom styles for clean view
 */
function slmm_clean_view_styles() {
    echo '<style>
        /* Hide admin bar and related elements */
        #wpadminbar,
        .admin-bar #wpadminbar,
        .wp-toolbar {
            display: none !important;
        }
        
        /* Remove top margin that admin bar adds */
        html.wp-toolbar,
        body.admin-bar,
        .admin-bar body {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }
        
        /* Clean up any remaining admin elements */
        .wp-admin,
        .wp-admin-bar-related {
            display: none !important;
        }
        
        /* Ensure clean iframe display */
        body {
            margin: 0 !important;
            padding: 0 !important;
        }
    </style>';
}
/**
 * Generate static emergency access URL for logged-in admins
 * This creates a long, secure URL that only works for already-authenticated admin users
 */
function slmm_seo_get_emergency_url() {
    $emergency_key = get_option('slmm_emergency_access_key');
    
    if (!$emergency_key) {
        // Generate a 64-character alphanumeric string
        $emergency_key = slmm_seo_generate_emergency_key();
        update_option('slmm_emergency_access_key', $emergency_key);
    }
    
    return 'access' . $emergency_key;
}

/**
 * Generate a 64-character emergency access key
 */
function slmm_seo_generate_emergency_key() {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $key = '';
    
    for ($i = 0; $i < 64; $i++) {
        $key .= $chars[wp_rand(0, strlen($chars) - 1)];
    }
    
    return $key;
}

/**
 * Verify emergency access - ONLY works for logged-in admins
 * This provides a secure way for legitimate admins to access the plugin in emergencies
 */
function slmm_seo_verify_emergency_access($provided_key) {
    // CRITICAL: Must be logged in as admin first
    if (!is_user_logged_in() || !current_user_can('administrator')) {
        return false;
    }
    
    // Must start with 'access' and be at least 70 characters
    if (strlen($provided_key) < 70 || substr($provided_key, 0, 6) !== 'access') {
        return false;
    }
    
    $stored_key = get_option('slmm_emergency_access_key');
    if (!$stored_key) {
        return false;
    }
    
    $expected_key = 'access' . $stored_key;
    
    // Log the access attempt for security monitoring
    if ($provided_key === $expected_key) {
        $current_user = wp_get_current_user();
        error_log("SLMM Emergency Access: Admin '{$current_user->user_login}' used emergency URL at " . date('Y-m-d H:i:s'));

        // AUTO-WHITELIST: Add current admin to authorized_admins if not already whitelisted
        $settings = get_option('chatgpt_generator_options', array());
        $authorized_admins = isset($settings['authorized_admins']) ? $settings['authorized_admins'] : array();

        if (!in_array($current_user->user_login, $authorized_admins)) {
            // Add current admin to whitelist to resolve lockout
            $authorized_admins[] = $current_user->user_login;
            $settings['authorized_admins'] = $authorized_admins;
            update_option('chatgpt_generator_options', $settings);
            error_log("SLMM Emergency Access: Auto-whitelisted admin '{$current_user->user_login}'");
        }

        return true; // Emergency access granted
    }
    
    return false;
}

/**
 * Get the full emergency URL for display to admins
 */
function slmm_seo_get_emergency_full_url() {
    $emergency_param = slmm_seo_get_emergency_url();
    $admin_url = admin_url('admin.php?page=chatgpt-generator-settings');
    return $admin_url . '&slmm_emergency=' . $emergency_param;
}

/**
 * Regenerate emergency access key (for security)
 */
function slmm_seo_regenerate_emergency_key() {
    $new_key = slmm_seo_generate_emergency_key();
    update_option('slmm_emergency_access_key', $new_key);
    return 'access' . $new_key;
}
