<?php
/**
 * BRICKS BUILDER Integration for SLMM SEO Bundle
 * This file handles script loading and functionality specifically for Bricks Builder
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class SLMM_Bricks_Integration {
    
    public function __construct() {
        // Hook early to catch Brick<PERSON> Builder requests
        add_action('init', array($this, 'detect_bricks_and_inject'), 1);
        add_action('wp_head', array($this, 'inject_bricks_scripts'), 1);
        add_action('admin_head', array($this, 'inject_bricks_scripts'), 1);
    }
    
    /**
     * Detect if we're in Bricks Builder context
     */
    public function is_bricks_context() {
        return isset($_GET['bricks']) && $_GET['bricks'] === 'run';
    }
    
    /**
     * Early detection and setup for Bricks
     */
    public function detect_bricks_and_inject() {
        if ($this->is_bricks_context()) {
            error_log('SLMM DEBUG [Bricks Builder]: Context detected - initializing integration');
            
            // Force load our dependencies
            add_action('wp_enqueue_scripts', array($this, 'enqueue_bricks_scripts'), 1);
            add_action('admin_enqueue_scripts', array($this, 'enqueue_bricks_scripts'), 1);
            
            // Hook into Bricks-specific actions if they exist
            add_action('bricks/setup', array($this, 'on_bricks_setup'), 10);
            add_action('bricks/frontend/render_element', array($this, 'on_bricks_render'), 10);
        }
    }
    
    /**
     * Enqueue scripts specifically for Bricks Builder
     */
    public function enqueue_bricks_scripts() {
        if (!$this->is_bricks_context()) {
            return;
        }
        
        error_log('SLMM DEBUG [Bricks Builder]: Enqueuing scripts for Bricks Builder');
        
        // Enqueue jQuery first
        wp_enqueue_script('jquery');
        
        // Get plugin URL
        $plugin_url = plugins_url('', dirname(__FILE__));
        
        // Enqueue our main scripts
        wp_enqueue_style('slmm-admin-styles', $plugin_url . '/assets/css/slmm-admin.css', array(), '1.0.0');
        wp_enqueue_script('slmm-keyboard-shortcuts', $plugin_url . '/js/slmm-keyboard-shortcuts.js', array('jquery'), '1.0.0', true);
        wp_enqueue_script('slmm-prompt-execution', $plugin_url . '/assets/js/slmm-prompt-execution.js', array('jquery'), '1.0.0', true);
        
        // Enqueue notes functionality if enabled
        $options = get_option('chatgpt_generator_options', array());
        $enable_notes = isset($options['enable_notes']) ? $options['enable_notes'] : true;
        if ($enable_notes) {
            wp_enqueue_style('slmm-notes-style', $plugin_url . '/assets/css/slmm-notes.css', array(), '1.0.0');
            wp_enqueue_script('slmm-notes-script', $plugin_url . '/assets/js/slmm-notes.js', array('jquery'), '1.0.0', true);
        }
        
        // Prepare localized data
        $prompts = get_option('slmm_gpt_prompts', array());
        $chatgpt_options = get_option('chatgpt_generator_options', array());
        
        $localized_data = array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('slmm_execute_gpt_prompt'),
            'prompts' => $prompts,
            'is_bricks_context' => true,
            'api_key' => $chatgpt_options['openai_api_key'] ?? '',
            'openrouter_api_key' => $chatgpt_options['openrouter_api_key'] ?? '',
            'title_prompt' => $chatgpt_options['title_prompt'] ?? '',
            'description_prompt' => $chatgpt_options['description_prompt'] ?? '',
            'business_name' => $chatgpt_options['business_name'] ?? '',
            'phone_number' => $chatgpt_options['phone_number'] ?? '',
            'title_model' => $chatgpt_options['model_for_title'] ?? 'gpt-4o',
            'description_model' => $chatgpt_options['model_for_description'] ?? 'gpt-4o',
            'title_provider' => $chatgpt_options['model_for_title_provider'] ?? 'openai',
            'description_provider' => $chatgpt_options['model_for_description_provider'] ?? 'openai',
            'debug_info' => array(
                'prompts_count' => count($prompts),
                'url' => $_SERVER['REQUEST_URI'] ?? '',
                'timestamp' => time(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            )
        );
        
        // Localize for multiple script handles to ensure availability
        // Note: chatgptGeneratorData is NOT localized here - only available in WordPress classic editor context
        wp_localize_script('jquery', 'slmmGptPromptData', $localized_data);
        wp_localize_script('jquery', 'SaveWithKeyboard', array('enabled' => '1'));
        
        // Localize notes data if notes are enabled
        if ($enable_notes) {
            wp_localize_script('jquery', 'slmmNotes', array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('slmm_notes_nonce'),
                'strings' => array(
                    'saving' => __('Saving...', 'slmm-seo-bundle'),
                    'saved' => __('✓ Saved', 'slmm-seo-bundle'),
                    'error' => __('Error saving note', 'slmm-seo-bundle')
                )
            ));
        }
        
        error_log('SLMM DEBUG [Bricks Builder]: Localized data with ' . count($prompts) . ' prompts');
    }
    
    /**
     * Inject comprehensive Bricks Builder integration script
     */
    public function inject_bricks_scripts() {
        if (!$this->is_bricks_context()) {
            return;
        }
        
        ?>
        <script type="text/javascript">
        // Fallback function if SLMM.debug is not yet available
        function debugLog(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log(category, message, data);
            } else {
                console.log('[' + category + '] ' + message, data || '');
            }
        }
        
        function debugWarn(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.warn(category, message, data);
            } else {
                console.warn('[' + category + '] ' + message, data || '');
            }
        }
        
        function debugError(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.error(category, message, data);
            } else {
                console.error('[' + category + '] ' + message, data || '');
            }
        }
        
        debugLog('Bricks Builder', '=== INTEGRATION LOADING ===');
        
        // Wait for document ready and ensure jQuery is available
        (function() {
            var initBricks = function() {
                if (typeof jQuery === 'undefined') {
                    debugWarn('Bricks Builder', 'jQuery not available yet, retrying...');
                    setTimeout(initBricks, 500);
                    return;
                }
                
                var $ = jQuery;
                debugLog('Bricks Builder', 'Integration script loaded with jQuery');
                debugLog('Bricks Builder', 'URL: ' + window.location.href);
                
                // Comprehensive status report immediately
                debugLog('Bricks Builder', '=== IMMEDIATE STATUS ===');
                debugLog('Bricks Builder', 'slmmGptPromptData available: ' + (typeof slmmGptPromptData !== 'undefined'));
                debugLog('Bricks Builder', 'chatgptGeneratorData available: ' + (typeof chatgptGeneratorData !== 'undefined') + ' (intentionally restricted to classic editor only)');
                debugLog('Bricks Builder', 'SaveWithKeyboard available: ' + (typeof SaveWithKeyboard !== 'undefined'));
                if (typeof slmmGptPromptData !== 'undefined') {
                    debugLog('Bricks Builder', 'slmmGptPromptData', slmmGptPromptData);
                }
                
                                 // Inject CSS immediately for tooltips and UI
                injectBricksCSS();
                
                // Initialize immediately and then continue monitoring
                 setTimeout(function() {
                     initializeBricksIntegration();
                 }, 1000);
             };
             
             // Start the initialization
             initBricks();
         })();
        
        function initializeBricksIntegration() {
            debugLog('Bricks Builder', 'Initializing TinyMCE integration');
            
            // Monitor for editors only (no keyboard shortcuts)
            monitorBricksEditors();
            
                    // Notes functionality is handled via wp-admin bar only
        // No separate Bricks toolbar button needed
            
            debugLog('Bricks Builder', 'Integration ready - manual button execution only');
        }
        
        // Bricks Builder notes functionality removed
        // Notes are only accessible via wp-admin bar to avoid confusion with save buttons
        
                 function monitorBricksEditors() {
             debugLog('Bricks Builder', 'Starting editor monitoring');
             
             var checkForBricksEditors = function() {
                 if (typeof tinyMCE !== 'undefined') {
                     var allEditors = Object.keys(tinyMCE.editors);
                                              var bricksEditors = allEditors.filter(id => id.indexOf('brickswpeditor') !== -1);
                     var newEditors = 0;
                     
                     bricksEditors.forEach(function(editorId) {
                         var editor = tinyMCE.editors[editorId];
                         if (editor && editor.initialized && !editor._slmmBricksSetup) {
                             debugLog('Bricks Builder', 'Found Bricks editor: ' + editorId);
                             editor._slmmBricksSetup = true;
                             newEditors++;
                         }
                     });
                     
                     if (newEditors > 0) {
                         debugLog('Bricks Builder', 'Set up ' + newEditors + ' new Bricks editors');
                     }
                 }
                 
                 // Continue monitoring but less frequently
                 setTimeout(checkForBricksEditors, 3000);
             };
             
             checkForBricksEditors();
         }
        

        
        function executeBricksGptPrompt(promptIndex, editor) {
            debugLog('Bricks Builder', 'Executing GPT prompt ' + promptIndex + ' in Bricks context');
            
            // Find active editor if not provided
            var activeEditor = editor;
            if (!activeEditor && typeof tinyMCE !== 'undefined') {
                // Look for focused Bricks editor first
                for (var editorId in tinyMCE.editors) {
                    if (editorId.indexOf('brickswpeditor') !== -1) {
                        var bricksEditor = tinyMCE.editors[editorId];
                        if (bricksEditor && bricksEditor.initialized && bricksEditor.hasFocus()) {
                            activeEditor = bricksEditor;
                            break;
                        }
                    }
                }
                
                // If no focused editor, use the first available Bricks editor
                if (!activeEditor) {
                    for (var editorId in tinyMCE.editors) {
                        if (editorId.indexOf('brickswpeditor') !== -1) {
                            var bricksEditor = tinyMCE.editors[editorId];
                            if (bricksEditor && bricksEditor.initialized) {
                                activeEditor = bricksEditor;
                                break;
                            }
                        }
                    }
                }
            }
            
            debugLog('Bricks Builder', 'Active editor found: ' + (activeEditor ? activeEditor.id : 'none'));
            
            if (!activeEditor) {
                debugError('Bricks Builder', 'No active Bricks editor found');
                alert('No active editor found. Please click in a text editor first.');
                return;
            }
            
            // Get selected text
            var selectedText = activeEditor.selection.getContent({format: 'text'});
            debugLog('Bricks Builder', 'Selected text: ' + selectedText);
            
            if (!selectedText) {
                debugWarn('Bricks Builder', 'No text selected');
                alert('Please select some text before executing a prompt.');
                return;
            }
            
            // Check if we have prompt data
            if (typeof slmmGptPromptData === 'undefined' || !slmmGptPromptData.prompts) {
                debugError('Bricks Builder', 'No prompt data available');
                alert('GPT Prompt data not available. Please refresh the page and try again.');
                return;
            }
            
            var promptData = slmmGptPromptData.prompts[promptIndex];
            if (!promptData) {
                debugError('Bricks Builder', 'Prompt not found at index: ' + promptIndex);
                debugError('Bricks Builder', 'Available prompts', slmmGptPromptData.prompts);
                alert('Prompt not found at index: ' + promptIndex);
                return;
            }
            
            debugLog('Bricks Builder', 'Executing prompt', promptData);
            
            // Show notification
            showBricksNotification('Generating content with: ' + (promptData.title || 'Prompt ' + (promptIndex + 1)));
            
            // Make AJAX request
            jQuery.ajax({
                url: slmmGptPromptData.ajax_url,
                method: 'POST',
                data: {
                    action: 'slmm_execute_gpt_prompt',
                    nonce: slmmGptPromptData.nonce,
                    prompt_index: promptIndex.toString(),
                    selected_text: selectedText
                },
                success: function(response) {
                    debugLog('Bricks Builder', 'GPT response', response);
                    if (response.success) {
                        activeEditor.selection.setContent(response.data);
                        activeEditor.focus();
                        showBricksNotification('✓ Content generated successfully!', 'success');
                    } else {
                        debugError('Bricks Builder', 'GPT error: ' + response.data);
                        showBricksNotification('Error: ' + response.data, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    debugError('Bricks Builder', 'AJAX error: ' + status + ' - ' + error);
                    debugError('Bricks Builder', 'XHR', xhr);
                    showBricksNotification('Network error. Please try again.', 'error');
                }
            });
        }
        
        function showBricksNotification(message, type) {
            debugLog('Bricks Builder', 'Showing notification: ' + message + ' (' + type + ')');
            
            var containerId = 'slmm-gpt-notification-container';
            var container = document.getElementById(containerId);
            
            if (!container) {
                container = document.createElement('div');
                container.id = containerId;
                container.style.cssText = 'position: fixed !important; top: 50% !important; left: 50% !important; transform: translate(-50%, -50%) !important; z-index: 9999999 !important; max-width: 400px !important; width: 90% !important; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important; pointer-events: none !important; visibility: hidden !important; opacity: 0 !important; transition: visibility 0.2s, opacity 0.2s !important;';
                document.body.appendChild(container);
            }

            // Create notification with dark theme
            var notification = document.createElement('div');
            notification.className = 'slmm-gpt-notification' + (type ? ' ' + type : '');
            
                        // Force dark theme styling for ALL contexts
            var bgColor = '#1a202c';
            var borderColor = '#4a5568';
            var textColor = '#ffffff';
            
            // Override colors for all notification types with dark theme
            if (type === 'success') {
                bgColor = '#1a202c';
                borderColor = '#2d3748';
                textColor = '#e2e8f0';
            } else if (type === 'error') {
                bgColor = '#1a1a1a';
                borderColor = '#4a5568';
                textColor = '#f7fafc';
            } else {
                // Default/info notifications - force dark
                bgColor = '#1a202c';
                borderColor = '#4a5568';
                textColor = '#ffffff';
            }
            
            notification.style.cssText = 'background: ' + bgColor + ' !important; color: ' + textColor + ' !important; border: 1px solid ' + borderColor + ' !important; font-size: 14px !important; display: flex !important; align-items: center !important; justify-content: center !important; text-align: center !important; min-height: 40px !important; padding: 12px 16px !important; border-radius: 8px !important; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important; margin: 0 !important; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif !important; font-weight: 500 !important;';
            notification.innerHTML = message;
            container.appendChild(notification);
            
            // Show the container
            container.style.visibility = 'visible';
            container.style.opacity = '1';
            
            // Hide after delay
            var delay = type === 'error' ? 4000 : 2000;
            setTimeout(function() {
                container.style.visibility = 'hidden';
                container.style.opacity = '0';
                setTimeout(function() {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 200);
            }, delay);
        }
        
        function injectBricksCSS() {
            debugLog('Bricks Builder', 'Injecting CSS for Bricks Builder');
            
            // Check if CSS is already injected
            if (document.getElementById('slmm-bricks-css')) {
                debugLog('Bricks Builder', 'CSS already injected, skipping');
                return;
            }
            
            var css = `
                /* SLMM Dark UI styles for Bricks Builder - JavaScript injection */
                .slmm-gpt-prompt-container {
                    display: flex !important;
                    align-items: center !important;
                    gap: 6px !important;
                    margin: 4px 0 !important;
                    position: relative !important;
                    max-width: 100% !important;
                }
                
                .slmm-gpt-prompt-dropdown,
                #slmm-gpt-prompt-dropdown,
                #slmm-gpt-prompt-dropdown-1 {
                    flex: 1 !important;
                    min-width: 180px !important;
                    max-width: 250px !important;
                    padding: 6px 10px !important;
                    border: 1px solid #4a5568 !important;
                    border-radius: 4px !important;
                    background: #2d3748 !important;
                    color: #ffffff !important;
                    font-size: 12px !important;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif !important;
                    transition: all 0.2s ease !important;
                    height: 32px !important;
                    line-height: 1.2 !important;
                }
                
                .slmm-gpt-prompt-dropdown:hover,
                #slmm-gpt-prompt-dropdown:hover {
                    border-color: #63b3ed !important;
                    background: #374151 !important;
                }
                
                .slmm-gpt-prompt-dropdown option,
                #slmm-gpt-prompt-dropdown option {
                    background: #2d3748 !important;
                    color: #ffffff !important;
                    padding: 4px !important;
                }
                
                .slmm-execute-gpt-prompt,
                #slmm-execute-gpt-prompt,
                #slmm-execute-gpt-prompt-1 {
                    background: #4a5568 !important;
                    color: #ffffff !important;
                    border: 1px solid #4a5568 !important;
                    border-radius: 4px !important;
                    padding: 6px 12px !important;
                    cursor: pointer !important;
                    font-size: 12px !important;
                    font-weight: 500 !important;
                    text-decoration: none !important;
                    display: inline-flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    transition: all 0.2s ease !important;
                    white-space: nowrap !important;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif !important;
                    height: 32px !important;
                    min-width: 100px !important;
                    flex-shrink: 0 !important;
                }
                
                .slmm-execute-gpt-prompt:hover,
                #slmm-execute-gpt-prompt:hover {
                    background: #63b3ed !important;
                    border-color: #63b3ed !important;
                    transform: translateY(-1px) !important;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
                }
            `;
            
            var style = document.createElement('style');
            style.type = 'text/css';
            style.id = 'slmm-bricks-css';
            style.innerHTML = css;
            document.head.appendChild(style);
            
            debugLog('Bricks Builder', 'CSS injected successfully');
        }
        </script>
        
        <style>
        /* SLMM Bricks Builder Dark UI Styles */
        
        /* GPT Prompt Container - Compact Single Row */
        .slmm-gpt-prompt-container {
            display: flex !important;
            align-items: center !important;
            gap: 6px !important;
            margin: 4px 0 !important;
            position: relative !important;
            max-width: 100% !important;
        }
        
        /* Dark Dropdown Styling - Optimized */
        .slmm-gpt-prompt-dropdown,
        #slmm-gpt-prompt-dropdown,
        #slmm-gpt-prompt-dropdown-1 {
            flex: 1 !important;
            min-width: 180px !important;
            max-width: 250px !important;
            padding: 6px 10px !important;
            border: 1px solid #4a5568 !important;
            border-radius: 4px !important;
            background: #2d3748 !important;
            color: #ffffff !important;
            font-size: 12px !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif !important;
            transition: all 0.2s ease !important;
            height: 32px !important;
            line-height: 1.2 !important;
        }
        
        .slmm-gpt-prompt-dropdown:hover,
        #slmm-gpt-prompt-dropdown:hover {
            border-color: #63b3ed !important;
            background: #374151 !important;
        }
        
        .slmm-gpt-prompt-dropdown:focus,
        #slmm-gpt-prompt-dropdown:focus {
            outline: none !important;
            border-color: #63b3ed !important;
            box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.1) !important;
        }
        
        /* Dark dropdown options */
        .slmm-gpt-prompt-dropdown option,
        #slmm-gpt-prompt-dropdown option {
            background: #2d3748 !important;
            color: #ffffff !important;
            padding: 4px !important;
        }
        
        /* Compact Dark Button Styling */
        .slmm-execute-gpt-prompt,
        #slmm-execute-gpt-prompt,
        #slmm-execute-gpt-prompt-1 {
            background: #4a5568 !important;
            color: #ffffff !important;
            border: 1px solid #4a5568 !important;
            border-radius: 4px !important;
            padding: 6px 12px !important;
            cursor: pointer !important;
            font-size: 12px !important;
            font-weight: 500 !important;
            text-decoration: none !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: all 0.2s ease !important;
            white-space: nowrap !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif !important;
            height: 32px !important;
            min-width: 100px !important;
            flex-shrink: 0 !important;
        }
        
        .slmm-execute-gpt-prompt:hover,
        #slmm-execute-gpt-prompt:hover {
            background: #63b3ed !important;
            border-color: #63b3ed !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        }
        
        .slmm-execute-gpt-prompt:active,
        #slmm-execute-gpt-prompt:active {
            transform: translateY(0) !important;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1) !important;
        }
        
        .slmm-execute-gpt-prompt:disabled,
        #slmm-execute-gpt-prompt:disabled {
            background: #2d3748 !important;
            border-color: #2d3748 !important;
            color: #a0aec0 !important;
            cursor: not-allowed !important;
            transform: none !important;
            box-shadow: none !important;
        }
        
        /* Notification Container - Bricks Dark Theme */
        #slmm-gpt-notification-container {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            z-index: 9999999 !important;
            max-width: 400px !important;
            width: 90% !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
            pointer-events: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            transition: visibility 0.2s, opacity 0.2s !important;
        }
        
        #slmm-gpt-notification-container.active {
            visibility: visible !important;
            opacity: 1 !important;
        }
        
        .slmm-gpt-notification {
            background: #1a202c !important;
            color: #ffffff !important;
            border: 1px solid #4a5568 !important;
            font-size: 14px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            text-align: center !important;
            min-height: 40px !important;
            padding: 12px 16px !important;
            border-radius: 8px !important;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
            margin: 0 !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif !important;
            font-weight: 500 !important;
        }
        
        .slmm-gpt-notification.success {
            background: #1a202c !important;
            border-color: #2d3748 !important;
            color: #e2e8f0 !important;
        }
        
        .slmm-gpt-notification.error {
            background: #1a1a1a !important;
            border-color: #4a5568 !important;
            color: #f7fafc !important;
        }
        
        /* Force dark notifications in ALL contexts */
        body .slmm-gpt-notification,
        .wp-admin .slmm-gpt-notification,
        .bricks-builder .slmm-gpt-notification,
        [data-theme*="bricks"] .slmm-gpt-notification {
            background: #1a202c !important;
            color: #ffffff !important;
            border: 1px solid #4a5568 !important;
        }
        
        body .slmm-gpt-notification.success,
        .wp-admin .slmm-gpt-notification.success,
        .bricks-builder .slmm-gpt-notification.success,
        [data-theme*="bricks"] .slmm-gpt-notification.success {
            background: #1a202c !important;
            border-color: #2d3748 !important;
            color: #e2e8f0 !important;
        }
        
        body .slmm-gpt-notification.error,
        .wp-admin .slmm-gpt-notification.error,
        .bricks-builder .slmm-gpt-notification.error,
        [data-theme*="bricks"] .slmm-gpt-notification.error {
            background: #1a1a1a !important;
            border-color: #4a5568 !important;
            color: #f7fafc !important;
        }
        
        /* Hide tooltip instructions and container in Bricks Builder only */
        body[class*="bricks"] .slmm-tooltip,
        body[class*="bricks"] .slmm-tooltip-text,
        .bricks-builder .slmm-tooltip,
        .bricks-builder .slmm-tooltip-text,
        [data-theme*="bricks"] .slmm-tooltip,
        [data-theme*="bricks"] .slmm-tooltip-text {
            display: none !important;
        }
        
        /* Ensure dark styling works in all Bricks contexts */
        body[class*="bricks"] .slmm-gpt-prompt-container,
        .bricks-builder .slmm-gpt-prompt-container,
        [data-theme*="bricks"] .slmm-gpt-prompt-container {
            background: transparent !important;
        }
        </style>
        <?php
    }
    
    /**
     * Handle Bricks setup action
     */
    public function on_bricks_setup() {
        error_log('SLMM DEBUG [Bricks Builder]: Setup action triggered');
    }
    
    /**
     * Handle Bricks render action
     */
    public function on_bricks_render($element) {
        // Could be used for element-specific functionality
    }
}

// Initialize the Bricks integration
new SLMM_Bricks_Integration(); 