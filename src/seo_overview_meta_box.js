/**
 * SEO Overview Meta Box
 * 
 * Provides a comprehensive overview of SEO metrics in a meta box
 * that can be toggled from Screen Options and repositioned.
 */
(function($) {
    'use strict';
    
    // SLMM debug system with fallback
    function debugLog(message, data) {
        if (window.SLMM && window.SLMM.debug) {
            window.SLMM.debug.log('SEO Overview', message, data);
        } else {
            console.log('[SEO Overview] ' + message, data || '');
        }
    }
    
    function debugError(message, data) {
        if (window.SLMM && window.SLMM.debug) {
            window.SLMM.debug.error('SEO Overview', message, data);
        } else {
            console.error('[SEO Overview] ' + message, data || '');
        }
    }
    
    function debugWarn(message, data) {
        if (window.SLMM && window.SLMM.debug) {
            window.SLMM.debug.warn('SEO Overview', message, data);
        } else {
            console.warn('[SEO Overview] ' + message, data || '');
        }
    }

    var SEOOverviewMetaBox = {
        init: function() {
            // Initialize listeners after the page has loaded
            $(document).ready(function() {
                SEOOverviewMetaBox.setupEventListeners();
                
                // Wait for the page to be fully loaded, including all scripts
                $(window).on('load', function() {
                    // Allow a short delay for all indicator plugins to initialize
                    setTimeout(function() {
                        debugLog('Performing initial update after full page load');
                        SEOOverviewMetaBox.updateMetaBoxContent();
                    }, 800);
                });
                
                // Also set up a one-time update after a short delay to catch any late-initializing elements
                setTimeout(function() {
                    debugLog('Performing fallback initial update');
                    SEOOverviewMetaBox.updateMetaBoxContent();
                }, 1500);
            });
        },

        setupEventListeners: function() {
            // Update the meta box when WordPress saves content (heartbeat)
            $(document).on('heartbeat-tick', function() {
                SEOOverviewMetaBox.updateMetaBoxContent();
            });

            // Real-time updates when tinymce content changes
            if (typeof tinymce !== 'undefined') {
                tinymce.on('AddEditor', function(e) {
                    e.editor.on('change keyup', SEOOverviewMetaBox.debounce(function() {
                        SEOOverviewMetaBox.updateMetaBoxContent();
                    }, 1000));
                    
                    // Check for schema in editor content changes
                    e.editor.on('change keyup', SEOOverviewMetaBox.debounce(function() {
                        var content = e.editor.getContent();
                        var hasSchema = content.includes('application/ld+json') || 
                                       content.includes('itemtype="http://schema.org') ||
                                       content.includes('itemtype="https://schema.org');
                        
                        // Only update if schema is present in the content
                        if (hasSchema) {
                            var schemaStatus = SEOOverviewMetaBox.getSchemaStatus();
                            $('.seo-overview-table tr:contains("Schema:")').find('td').html(schemaStatus);
                        }
                    }, 1000));
                    
                    // Also update once when editor is fully initialized
                    e.editor.on('init', function() {
                        setTimeout(function() {
                            debugLog('Update triggered by TinyMCE init');
                            SEOOverviewMetaBox.updateMetaBoxContent();
                        }, 500);
                    });
                });
            }

            // Monitor for when key indicators are ready
            var checkForIndicators = function() {
                var areReady = false;
                
                // Check if links indicator is ready
                var linksButton = $('#broken-links-button, .mce-i-link').first();
                if (linksButton.length && linksButton.find('.broken-links-indicator').length) {
                    areReady = true;
                }
                
                // Check if duplicates indicator is ready
                var dupButton = $('#content-duplication');
                if (dupButton.length && dupButton.find('.duplication-indicator').length) {
                    areReady = true;
                }
                
                // Check if hidden div indicator is ready
                var hiddenDiv = $('.hidden-div-indicator');
                if (hiddenDiv.length) {
                    areReady = true;
                }
                
                // If indicators are ready, update the SEO overview
                if (areReady) {
                    debugLog('Key indicators detected, triggering update');
                    SEOOverviewMetaBox.updateMetaBoxContent();
                    clearInterval(indicatorCheckInterval);
                }
            };
            
            // Check every 200ms for indicators, up to 5 seconds
            var indicatorCheckInterval = setInterval(checkForIndicators, 200);
            setTimeout(function() {
                clearInterval(indicatorCheckInterval);
            }, 5000);

            // Update when publish button is clicked
            $('#publish').on('click', function() {
                setTimeout(function() {
                    SEOOverviewMetaBox.updateMetaBoxContent();
                }, 1000);
            });
            
            // Update on editor content change from text mode
            $('#content').on('input change', SEOOverviewMetaBox.debounce(function() {
                SEOOverviewMetaBox.updateMetaBoxContent();
            }, 1000));
            
            // Update on featured image change
            $('#postimagediv').on('DOMSubtreeModified', SEOOverviewMetaBox.debounce(function() {
                // Update just the featured image section
                var featuredImageStatus = SEOOverviewMetaBox.getFeaturedImageStatus();
                $('.seo-overview-table tr:contains("Featured Image:")').find('td').html(featuredImageStatus);
            }, 500));
            
            // Monitor the "Remove featured image" link click
            $(document).on('click', '#remove-post-thumbnail', function() {
                setTimeout(function() {
                    var featuredImageStatus = SEOOverviewMetaBox.getFeaturedImageStatus();
                    $('.seo-overview-table tr:contains("Featured Image:")').find('td').html(featuredImageStatus);
                }, 100);
            });
            
            // Monitor the "Set featured image" link click
            $(document).on('click', '#set-post-thumbnail', function() {
                // Need a longer timeout as the media uploader takes time to close
                var checkInterval = setInterval(function() {
                    if ($('#postimagediv .inside img').length > 0) {
                        var featuredImageStatus = SEOOverviewMetaBox.getFeaturedImageStatus();
                        $('.seo-overview-table tr:contains("Featured Image:")').find('td').html(featuredImageStatus);
                        clearInterval(checkInterval);
                    }
                }, 500);
                
                // Clear interval after 10 seconds in case user cancels
                setTimeout(function() {
                    clearInterval(checkInterval);
                }, 10000);
            });
            
            // Monitor the schema textarea changes
            $(document).on('input change', '#slmm_insert_schema', function() {
                // Update schema status when the schema textarea changes
                var schemaStatus = SEOOverviewMetaBox.getSchemaStatus();
                $('.seo-overview-table tr:contains("Schema:")').find('td').html(schemaStatus);
            });
            
            // Monitor schema validation and rescan buttons
            $(document).on('click', '#validate-schema, #rescan-schema', function() {
                // Update after a short delay to allow for validation/rescan
                setTimeout(function() {
                    var schemaStatus = SEOOverviewMetaBox.getSchemaStatus();
                    $('.seo-overview-table tr:contains("Schema:")').find('td').html(schemaStatus);
                }, 500);
            });
            
            // Directly monitor the WordPress word count element
            $('#wp-word-count .word-count').on('DOMSubtreeModified', function() {
                // Immediately update when word count changes
                var wordCount = $(this).text();
                $('.seo-overview-table .value-highlight').text(wordCount);
            });
            
            // Use MutationObserver to monitor the duplicate content indicator
            if (window.MutationObserver) {
                // Observe the duplication indicator in ContentDuplication.js
                var duplicateButton = document.querySelector('#content-duplication');
                if (duplicateButton) {
                    var observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'childList' || mutation.type === 'attributes') {
                                // Update just the duplicates status without reloading everything
                                var duplicatesStatus = SEOOverviewMetaBox.getDuplicatesStatus();
                                $('.seo-overview-table tr:contains("Duplicates:")').find('td').html(duplicatesStatus);
                            }
                        });
                    });
                    
                    observer.observe(duplicateButton, { 
                        childList: true,
                        attributes: true,
                        subtree: true
                    });
                }
                
                // Observe the featured image indicator in the preflight checklist
                var featuredImageIndicator = document.querySelector('.featured-image-indicator');
                if (featuredImageIndicator) {
                    var featuredObserver = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                                // Update just the featured image status without reloading everything
                                var featuredImageStatus = SEOOverviewMetaBox.getFeaturedImageStatus();
                                $('.seo-overview-table tr:contains("Featured Image:")').find('td').html(featuredImageStatus);
                            }
                        });
                    });
                    
                    featuredObserver.observe(featuredImageIndicator, { 
                        attributes: true,
                        attributeFilter: ['style', 'class']
                    });
                }
                
                // Observe the schema indicator in the preflight checklist
                var schemaIndicator = document.querySelector('.schema-indicator');
                if (schemaIndicator) {
                    var schemaObserver = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                                // Update just the schema status without reloading everything
                                var schemaStatus = SEOOverviewMetaBox.getSchemaStatus();
                                $('.seo-overview-table tr:contains("Schema:")').find('td').html(schemaStatus);
                                debugLog('Schema status updated via mutation observer');
                            }
                        });
                    });
                    
                    schemaObserver.observe(schemaIndicator, { 
                        attributes: true,
                        attributeFilter: ['style', 'class']
                    });
                }
            }
            
            // Use MutationObserver to ensure we catch all word count updates
            if (window.MutationObserver) {
                var wordCountSpan = document.querySelector('#wp-word-count .word-count');
                if (wordCountSpan) {
                    var observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'characterData' || mutation.type === 'childList') {
                                var wordCount = $(wordCountSpan).text();
                                $('.seo-overview-table .value-highlight').text(wordCount);
                            }
                        });
                    });
                    
                    observer.observe(wordCountSpan, { 
                        characterData: true,
                        childList: true,
                        subtree: true
                    });
                }
            }
            
            // Set up SILO STRUCTURE click handlers for two-way sync
            $(document).on('click', '.clickable-importance', function(e) {
                e.preventDefault();
                e.stopPropagation();
                var importance = $(this).data('importance');
                SEOOverviewMetaBox.updateImportance(importance);
            });
            
            $(document).on('click', '.clickable-difficulty', function(e) {
                e.preventDefault();
                e.stopPropagation();
                var difficulty = $(this).data('difficulty');
                SEOOverviewMetaBox.updateDifficulty(difficulty);
            });
            
            $(document).on('click', '.clickable-keyword', function() {
                var currentKeyword = $(this).data('keyword');
                SEOOverviewMetaBox.editKeyword(currentKeyword);
            });
            
            // Set up click-to-copy functionality for navigation URLs
            $(document).on('click', '.clickable-url', function(e) {
                e.preventDefault();
                e.stopPropagation();
                var url = $(this).data('url');
                SEOOverviewMetaBox.copyToClipboard(url, $(this));
            });
            
            // Set up click-to-copy functionality for all sibling links
            $(document).on('click', '.seo-copy-siblings-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                SEOOverviewMetaBox.copySiblingLinks($(this));
            });
            
            // Set up click-to-copy functionality for all child links
            $(document).on('click', '.seo-copy-children-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                SEOOverviewMetaBox.copyChildLinks($(this));
            });
            
            // Set up interlinking suite button functionality
            $(document).on('click', '.seo-interlinking-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                SEOOverviewMetaBox.openInterlinkingSuite();
            });
            
            // Set up priority accordion click handlers
            $(document).on('click', '.priority-accordion-header', function(e) {
                e.preventDefault();
                e.stopPropagation();
                var $this = $(this);
                var priorityLevel = $this.data('priority');
                var $content = $this.next('.priority-accordion-content');
                
                // Close all other accordions first
                $('.priority-accordion-header.expanded').each(function() {
                    if (this !== $this[0]) {
                        $(this).removeClass('expanded');
                        $(this).next('.priority-accordion-content').removeClass('expanded').slideUp(300);
                    }
                });
                
                // Toggle the clicked accordion
                if ($content.hasClass('expanded')) {
                    // Collapse this accordion
                    $content.removeClass('expanded').slideUp(300);
                    $this.removeClass('expanded');
                } else {
                    // Expand this accordion and load data if needed
                    $this.addClass('expanded');
                    $content.addClass('expanded').slideDown(300);
                    
                    // Load priority pages if not already loaded
                    if (!$content.hasClass('loaded')) {
                        SEOOverviewMetaBox.loadPriorityPages(priorityLevel, $content);
                    }
                }
            });
            
            // Set up a periodic refresh
            setInterval(function() {
                SEOOverviewMetaBox.updateMetaBoxContent();
            }, 5000); // Update every 5 seconds
        },

        updateMetaBoxContent: function() {
            // Check if priority accordions exist and preserve their state
            var priorityAccordionStates = {};
            $('.priority-accordion-header').each(function() {
                var priority = $(this).data('priority');
                var isExpanded = $(this).hasClass('expanded');
                var content = $(this).next('.priority-accordion-content');
                var isLoaded = content.hasClass('loaded');
                var contentHtml = content.html();
                
                priorityAccordionStates[priority] = {
                    expanded: isExpanded,
                    loaded: isLoaded,
                    content: contentHtml
                };
            });
            
            // Get the values from various sources
            var wordCount = SEOOverviewMetaBox.getWordCount();
            var publishStatus = SEOOverviewMetaBox.getPublishStatus();
            var featuredImageStatus = SEOOverviewMetaBox.getFeaturedImageStatus();
            var duplicatesStatus = SEOOverviewMetaBox.getDuplicatesStatus();
            var linksStatus = SEOOverviewMetaBox.getLinksStatus();
            var hiddenDivStatus = SEOOverviewMetaBox.getHiddenDivStatus();
            var schemaStatus = SEOOverviewMetaBox.getSchemaStatus();
            var siloStructureStatus = SEOOverviewMetaBox.getSiloStructureStatus();
            var navigationStatus = SEOOverviewMetaBox.getNavigationStatus();

            // Check if checklist is enabled (from localized script)
            var checklistEnabled = typeof slmmSeoOverview !== 'undefined' && slmmSeoOverview.checklistEnabled;

            // Build the SEO elements table rows
            var seoElementsRows =
                '<tr><th class="heading-larger">Featured Image:</th><td>' + featuredImageStatus + '</td></tr>' +
                '<tr><th class="heading-larger">Links Status:</th><td>' + linksStatus + '</td></tr>';
            if (checklistEnabled) {
                seoElementsRows += '<tr><th class="heading-larger">Schema:</th><td>' + schemaStatus + '</td></tr>';
            }
            seoElementsRows += '<tr><th class="heading-larger"></th><td>' + publishStatus + '</td></tr>';

            // Update the meta box content with a more compact layout
            $('#seo-overview-content').html(
                '<style>#slmm-seo-overview .seo-overview-table th.heading-larger { width: 95px !important; } ' +
                '#slmm-seo-overview .seo-overview-table td { text-align: left !important; } ' +
                '#slmm-seo-overview .nav-link { margin-right: 4px; margin-bottom: 2px; display: inline-block; }</style>' +
                // SILO STRUCTURE section - NEW (above CONTENT)
                '<div class="seo-overview-section">' +
                    '<h2 class="section-title"><strong>SILO STRUCTURE</strong>' +
                    SEOOverviewMetaBox.getInterlinkingButtonHtml() + 
                    '</h2>' +
                    '<table class="seo-overview-table">' +
                        siloStructureStatus +
                    '</table>' +
                '</div>' +
                // NAVIGATION section - NEW (Parents and Siblings)
                '<div class="seo-overview-section">' +
                    '<h2 class="section-title"><strong>NAVIGATION</strong></h2>' +
                    '<table class="seo-overview-table">' +
                        navigationStatus +
                    '</table>' +
                '</div>' +
                // PRIORITY PAGES section - NEW (Accordion-based priority pages)
                '<div class="seo-overview-section">' +
                    '<h2 class="section-title"><strong>PRIORITY PAGES</strong></h2>' +
                    '<div class="priority-accordion">' +
                        SEOOverviewMetaBox.getPriorityAccordions() +
                    '</div>' +
                '</div>' +
                // Content metrics section - more compact
                '<div class="seo-overview-section">' +
                    '<h2 class="section-title"><strong>CONTENT</strong></h2>' +
                    '<table class="seo-overview-table">' +
                        '<tr><th class="heading-larger">Word Count:</th><td><span class="value-highlight">' + wordCount + '</span></td></tr>' +
                        '<tr><th class="heading-larger">Duplicates:</th><td>' + duplicatesStatus + '</td></tr>' +
                        '<tr><th class="heading-larger">Hidden Divs:</th><td>' + hiddenDivStatus + '</td></tr>' +
                    '</table>' +
                '</div>' +
                // SEO elements section - more compact
                '<div class="seo-overview-section">' +
                    '<h2 class="section-title"><strong>SEO ELEMENTS</strong></h2>' +
                    '<table class="seo-overview-table">' +
                        seoElementsRows +
                    '</table>' +
                '</div>'
            );
            
            // Restore accordion states after rebuilding HTML
            SEOOverviewMetaBox.restorePriorityAccordionStates(priorityAccordionStates);
            
            // PERSISTENCE: Restore semantic indicators after HTML regeneration
            // This prevents the 5-second refresh cycle from clearing green backgrounds
            SEOOverviewMetaBox.restoreSemanticIndicators();
        },

        getWordCount: function() {
            // Get word count directly from the WordPress word count element's span
            var wordCountSpan = $('#wp-word-count .word-count');
            if (wordCountSpan.length) {
                return wordCountSpan.text();
            }
            return '0';
        },

        getPublishStatus: function() {
            // Get publish status from WordPress post_status
            var status = $('#post_status').val() || $('#original_post_status').val() || 'draft';
            var statusText, statusClass;
            
            // Also check the visible status text which might be more accurate
            var visibleStatus = $('#post-status-display').text() || $('.misc-pub-post-status #post-status-display').text() || $('label[for="post_status"]').text() || $('.misc-pub-section').text() || '';
            
            // Check specifically for "Privately Published" text
            if (visibleStatus.toLowerCase().indexOf('privately published') !== -1 || 
                visibleStatus.toLowerCase().indexOf('private') !== -1) {
                status = 'private';
            }
            
            // Add another check for status in the publish panel
            var publishPanelStatus = $('.edit-post-post-status__row:contains("Status")').text() || 
                                    $('span:contains("Status:")').text() || 
                                    $('.components-panel:contains("Status")').text() || '';
            if (publishPanelStatus.toLowerCase().indexOf('private') !== -1) {
                status = 'private';
            }

            switch (status) {
                case 'publish':
                    statusText = 'Published';
                    statusClass = 'status-green';
                    break;
                case 'private':
                    statusText = 'Private';
                    statusClass = 'status-orange';
                    break;
                default:
                    statusText = 'Draft';
                    statusClass = 'status-red';
                    break;
            }

            // All statuses use the same larger size for consistency
            return '<span class="publish-status ' + statusClass + ' large-text published-larger">' + statusText + '</span>';
        },

        getFeaturedImageStatus: function() {
            // First check for featured image indicator from the preflight checklist
            var preflightIndicator = $('.featured-image-indicator');
            if (preflightIndicator.length) {
                // Get color from the preflight checklist indicator
                var bgColor = preflightIndicator.css('background-color');
                if (bgColor && bgColor !== 'transparent' && bgColor !== 'rgba(0, 0, 0, 0)') {
                    // Green indicator means featured image exists
                    var isValid = (bgColor === 'green' || bgColor === 'rgb(0, 128, 0)' || 
                                  bgColor.indexOf('0, 128, 0') > -1 || bgColor.indexOf('rgb(0, 163, 42)') > -1 || 
                                  bgColor.indexOf('0, 163, 42') > -1 || bgColor.indexOf('#00a32a') > -1);
                    
                    return SEOOverviewMetaBox.getStatusIndicator(isValid);
                }
            }
            
            // Fallback method: Check if featured image exists directly
            var hasFeaturedImage = $('#postimagediv .inside img').length > 0;
            
            // If using the featured image indicator from Content Checklist, get color from there
            var checklistIndicator = $('.featured-image-indicator');
            if (checklistIndicator.length) {
                var bgColor = checklistIndicator.css('background-color');
                if (bgColor && bgColor !== 'transparent' && bgColor !== 'rgba(0, 0, 0, 0)') {
                    // Get the status from Content Checklist's indicator
                    var isValid = (bgColor.indexOf('0, 163, 42') > -1 || bgColor.indexOf('0,163,42') > -1 || 
                                  bgColor.indexOf('#00a32a') > -1 || bgColor.indexOf('rgb(0, 163, 42)') > -1);
                    
                    // Use the Content Checklist value
                    return SEOOverviewMetaBox.getStatusIndicator(isValid);
                }
            }
            
            // Use our own check as a fallback
            return SEOOverviewMetaBox.getStatusIndicator(hasFeaturedImage);
        },

        getDuplicatesStatus: function() {
            // Directly check the duplicate content indicator from ContentDuplication.js
            var duplicateButton = $('#content-duplication');
            var duplicateIndicator = duplicateButton.find('.duplication-indicator');
            var duplicateCount = duplicateButton.find('.duplication-count').text();
            
            // Check if the indicator is active (has duplicates)
            var hasDuplicates = false;
            if (duplicateIndicator.length) {
                hasDuplicates = duplicateIndicator.hasClass('active');
                
                // Extract count number from the 'x #' format if present
                if (duplicateCount) {
                    var countMatch = duplicateCount.match(/x\s*(\d+)/);
                    if (countMatch && countMatch[1]) {
                        duplicateCount = countMatch[1];
                    }
                }
                
                // Indicator is inverse - GREEN means NO duplicates
                return SEOOverviewMetaBox.getStatusIndicator(!hasDuplicates, hasDuplicates ? duplicateCount : '');
            }
            
            // Fallback to previous behavior if no indicator found
            var duplicateElements = $('.duplicate-content-indicator, .duplicate-indicator');
            if (duplicateElements.length) {
                // Get the first element with a background color set
                duplicateElements.each(function() {
                    var bgColor = $(this).css('background-color');
                    if (bgColor && bgColor !== 'transparent' && bgColor !== 'rgba(0, 0, 0, 0)') {
                        // Red indicator means duplicates found
                        if (bgColor.indexOf('214, 54, 56') > -1 || bgColor.indexOf('rgb(214, 54, 56)') > -1 || 
                            bgColor.indexOf('#d63638') > -1 || bgColor.indexOf('red') > -1) {
                            hasDuplicates = true;
                            return false; // Exit the loop after finding a duplicate
                        }
                    }
                });
            } else if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                // Fallback: Check for duplicate paragraphs in content
                var content = tinymce.activeEditor.getContent();
                
                // Extract all paragraphs
                var tempDiv = document.createElement('div');
                tempDiv.innerHTML = content;
                
                var paragraphs = [];
                var p = tempDiv.getElementsByTagName('p');
                for (var i = 0; i < p.length; i++) {
                    var text = p[i].textContent.trim();
                    if (text.length > 20) { // Only consider substantial paragraphs
                        paragraphs.push(text);
                    }
                }
                
                // Check for duplicate paragraphs
                var duplicates = {};
                paragraphs.forEach(function(para) {
                    if (duplicates[para]) {
                        duplicates[para]++;
                    } else {
                        duplicates[para] = 1;
                    }
                });
                
                var dupCount = 0;
                for (var key in duplicates) {
                    if (duplicates[key] > 1) {
                        dupCount += duplicates[key] - 1; // Count only extra occurrences
                    }
                }
                
                hasDuplicates = dupCount > 0;
                duplicateCount = dupCount > 0 ? dupCount : '';
            }
            
            // GREEN means no duplicates, RED means duplicates
            return SEOOverviewMetaBox.getStatusIndicator(!hasDuplicates, hasDuplicates ? duplicateCount : '');
        },

        getLinksStatus: function() {
            // Get links status from BrokenLinkDetector
            var brokenLinksIndicator = $('.broken-links-indicator');
            var isValid = brokenLinksIndicator.hasClass('valid');
            var count = '';
            
            // Try to get count from the broken-links-count element
            var countEl = $('.broken-links-count');
            if (countEl.length && countEl.text().trim() !== '') {
                // Extract just the number if it already has an 'x' prefix
                var countText = countEl.text().trim();
                var countMatch = countText.match(/x\s*(\d+)/);
                if (countMatch && countMatch[1]) {
                    count = countMatch[1];
                } else {
                    count = countText;
                }
            }
            
            return SEOOverviewMetaBox.getStatusIndicator(isValid, count);
        },

        getHiddenDivStatus: function() {
            // Get hidden div count based on the server-side count_hidden_divs function
            var hiddenDivCount = 0;
            
            // First try to directly read the value from the hidden div button in the toolbar
            // This ensures we mirror exactly what's shown in the editor toolbar
            var hiddenDivIndicator = $('.hidden-div-indicator');
            var hiddenDivCount = $('.hidden-div-count');
            
            if (hiddenDivIndicator.length) {
                // Check the background color style
                var bgColor = hiddenDivIndicator.css('background-color');
                var bgStyle = hiddenDivIndicator.attr('style');
                
                // Default to no hidden divs
                var hasHiddenDivs = false;
                var displayCount = '';
                
                // Check if the indicator has a green background (has hidden divs)
                if (bgColor && (bgColor === 'green' || bgColor === 'rgb(0, 128, 0)' || bgColor.indexOf('0, 128, 0') > -1)) {
                    hasHiddenDivs = true;
                    
                    // Get count from the counter element if it exists and has text
                    if (hiddenDivCount.length && hiddenDivCount.text().trim() !== '') {
                        var countMatch = hiddenDivCount.text().match(/x\s*(\d+)/);
                        if (countMatch && countMatch[1]) {
                            displayCount = countMatch[1];
                        } else {
                            displayCount = hiddenDivCount.text().trim();
                        }
                    }
                }
                
                return SEOOverviewMetaBox.getStatusIndicator(hasHiddenDivs, displayCount);
            }
            
            // If we can't find the indicator element, fall back to content scanning
            if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                var content = tinymce.activeEditor.getContent();
                
                // Check for hidden divs in the content
                var regex = /style\s*=\s*["'][^"']*display\s*:\s*none[^"']*["']/gi;
                var matches = content.match(regex);
                hiddenDivCount = matches ? matches.length : 0;
                
                // IMPORTANT: In seo_text_helper_2_3.php, hidden div indicator is GREEN when divs exist
                // So we need to invert our logic - hasHiddenDivs should be true when count > 0
                var hasHiddenDivs = hiddenDivCount > 0;
                var displayCount = hiddenDivCount > 1 ? hiddenDivCount : '';
                
                return SEOOverviewMetaBox.getStatusIndicator(hasHiddenDivs, displayCount);
            }
            
            // Default to no hidden divs if we can't determine
            return SEOOverviewMetaBox.getStatusIndicator(false, '');
        },

        getSchemaStatus: function() {
            // Check for schema markup from preflight checklist/Content Checklist
            var schemaIndicator = $('.schema-indicator');
            if (schemaIndicator.length) {
                var bgColor = schemaIndicator.css('background-color');
                if (bgColor && bgColor !== 'transparent' && bgColor !== 'rgba(0, 0, 0, 0)') {
                    // Get the status from schema indicator
                    var isValid = (bgColor.indexOf('0, 163, 42') > -1 || bgColor.indexOf('0,163,42') > -1 || 
                                  bgColor.indexOf('#00a32a') > -1 || bgColor.indexOf('rgb(0, 163, 42)') > -1 ||
                                  bgColor === 'green' || bgColor === 'rgb(0, 128, 0)' || bgColor.indexOf('0, 128, 0') > -1);
                    var isWarning = (bgColor.indexOf('245, 110, 40') > -1 || bgColor.indexOf('245,110,40') > -1 || 
                                    bgColor.indexOf('#f56e28') > -1 || bgColor.indexOf('orange') > -1 ||
                                    bgColor === 'orange' || bgColor === 'rgb(255, 165, 0)' || bgColor.indexOf('255, 165, 0') > -1);
                    
                    // Return status with right color for schema - use large-indicator class
                    if (isValid) {
                        return '<span class="status-indicator status-green large-indicator">•</span>';
                    } else if (isWarning) {
                        return '<span class="status-indicator status-orange large-indicator">•</span>';
                    } else {
                        return '<span class="status-indicator status-red large-indicator">•</span>';
                    }
                }
            }
            
            // Fallback: Check the insert schema metabox
            var schemaTextarea = $('#slmm_insert_schema');
            if (schemaTextarea.length && schemaTextarea.val().trim() !== '') {
                return '<span class="status-indicator status-green large-indicator">•</span>';
            }
            
            // Fallback: Simple detection of schema in content
            var hasSchema = false;
            
            // Look for schema in TinyMCE
            if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                var content = tinymce.activeEditor.getContent();
                hasSchema = content.includes('application/ld+json') || 
                           content.includes('itemtype="http://schema.org') ||
                           content.includes('itemtype="https://schema.org');
            }
            
            return SEOOverviewMetaBox.getStatusIndicator(hasSchema);
        },

        getSiloStructureStatus: function() {
            // Get current post ID from global data or form
            var postId = (typeof slmmGptPromptData !== 'undefined' && slmmGptPromptData.current_post_id) ? 
                        slmmGptPromptData.current_post_id : 
                        $('#post_ID').val() || $('#post-id').val() || 0;
            
            if (!postId || postId === 0) {
                // No post ID available, show default state
                return '<tr><th class="heading-larger">Keyword:</th><td>' + SEOOverviewMetaBox.getKeywordDisplay('') + '</td></tr>' +
                       '<tr><th class="heading-larger">Importance:</th><td>' + SEOOverviewMetaBox.getImportanceCircles('3') + '</td></tr>' +
                       '<tr><th class="heading-larger">Difficulty:</th><td>' + SEOOverviewMetaBox.getDifficultyCircles('medium') + '</td></tr>';
            }
            
            // Check if we have cached data to avoid repeated AJAX calls (energy conservative)
            var cacheKey = 'silo_structure_' + postId;
            var cached = SEOOverviewMetaBox.cache && SEOOverviewMetaBox.cache[cacheKey];
            
            if (cached && (Date.now() - cached.timestamp < 30000)) { // Cache for 30 seconds
                return '<tr><th class="heading-larger">Keyword:</th><td>' + SEOOverviewMetaBox.getKeywordDisplay(cached.keyword) + '</td></tr>' +
                       '<tr><th class="heading-larger">Importance:</th><td>' + SEOOverviewMetaBox.getImportanceCircles(cached.importance) + '</td></tr>' +
                       '<tr><th class="heading-larger">Difficulty:</th><td>' + SEOOverviewMetaBox.getDifficultyCircles(cached.difficulty) + '</td></tr>';
            }
            
            // Try to get values from WordPress block editor data first (fastest method)
            var importance = '3'; // Default value
            var difficulty = 'medium'; // Default value
            var keyword = ''; // Default value
            
            if (typeof wp !== 'undefined' && wp.data && wp.data.select) {
                try {
                    var currentPost = wp.data.select('core/editor').getCurrentPost();
                    if (currentPost && currentPost.meta) {
                        importance = currentPost.meta._slmm_importance_rating || '3';
                        difficulty = currentPost.meta._slmm_difficulty_level || 'medium';
                        keyword = currentPost.meta._slmm_target_keyword || '';
                        
                        // Cache the values
                        SEOOverviewMetaBox.cache = SEOOverviewMetaBox.cache || {};
                        SEOOverviewMetaBox.cache[cacheKey] = {
                            importance: importance,
                            difficulty: difficulty,
                            keyword: keyword,
                            timestamp: Date.now()
                        };
                    }
                } catch(e) {
                    // Gutenberg not available, continue to fallback methods
                }
            }
            
            // If Gutenberg data not available, try to get from PHP-rendered meta fields (fallback)
            if (importance === '3' && difficulty === 'medium' && keyword === '') {
                // Check if there are hidden meta fields we can read from
                var importanceMeta = $('input[name="_slmm_importance_rating"]').val();
                var difficultyMeta = $('input[name="_slmm_difficulty_level"]').val();
                var keywordMeta = $('input[name="_slmm_target_keyword"]').val();
                
                if (importanceMeta) importance = importanceMeta;
                if (difficultyMeta) difficulty = difficultyMeta;
                if (keywordMeta) keyword = keywordMeta;
                
                // Cache these values too
                if (importanceMeta || difficultyMeta || keywordMeta) {
                    SEOOverviewMetaBox.cache = SEOOverviewMetaBox.cache || {};
                    SEOOverviewMetaBox.cache[cacheKey] = {
                        importance: importance,
                        difficulty: difficulty,
                        keyword: keyword,
                        timestamp: Date.now()
                    };
                }
            }
            
            return '<tr><th class="heading-larger">Keyword:</th><td>' + SEOOverviewMetaBox.getKeywordDisplay(keyword) + '</td></tr>' +
                   '<tr><th class="heading-larger">Importance:</th><td>' + SEOOverviewMetaBox.getImportanceCircles(importance) + '</td></tr>' +
                   '<tr><th class="heading-larger">Difficulty:</th><td>' + SEOOverviewMetaBox.getDifficultyCircles(difficulty) + '</td></tr>';
        },

        getImportanceCircles: function(currentImportance) {
            var html = '<div class="silo-importance-circles">';
            var currentLevel = parseInt(currentImportance) || 3;
            
            for (var i = 1; i <= 5; i++) {
                // Only the exact current level is active, all others are inactive
                var activeClass = (i === currentLevel) ? ' active' : ' inactive';
                var colorClass = 'importance-' + i;
                html += '<span class="silo-circle importance-circle ' + colorClass + activeClass + ' clickable-importance" data-importance="' + i + '">' + i + '</span>';
            }
            
            html += '</div>';
            return html;
        },

        getDifficultyCircles: function(currentDifficulty) {
            var html = '<div class="silo-difficulty-circles">';
            var difficulties = [
                {key: 'easy', label: 'E', color: 'difficulty-easy'},
                {key: 'medium', label: 'M', color: 'difficulty-medium'},
                {key: 'hard', label: 'H', color: 'difficulty-hard'},
                {key: 'very-hard', label: 'VH', color: 'difficulty-very-hard'}
            ];
            
            difficulties.forEach(function(diff) {
                // Only the exact current difficulty is active, all others are inactive
                var activeClass = (diff.key === currentDifficulty) ? ' active' : ' inactive';
                html += '<span class="silo-circle difficulty-circle ' + diff.color + activeClass + ' clickable-difficulty" data-difficulty="' + diff.key + '">' + diff.label + '</span>';
            });
            
            html += '</div>';
            return html;
        },

        getKeywordDisplay: function(keyword) {
            var displayKeyword = keyword || 'Click to add keyword';
            var keywordClass = keyword ? 'has-keyword' : 'no-keyword';
            return '<div class="silo-keyword-display ' + keywordClass + ' clickable-keyword" data-keyword="' + (keyword || '') + '">' + displayKeyword + '</div>';
        },

        getNavigationStatus: function() {
            // Get current post ID
            var postId = $('#post_ID').val() || $('#post-id').val() || 0;
            
            if (!postId || postId === 0) {
                return '<tr><th class="heading-larger"><svg fill="#000000" width="16px" height="16px" viewBox="0 0 0.6 0.6" xmlns="http://www.w3.org/2000/svg" style="margin-right: 6px; vertical-align: middle;"><path d="m0.493 0.232 -0.175 -0.175a0.025 0.025 0 0 0 -0.035 0l-0.175 0.175a0.025 0.025 0 0 0 0.035 0.035L0.275 0.135V0.525a0.025 0.025 0 0 0 0.05 0V0.135l0.132 0.133a0.025 0.025 0 0 0 0.035 0 0.025 0.025 0 0 0 0 -0.035" style="fill: #000000"/></svg>Parents:</th><td><span class="no-data">No parent pages</span></td></tr>' +
                       '<tr><th class="heading-larger"><svg fill="#000000" width="16px" height="16px" viewBox="0 0 0.6 0.6" xmlns="http://www.w3.org/2000/svg" style="margin-right: 6px; vertical-align: middle; transform: rotate(180deg);"><path d="m0.493 0.232 -0.175 -0.175a0.025 0.025 0 0 0 -0.035 0l-0.175 0.175a0.025 0.025 0 0 0 0.035 0.035L0.275 0.135V0.525a0.025 0.025 0 0 0 0.05 0V0.135l0.132 0.133a0.025 0.025 0 0 0 0.035 0 0.025 0.025 0 0 0 0 -0.035" style="fill: #000000"/></svg>Children:</th><td><span class="no-data">No child pages</span></td></tr>' +
                       '<tr><th class="heading-larger"><svg width="16px" height="16px" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg" fill="none" stroke="#000000" stroke-width="2" style="margin-right: 6px; vertical-align: middle;"><path d="M44 40L52 32L44 24"/><path d="M20 24L12 32L20 40"/><path d="M52 32L12 32"/></svg>Siblings:</th><td><span class="no-data">No sibling pages</span></td></tr>';
            }
            
            // Check cache first
            var cacheKey = 'navigation_' + postId;
            var cached = SEOOverviewMetaBox.navigationCache && SEOOverviewMetaBox.navigationCache[cacheKey];
            
            if (cached && (Date.now() - cached.timestamp < 300000)) { // Cache for 5 minutes
                return SEOOverviewMetaBox.formatNavigationData(cached.data);
            }
            
            // Fetch navigation data via AJAX
            SEOOverviewMetaBox.fetchNavigationData(postId);
            
            // Return loading state
            return '<tr><th class="heading-larger"><svg fill="#000000" width="16px" height="16px" viewBox="0 0 0.6 0.6" xmlns="http://www.w3.org/2000/svg" style="margin-right: 6px; vertical-align: middle;"><path d="m0.493 0.232 -0.175 -0.175a0.025 0.025 0 0 0 -0.035 0l-0.175 0.175a0.025 0.025 0 0 0 0.035 0.035L0.275 0.135V0.525a0.025 0.025 0 0 0 0.05 0V0.135l0.132 0.133a0.025 0.025 0 0 0 0.035 0 0.025 0.025 0 0 0 0 -0.035" style="fill: #000000"/></svg>Parents:</th><td><span class="loading-data">Loading...</span></td></tr>' +
                   '<tr><th class="heading-larger"><svg fill="#000000" width="16px" height="16px" viewBox="0 0 0.6 0.6" xmlns="http://www.w3.org/2000/svg" style="margin-right: 6px; vertical-align: middle; transform: rotate(180deg);"><path d="m0.493 0.232 -0.175 -0.175a0.025 0.025 0 0 0 -0.035 0l-0.175 0.175a0.025 0.025 0 0 0 0.035 0.035L0.275 0.135V0.525a0.025 0.025 0 0 0 0.05 0V0.135l0.132 0.133a0.025 0.025 0 0 0 0.035 0 0.025 0.025 0 0 0 0 -0.035" style="fill: #000000"/></svg>Children:</th><td><span class="loading-data">Loading...</span></td></tr>' +
                   '<tr><th class="heading-larger"><svg width="16px" height="16px" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg" fill="none" stroke="#000000" stroke-width="2" style="margin-right: 6px; vertical-align: middle;"><path d="M44 40L52 32L44 24"/><path d="M20 24L12 32L20 40"/><path d="M52 32L12 32"/></svg>Siblings:</th><td><span class="loading-data">Loading...</span></td></tr>';
        },

        fetchNavigationData: function(postId) {
            $.ajax({
                url: slmmSeoOverview.ajaxurl,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'get_silo_navigation',
                    post_id: postId,
                    nonce: $('input[name="slmm_seo_overview_nonce"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        // Cache the data
                        SEOOverviewMetaBox.navigationCache = SEOOverviewMetaBox.navigationCache || {};
                        var cacheKey = 'navigation_' + postId;
                        SEOOverviewMetaBox.navigationCache[cacheKey] = {
                            data: response.data,
                            timestamp: Date.now()
                        };
                        
                        // Update the navigation section
                        SEOOverviewMetaBox.updateNavigationDisplay(response.data);
                        
                        // Load semantic links data after navigation is loaded
                        SEOOverviewMetaBox.fetchSemanticLinksData(postId);
                    } else {
                        debugError('Failed to fetch navigation data: ' + response.data);
                        SEOOverviewMetaBox.updateNavigationDisplay(null);
                    }
                },
                error: function(xhr, status, error) {
                    debugError('AJAX error fetching navigation data: ' + error);
                    SEOOverviewMetaBox.updateNavigationDisplay(null);
                }
            });
        },

        updateNavigationDisplay: function(navigationData) {
            var navigationHtml = SEOOverviewMetaBox.formatNavigationData(navigationData);
            var $navigationTable = $('.seo-overview-section:contains("NAVIGATION") table');
            if ($navigationTable.length) {
                $navigationTable.html(navigationHtml);
            }
        },

        fetchSemanticLinksData: function(postId) {
            // Check if we have the semantic links AJAX handler available
            if (!slmmSeoOverview.semanticLinksNonce) {
                debugWarn('Semantic links not available - missing nonce');
                SEOOverviewMetaBox.updateSemanticLinksDisplay([]);
                return;
            }

            // Check cache first to prevent duplicate calls
            SEOOverviewMetaBox.semanticLinksCache = SEOOverviewMetaBox.semanticLinksCache || {};
            var cacheKey = 'semantic_links_' + postId;
            var cached = SEOOverviewMetaBox.semanticLinksCache[cacheKey];
            
            if (cached && (Date.now() - cached.timestamp < 300000)) { // Cache for 5 minutes
                debugLog('Using cached semantic links');
                SEOOverviewMetaBox.updateSemanticLinksDisplay(cached.data);
                return;
            }

            // Prevent duplicate simultaneous requests
            if (SEOOverviewMetaBox.semanticLinksLoading === postId) {
                debugLog('Semantic links already loading for post ' + postId);
                return;
            }
            SEOOverviewMetaBox.semanticLinksLoading = postId;

            $.ajax({
                url: slmmSeoOverview.ajaxurl,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'slmm_get_semantic_links',
                    sender_id: postId,
                    nonce: slmmSeoOverview.semanticLinksNonce
                },
                success: function(response) {
                    if (response.success) {
                        debugLog('Semantic links loaded', response.data);
                        var semanticLinks = response.data.semantic_links || [];
                        
                        // Cache the data
                        SEOOverviewMetaBox.semanticLinksCache[cacheKey] = {
                            data: semanticLinks,
                            timestamp: Date.now()
                        };
                        
                        SEOOverviewMetaBox.updateSemanticLinksDisplay(semanticLinks);
                    } else {
                        debugWarn('Failed to fetch semantic links: ' + response.data);
                        SEOOverviewMetaBox.updateSemanticLinksDisplay([]);
                    }
                },
                error: function(xhr, status, error) {
                    debugError('AJAX error fetching semantic links: ' + error);
                    SEOOverviewMetaBox.updateSemanticLinksDisplay([]);
                },
                complete: function() {
                    // Clear loading flag
                    SEOOverviewMetaBox.semanticLinksLoading = null;
                }
            });
        },

        updateSemanticLinksDisplay: function(semanticLinksData) {
            var $semanticContainer = $('#semantic-links-data');
            if (!$semanticContainer.length) {
                debugWarn('Semantic links container not found');
                return;
            }

            if (!semanticLinksData || semanticLinksData.length === 0) {
                $semanticContainer.html('<span class="no-data">No semantic links</span>');
                return;
            }

            // Format semantic links similar to navigation links
            var semanticItems = [];
            semanticLinksData.forEach(function(link) {
                var linkHtml = '<span class="nav-link clickable-url" data-url="' + link.edit_link + '">' + 
                              link.post_title + '</span>';
                semanticItems.push(linkHtml);
            });

            $semanticContainer.html(semanticItems.join(''));
        },

        formatNavigationData: function(data) {
            if (!data) {
                return '<tr><th class="heading-larger"><svg fill="#000000" width="16px" height="16px" viewBox="0 0 0.6 0.6" xmlns="http://www.w3.org/2000/svg" style="margin-right: 6px; vertical-align: middle;"><path d="m0.493 0.232 -0.175 -0.175a0.025 0.025 0 0 0 -0.035 0l-0.175 0.175a0.025 0.025 0 0 0 0.035 0.035L0.275 0.135V0.525a0.025 0.025 0 0 0 0.05 0V0.135l0.132 0.133a0.025 0.025 0 0 0 0.035 0 0.025 0.025 0 0 0 0 -0.035" style="fill: #000000"/></svg>Parents:</th><td><span class="no-data">No parent pages</span></td></tr>' +
                       '<tr><th class="heading-larger"><svg fill="#000000" width="16px" height="16px" viewBox="0 0 0.6 0.6" xmlns="http://www.w3.org/2000/svg" style="margin-right: 6px; vertical-align: middle; transform: rotate(180deg);"><path d="m0.493 0.232 -0.175 -0.175a0.025 0.025 0 0 0 -0.035 0l-0.175 0.175a0.025 0.025 0 0 0 0.035 0.035L0.275 0.135V0.525a0.025 0.025 0 0 0 0.05 0V0.135l0.132 0.133a0.025 0.025 0 0 0 0.035 0 0.025 0.025 0 0 0 0 -0.035" style="fill: #000000"/></svg>Children:</th><td><span class="no-data">No child pages</span></td></tr>' +
                       '<tr><th class="heading-larger"><svg width="16px" height="16px" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg" fill="none" stroke="#000000" stroke-width="2" style="margin-right: 6px; vertical-align: middle;"><path d="M44 40L52 32L44 24"/><path d="M20 24L12 32L20 40"/><path d="M52 32L12 32"/></svg>Siblings:</th><td><span class="no-data">No sibling pages</span></td></tr>';
            }
            
            // Format parents with icon
            var parentsHtml = '<tr><th class="heading-larger"><svg fill="#000000" width="16px" height="16px" viewBox="0 0 0.6 0.6" xmlns="http://www.w3.org/2000/svg" style="margin-right: 6px; vertical-align: middle;"><path d="m0.493 0.232 -0.175 -0.175a0.025 0.025 0 0 0 -0.035 0l-0.175 0.175a0.025 0.025 0 0 0 0.035 0.035L0.275 0.135V0.525a0.025 0.025 0 0 0 0.05 0V0.135l0.132 0.133a0.025 0.025 0 0 0 0.035 0 0.025 0.025 0 0 0 0 -0.035" style="fill: #000000"/></svg>Parents:</th><td>';
            if (data.parents && data.parents.length > 0) {
                var parentItems = [];
                data.parents.forEach(function(parent) {
                    parentItems.push('<span class="nav-link clickable-url" data-url="' + parent.url + '">' + parent.title + '</span>');
                });
                parentsHtml += parentItems.join('');
            } else {
                parentsHtml += '<span class="no-data">No parent pages</span>';
            }
            parentsHtml += '</td></tr>';
            
            // Format children with icon (rotated down arrow)
            var childrenHtml = '<tr><th class="heading-larger"><svg fill="#000000" width="16px" height="16px" viewBox="0 0 0.6 0.6" xmlns="http://www.w3.org/2000/svg" style="margin-right: 6px; vertical-align: middle; transform: rotate(180deg);"><path d="m0.493 0.232 -0.175 -0.175a0.025 0.025 0 0 0 -0.035 0l-0.175 0.175a0.025 0.025 0 0 0 0.035 0.035L0.275 0.135V0.525a0.025 0.025 0 0 0 0.05 0V0.135l0.132 0.133a0.025 0.025 0 0 0 0.035 0 0.025 0.025 0 0 0 0 -0.035" style="fill: #000000"/></svg>Children:</th><td>';
            if (data.children && data.children.length > 0) {
                var childItems = [];
                data.children.forEach(function(child) {
                    childItems.push('<span class="nav-link clickable-url" data-url="' + child.url + '">' + child.title + '</span>');
                });
                childrenHtml += childItems.join('');
            } else {
                childrenHtml += '<span class="no-data">No child pages</span>';
            }
            childrenHtml += '</td></tr>';
            
            // Format siblings with icon (left-right arrows)
            var siblingsHtml = '<tr><th class="heading-larger"><svg width="16px" height="16px" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg" fill="none" stroke="#000000" stroke-width="2" style="margin-right: 6px; vertical-align: middle;"><path d="M44 40L52 32L44 24"/><path d="M20 24L12 32L20 40"/><path d="M52 32L12 32"/></svg>Siblings:';
            if (data.siblings && data.siblings.length > 0) {
                siblingsHtml += '<button type="button" class="button button-secondary seo-copy-siblings-btn" ' +
                               'title="Copy all sibling links for pasting into content" ' +
                               'style="width: 100%; margin: 12px 0 0 0; padding: 2px 2px; min-height: 20px; font-size: 11px; font-weight: 500;">Copy Siblings</button>';
            }
            siblingsHtml += '</th><td>';
            if (data.siblings && data.siblings.length > 0) {
                var siblingItems = [];
                data.siblings.forEach(function(sibling) {
                    siblingItems.push('<span class="nav-link clickable-url" data-url="' + sibling.url + '">' + sibling.title + '</span>');
                });
                siblingsHtml += siblingItems.join('');
            } else {
                siblingsHtml += '<span class="no-data">No sibling pages</span>';
            }
            siblingsHtml += '</td></tr>';
            
            // Format semantic links with black icon
            var semanticLinksHtml = '<tr><th class="heading-larger">' +
                '<svg width="16px" height="16px" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg" fill="none" stroke="#000000" style="margin-right: 6px; vertical-align: middle;">' +
                    '<path d="M15.75 3.75A2.25 2.25 0 0 1 13.5 6A2.25 2.25 0 0 1 11.25 3.75A2.25 2.25 0 0 1 15.75 3.75z" stroke-width="1.5"/>' +
                    '<path d="M15.75 14.25A2.25 2.25 0 0 1 13.5 16.5A2.25 2.25 0 0 1 11.25 14.25A2.25 2.25 0 0 1 15.75 14.25z" stroke-width="1.5"/>' +
                    '<path d="M6.75 9A2.25 2.25 0 0 1 4.5 11.25A2.25 2.25 0 0 1 2.25 9A2.25 2.25 0 0 1 6.75 9z" stroke-width="1.5"/>' +
                    '<path d="m11.55 4.89 -5.1 2.97m5.1 5.25 -5.1 -2.97" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>' +
                '</svg>Semantic Links:';
            
            // Add semantic links data if available (check cache first)
            semanticLinksHtml += '</th><td id="semantic-links-data">';
            var postId = $('#post_ID').val() || $('#post-id').val() || 0;
            var cacheKey = 'semantic_links_' + postId;
            var cached = SEOOverviewMetaBox.semanticLinksCache && SEOOverviewMetaBox.semanticLinksCache[cacheKey];
            
            if (cached && cached.data && cached.data.length > 0) {
                // Use cached data instead of showing loading
                var semanticItems = [];
                cached.data.forEach(function(link) {
                    var linkHtml = '<span class="nav-link clickable-url" data-url="' + link.edit_link + '">' + 
                                  link.post_title + '</span>';
                    semanticItems.push(linkHtml);
                });
                semanticLinksHtml += semanticItems.join('');
            } else if (cached && cached.data && cached.data.length === 0) {
                // Cached but empty
                semanticLinksHtml += '<span class="no-data">No semantic links</span>';
            } else {
                // Not loaded yet
                semanticLinksHtml += '<span class="no-data">Loading...</span>';
            }
            semanticLinksHtml += '</td></tr>';
            
            return parentsHtml + childrenHtml + siblingsHtml + semanticLinksHtml;
        },

        getStatusIndicator: function(isPositive, count) {
            var statusClass = isPositive ? 'status-green' : 'status-red';
            var statusHtml = '<span class="status-indicator ' + statusClass + ' large-indicator">•</span>';
            
            if (count !== undefined && count !== '') {
                statusHtml += ' <span class="status-count large-count">x ' + count + '</span>';
            }
            
            return statusHtml;
        },

        // Two-way sync functions for SILO STRUCTURE
        updateImportance: function(newImportance) {
            var postId = $('#post_ID').val() || $('#post-id').val() || 0;
            if (!postId || postId === 0) return;
            
            debugLog('Updating importance to ' + newImportance + ' for post ' + postId);
            
            // Optimistic update - show change immediately
            SEOOverviewMetaBox.updateImportanceDisplay(newImportance);
            
            $.ajax({
                url: slmmSeoOverview.ajaxurl,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'slmm_change_importance',
                    post_id: postId,
                    new_importance: newImportance,
                    nonce: slmmSeoOverview.interlinkingNonce
                },
                success: function(response) {
                    if (response.success) {
                        debugLog('Importance updated successfully');
                        // No need to clear cache or refresh - optimistic update is already correct
                    } else {
                        debugError('Failed to update importance: ' + response.data);
                        // Revert optimistic update on failure
                        SEOOverviewMetaBox.cache = {};
                        SEOOverviewMetaBox.updateMetaBoxContent();
                    }
                },
                error: function(xhr, status, error) {
                    debugError('AJAX error updating importance: ' + error);
                    // Revert optimistic update on error
                    SEOOverviewMetaBox.cache = {};
                    SEOOverviewMetaBox.updateMetaBoxContent();
                }
            });
        },

        updateDifficulty: function(newDifficulty) {
            var postId = $('#post_ID').val() || $('#post-id').val() || 0;
            if (!postId || postId === 0) return;
            
            debugLog('Updating difficulty to ' + newDifficulty + ' for post ' + postId);
            
            // Optimistic update - show change immediately
            SEOOverviewMetaBox.updateDifficultyDisplay(newDifficulty);
            
            $.ajax({
                url: slmmSeoOverview.ajaxurl,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'slmm_change_difficulty',
                    post_id: postId,
                    new_difficulty: newDifficulty,
                    nonce: slmmSeoOverview.interlinkingNonce
                },
                success: function(response) {
                    if (response.success) {
                        debugLog('Difficulty updated successfully');
                        // No need to clear cache or refresh - optimistic update is already correct
                    } else {
                        debugError('Failed to update difficulty: ' + response.data);
                        // Revert optimistic update on failure
                        SEOOverviewMetaBox.cache = {};
                        SEOOverviewMetaBox.updateMetaBoxContent();
                    }
                },
                error: function(xhr, status, error) {
                    debugError('AJAX error updating difficulty: ' + error);
                    // Revert optimistic update on error
                    SEOOverviewMetaBox.cache = {};
                    SEOOverviewMetaBox.updateMetaBoxContent();
                }
            });
        },

        updateKeyword: function(newKeyword) {
            var postId = $('#post_ID').val() || $('#post-id').val() || 0;
            if (!postId || postId === 0) return;
            
            debugLog('Updating keyword to ' + newKeyword + ' for post ' + postId);
            
            // Optimistic update - show change immediately
            SEOOverviewMetaBox.updateKeywordDisplay(newKeyword);
            
            $.ajax({
                url: slmmSeoOverview.ajaxurl,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'slmm_change_keyword',
                    post_id: postId,
                    new_keyword: newKeyword,
                    nonce: slmmSeoOverview.interlinkingNonce
                },
                success: function(response) {
                    if (response.success) {
                        debugLog('Keyword updated successfully');
                        // No need to clear cache or refresh - optimistic update is already correct
                    } else {
                        debugError('Failed to update keyword: ' + response.data);
                        // Revert optimistic update on failure
                        SEOOverviewMetaBox.cache = {};
                        SEOOverviewMetaBox.updateMetaBoxContent();
                    }
                },
                error: function(xhr, status, error) {
                    debugError('AJAX error updating keyword: ' + error);
                    // Revert optimistic update on error
                    SEOOverviewMetaBox.cache = {};
                    SEOOverviewMetaBox.updateMetaBoxContent();
                }
            });
        },

        editKeyword: function(currentKeyword) {
            var newKeyword = prompt('Enter target keyword:', currentKeyword || '');
            if (newKeyword !== null) { // null means user cancelled
                // Trim and allow empty keywords
                newKeyword = newKeyword.trim();
                SEOOverviewMetaBox.updateKeyword(newKeyword);
            }
        },

        // Optimistic display update functions - immediate visual feedback
        updateImportanceDisplay: function(newImportance) {
            // Update the visual display
            var $importanceRow = $('.seo-overview-table tr:contains("Importance:")').find('td');
            if ($importanceRow.length) {
                $importanceRow.html(SEOOverviewMetaBox.getImportanceCircles(newImportance));
            }
            
            // Update the hidden field so future refreshes use the new value
            $('input[name="_slmm_importance_rating"]').val(newImportance);
            
            // Update cache with new value
            var postId = $('#post_ID').val() || $('#post-id').val() || 0;
            if (postId) {
                var cacheKey = 'silo_structure_' + postId;
                if (SEOOverviewMetaBox.cache && SEOOverviewMetaBox.cache[cacheKey]) {
                    SEOOverviewMetaBox.cache[cacheKey].importance = newImportance;
                }
            }
        },

        updateDifficultyDisplay: function(newDifficulty) {
            // Update the visual display
            var $difficultyRow = $('.seo-overview-table tr:contains("Difficulty:")').find('td');
            if ($difficultyRow.length) {
                $difficultyRow.html(SEOOverviewMetaBox.getDifficultyCircles(newDifficulty));
            }
            
            // Update the hidden field so future refreshes use the new value
            $('input[name="_slmm_difficulty_level"]').val(newDifficulty);
            
            // Update cache with new value
            var postId = $('#post_ID').val() || $('#post-id').val() || 0;
            if (postId) {
                var cacheKey = 'silo_structure_' + postId;
                if (SEOOverviewMetaBox.cache && SEOOverviewMetaBox.cache[cacheKey]) {
                    SEOOverviewMetaBox.cache[cacheKey].difficulty = newDifficulty;
                }
            }
        },

        updateKeywordDisplay: function(newKeyword) {
            // Update the visual display
            var $keywordRow = $('.seo-overview-table tr:contains("Keyword:")').find('td');
            if ($keywordRow.length) {
                $keywordRow.html(SEOOverviewMetaBox.getKeywordDisplay(newKeyword));
            }
            
            // Update the hidden field so future refreshes use the new value
            $('input[name="_slmm_target_keyword"]').val(newKeyword);
            
            // Update cache with new value
            var postId = $('#post_ID').val() || $('#post-id').val() || 0;
            if (postId) {
                var cacheKey = 'silo_structure_' + postId;
                if (SEOOverviewMetaBox.cache && SEOOverviewMetaBox.cache[cacheKey]) {
                    SEOOverviewMetaBox.cache[cacheKey].keyword = newKeyword;
                }
            }
        },

        // Copy URL to clipboard with visual feedback
        copyToClipboard: function(url, element) {
            // Try modern clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(url).then(function() {
                    SEOOverviewMetaBox.showCopyFeedback(element, 'Copied!');
                }).catch(function(err) {
                    debugError('Failed to copy URL: ' + err);
                    SEOOverviewMetaBox.fallbackCopyToClipboard(url, element);
                });
            } else {
                // Fallback for older browsers
                SEOOverviewMetaBox.fallbackCopyToClipboard(url, element);
            }
        },

        fallbackCopyToClipboard: function(url, element) {
            // Create temporary textarea
            var textArea = document.createElement('textarea');
            textArea.value = url;
            textArea.style.position = 'fixed';
            textArea.style.left = '-9999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    SEOOverviewMetaBox.showCopyFeedback(element, 'Copied!');
                } else {
                    SEOOverviewMetaBox.showCopyFeedback(element, 'Copy failed', true);
                }
            } catch (err) {
                debugError('Fallback copy failed: ' + err);
                SEOOverviewMetaBox.showCopyFeedback(element, 'Copy failed', true);
            }
            
            document.body.removeChild(textArea);
        },

        showCopyFeedback: function(element, message, isError) {
            // Save original text
            var originalText = element.text();
            var originalClass = element.attr('class');
            
            // Show feedback
            element.text(message);
            element.addClass(isError ? 'copy-error' : 'copy-success');
            
            // Restore after 1.5 seconds
            setTimeout(function() {
                element.text(originalText);
                element.attr('class', originalClass);
            }, 1500);
        },

        // Copy all sibling links for pasting into WordPress Visual Editor
        copySiblingLinks: function(buttonElement) {
            // Get current post ID using the same method as other functions
            var postId = $('#post_ID').val() || $('#post-id').val() || 0;
            
            // Get navigation data from cache
            var cacheKey = 'navigation_' + postId;
            var navigationData = SEOOverviewMetaBox.navigationCache && SEOOverviewMetaBox.navigationCache[cacheKey] && SEOOverviewMetaBox.navigationCache[cacheKey].data;
            
            if (!navigationData || !navigationData.siblings || navigationData.siblings.length === 0) {
                this.showCopyFeedback(buttonElement, 'No siblings', true);
                return;
            }
            
            // Format siblings for WordPress Visual Editor
            var formattedData = this.formatSiblingsForWordPress(navigationData.siblings);
            
            // Copy with rich text support
            this.copyRichTextToClipboard(formattedData, buttonElement);
        },

        // Format sibling links for WordPress Visual Editor Rich Text
        formatSiblingsForWordPress: function(siblings) {
            if (!siblings || siblings.length === 0) {
                return '';
            }
            
            // Create a temporary DOM container to build rich text
            var tempDiv = document.createElement('div');
            tempDiv.style.position = 'absolute';
            tempDiv.style.left = '-9999px';
            tempDiv.innerHTML = siblings.map(function(sibling) {
                // Clean title (no HTML escaping needed for rich text)
                var title = sibling.title;
                return '<p><a href="' + sibling.url + '">' + title + '</a></p>';
            }).join('');
            
            document.body.appendChild(tempDiv);
            
            // Use clipboard API with HTML MIME type for rich text
            if (navigator.clipboard && window.ClipboardItem) {
                var htmlBlob = new Blob([tempDiv.innerHTML], { type: 'text/html' });
                var textBlob = new Blob([siblings.map(function(s) { return s.title + ': ' + s.url; }).join('\n')], { type: 'text/plain' });
                
                var clipboardItem = new ClipboardItem({
                    'text/html': htmlBlob,
                    'text/plain': textBlob
                });
                
                document.body.removeChild(tempDiv);
                
                return clipboardItem; // Return ClipboardItem instead of string
            } else {
                // Fallback: return HTML string
                var result = tempDiv.innerHTML;
                document.body.removeChild(tempDiv);
                return result;
            }
        },

        // Copy all child links for pasting into WordPress Visual Editor
        copyChildLinks: function(buttonElement) {
            // Get current post ID using the same method as other functions
            var postId = $('#post_ID').val() || $('#post-id').val() || 0;
            
            // Get navigation data from cache
            var cacheKey = 'navigation_' + postId;
            var navigationData = SEOOverviewMetaBox.navigationCache && SEOOverviewMetaBox.navigationCache[cacheKey] && SEOOverviewMetaBox.navigationCache[cacheKey].data;
            
            if (!navigationData || !navigationData.children || navigationData.children.length === 0) {
                this.showCopyFeedback(buttonElement, 'No children', true);
                return;
            }
            
            // Format children for WordPress Visual Editor
            var formattedData = this.formatChildrenForWordPress(navigationData.children);
            
            // Copy with rich text support
            this.copyRichTextToClipboard(formattedData, buttonElement);
        },

        // Format child links for WordPress Visual Editor Rich Text
        formatChildrenForWordPress: function(children) {
            if (!children || children.length === 0) {
                return '';
            }
            
            // Create a temporary DOM container to build rich text
            var tempDiv = document.createElement('div');
            tempDiv.style.position = 'absolute';
            tempDiv.style.left = '-9999px';
            tempDiv.style.top = '-9999px';
            document.body.appendChild(tempDiv);
            
            // Build HTML content
            var htmlContent = '';
            children.forEach(function(child, index) {
                if (index > 0) htmlContent += '<br>';
                htmlContent += '<a href="' + child.url + '">' + child.title + '</a>';
            });
            
            tempDiv.innerHTML = htmlContent;
            
            // Create a rich text ClipboardItem
            var richTextData = tempDiv.outerHTML;
            
            // Cleanup
            document.body.removeChild(tempDiv);
            
            // Return ClipboardItem for rich text
            if (window.ClipboardItem) {
                return new ClipboardItem({
                    'text/html': new Blob([richTextData], { type: 'text/html' }),
                    'text/plain': new Blob([children.map(function(child) {
                        return child.title + ': ' + child.url;
                    }).join('\n')], { type: 'text/plain' })
                });
            } else {
                // Fallback to HTML string
                return richTextData;
            }
        },

        // Copy rich text to clipboard with comprehensive browser support
        copyRichTextToClipboard: function(data, buttonElement) {
            var self = this;
            
            try {
                // Modern rich text clipboard API
                if (navigator.clipboard && window.ClipboardItem && data instanceof ClipboardItem) {
                    navigator.clipboard.write([data]).then(function() {
                        debugLog('Rich text copied successfully');
                        self.showCopyFeedback(buttonElement, 'Copied!');
                    }).catch(function(err) {
                        debugWarn('Rich text copy failed: ' + err);
                        self.showCopyFeedback(buttonElement, 'Failed', true);
                    });
                } else if (navigator.clipboard && typeof data === 'string') {
                    // Fallback to HTML clipboard write
                    navigator.clipboard.write([
                        new ClipboardItem({
                            'text/html': new Blob([data], { type: 'text/html' }),
                            'text/plain': new Blob([data.replace(/<[^>]*>/g, '')], { type: 'text/plain' })
                        })
                    ]).then(function() {
                        debugLog('HTML clipboard write successful');
                        self.showCopyFeedback(buttonElement, 'Copied!');
                    }).catch(function() {
                        // Final fallback to plain text
                        navigator.clipboard.writeText(data).then(function() {
                            debugWarn('Fell back to plain text copy');
                            self.showCopyFeedback(buttonElement, 'Copied!');
                        }).catch(function() {
                            self.showCopyFeedback(buttonElement, 'Failed', true);
                        });
                    });
                } else {
                    // Legacy browser fallback using DOM manipulation
                    var tempDiv = document.createElement('div');
                    tempDiv.innerHTML = typeof data === 'string' ? data : '';
                    tempDiv.style.position = 'absolute';
                    tempDiv.style.left = '-9999px';
                    document.body.appendChild(tempDiv);
                    
                    var range = document.createRange();
                    range.selectNodeContents(tempDiv);
                    var selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);
                    
                    var success = document.execCommand('copy');
                    document.body.removeChild(tempDiv);
                    selection.removeAllRanges();
                    
                    debugLog('Legacy copy result: ' + success);
                    self.showCopyFeedback(buttonElement, success ? 'Copied!' : 'Failed', !success);
                }
            } catch (e) {
                debugError('Rich text copy error: ' + e);
                self.showCopyFeedback(buttonElement, 'Error', true);
            }
        },

        // Generate interlinking button HTML with authorization-aware styling
        getInterlinkingButtonHtml: function() {
            // Check authorization status - if not authorized, return empty string (hide button)
            // Treat undefined, false, empty string, or 0 as unauthorized
            var isAuthorized = (typeof slmmSeoOverview === 'undefined' || !!slmmSeoOverview.userAuthorized);
            
            if (!isAuthorized) {
                return ''; // Return empty string to completely hide the button
            }
            
            // Generate button HTML for authorized users
            return '<button type="button" class="button button-secondary seo-interlinking-btn" ' +
                   'title="Open Interlinking Suite with current page search" ' +
                   'style="margin-left: 8px; padding: 1px 2px; min-height: 16px; vertical-align: middle; border-radius: 3px; border: 1px solid #ddd; background: linear-gradient(to bottom, #f9f9f9, #ececec); box-shadow: 0 1px 2px rgba(0,0,0,0.1); display: inline-flex; align-items: center; justify-content: center; cursor: pointer;">' +
                   '<svg width="14px" height="14px" viewBox="0 0 0.4 0.4" xmlns="http://www.w3.org/2000/svg" fill="none" style="display: block;">' +
                   '<path fill="currentColor" fill-rule="evenodd" d="M0.156 0C0.139 0 0.125 0.014 0.125 0.031v0.088C0.125 0.136 0.139 0.15 0.156 0.15H0.175v0.025H0.025a0.019 0.019 0 0 0 0 0.038h0.063V0.25H0.056C0.039 0.25 0.025 0.264 0.025 0.281v0.088c0 0.017 0.014 0.031 0.031 0.031h0.088C0.161 0.4 0.175 0.386 0.175 0.369v-0.088C0.175 0.264 0.161 0.25 0.144 0.25H0.125V0.213h0.15V0.25h-0.019C0.239 0.25 0.225 0.264 0.225 0.281v0.088c0 0.017 0.014 0.031 0.031 0.031h0.088c0.017 0 0.031 -0.014 0.031 -0.031v-0.088c0 -0.017 -0.014 -0.031 -0.031 -0.031H0.313V0.213H0.375A0.019 0.019 0 0 0 0.375 0.175H0.213V0.15h0.031C0.261 0.15 0.275 0.136 0.275 0.119v-0.088C0.275 0.014 0.261 0 0.244 0zm0.106 0.288v0.075h0.075v-0.075zm-0.2 0v0.075h0.075v-0.075zm0.175 -0.175v-0.075h-0.075v0.075z" clip-rule="evenodd"/>' +
                   '</svg>' +
                   '</button>';
        },

        // Open Interlinking Suite with current page search
        openInterlinkingSuite: function() {
            // Get current post ID and title
            var postId = $('#post_ID').val() || $('#post-id').val() || 0;
            var currentTitle = $('#title').val() || $('#post-title-0').val() || '';
            
            if (!currentTitle) {
                // Try to get title from tinymce if available
                if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                    var titleElement = document.getElementById('title');
                    if (titleElement) {
                        currentTitle = titleElement.value;
                    }
                }
            }
            
            // Use a fallback if we still don't have a title
            if (!currentTitle) {
                currentTitle = document.title.replace(' ‹ ', ' - ').replace(' — WordPress', '');
            }
            
            // Try to get ACF title if configured
            var self = this;
            this.getACFTitleForPost(postId, function(acfTitle) {
                var searchTitle = acfTitle || currentTitle;
                
                // Construct the interlinking suite URL
                var baseUrl = (typeof ajaxurl !== 'undefined') ? 
                    ajaxurl.replace('admin-ajax.php', 'admin.php?page=slmm-interlinking-suite') :
                    '/wp-admin/admin.php?page=slmm-interlinking-suite';
                
                // Add search parameter if we have a title
                var finalUrl = searchTitle ? baseUrl + '&search=' + encodeURIComponent(searchTitle) : baseUrl;
                
                // Open in new tab
                window.open(finalUrl, '_blank');
                
                debugLog('Opened interlinking suite with search: ' + searchTitle);
            });
        },

        // Get ACF title for a specific post (helper function for interlinking suite)
        getACFTitleForPost: function(postId, callback) {
            if (!postId || postId === 0) {
                callback(null);
                return;
            }
            
            // Check if we have SEO overview data available (fallback to interlinking data)
            var ajaxUrl = (typeof slmmSeoOverview !== 'undefined') ? slmmSeoOverview.ajaxurl : 
                         (typeof slmmInterlinkingData !== 'undefined') ? slmmInterlinkingData.ajax_url : 
                         (typeof ajaxurl !== 'undefined') ? ajaxurl : '/wp-admin/admin-ajax.php';
                         
            var nonce = (typeof slmmSeoOverview !== 'undefined') ? slmmSeoOverview.interlinkingNonce :
                       (typeof slmmInterlinkingData !== 'undefined') ? slmmInterlinkingData.nonce : '';
            
            if (!nonce) {
                callback(null);
                return;
            }
            
            // Make AJAX call to get ACF settings and then the ACF title
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'slmm_load_acf_settings',
                    nonce: nonce
                },
                success: function(response) {
                    if (response.success && response.data && response.data.acf_field_name) {
                        var acfFieldName = response.data.acf_field_name;
                        
                        // Now get the ACF title for this specific post
                        $.ajax({
                            url: ajaxUrl,
                            type: 'POST',
                            dataType: 'json',
                            data: {
                                action: 'slmm_get_acf_titles_batch',
                                post_ids: [postId],
                                field_name: acfFieldName,
                                nonce: nonce
                            },
                            success: function(titleResponse) {
                                if (titleResponse.success && titleResponse.data && titleResponse.data[postId]) {
                                    callback(titleResponse.data[postId]);
                                } else {
                                    callback(null);
                                }
                            },
                            error: function() {
                                callback(null);
                            }
                        });
                    } else {
                        // No ACF field configured
                        callback(null);
                    }
                },
                error: function() {
                    callback(null);
                }
            });
        },

        // Priority accordion functions
        getPriorityAccordions: function() {
            var priorities = [
                {level: '1', label: 'Priority 1 Pages', color: 'priority-1'},
                {level: '2', label: 'Priority 2 Pages', color: 'priority-2'},
                {level: '3', label: 'Priority 3 Pages', color: 'priority-3'}
            ];
            
            var accordionHtml = '';
            priorities.forEach(function(priority) {
                accordionHtml += '<div class="priority-accordion-item">' +
                    '<div class="priority-accordion-header ' + priority.color + '" data-priority="' + priority.level + '">' +
                        '<span class="accordion-icon">▼</span>' +
                        '<span class="accordion-title">' + priority.label + '</span>' +
                        '<span class="accordion-count">(<span class="count-number">0</span>)</span>' +
                    '</div>' +
                    '<div class="priority-accordion-content" data-priority="' + priority.level + '">' +
                        '<div class="loading-data">Click to load pages...</div>' +
                    '</div>' +
                '</div>';
            });
            
            return accordionHtml;
        },

        loadPriorityPages: function(priorityLevel, $content) {
            // Show loading state
            $content.html('<div class="loading-data">Loading pages...</div>');
            
            $.ajax({
                url: slmmSeoOverview.ajaxurl,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'get_priority_pages',
                    priority_level: priorityLevel,
                    nonce: $('input[name="slmm_seo_overview_nonce"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        // Update the count in the header
                        var $header = $content.prev('.priority-accordion-header');
                        $header.find('.count-number').text(response.data.count);
                        
                        // Display the pages
                        var pagesHtml = SEOOverviewMetaBox.formatPriorityPages(response.data);
                        $content.html(pagesHtml);
                        $content.addClass('loaded');
                    } else {
                        debugError('Failed to load priority pages: ' + response.data);
                        $content.html('<div class="no-data">Error loading pages</div>');
                    }
                },
                error: function(xhr, status, error) {
                    debugError('AJAX error loading priority pages: ' + error);
                    $content.html('<div class="no-data">Error loading pages</div>');
                }
            });
        },

        formatPriorityPages: function(data) {
            if (!data.pages || data.pages.length === 0) {
                return '<div class="no-data">No pages found with this priority level</div>';
            }
            
            var pagesHtml = '<div class="priority-pages-list">';
            data.pages.forEach(function(page) {
                var statusClass = page.status === 'publish' ? 'status-published' : 'status-draft';
                var difficultyLabel = SEOOverviewMetaBox.getDifficultyLabel(page.difficulty);
                var keywordDisplay = page.keyword ? page.keyword : 'No keyword';
                
                pagesHtml += '<div class="priority-page-item">' +
                    '<div class="page-title">' +
                        '<span class="nav-link clickable-url" data-url="' + page.url + '">' + page.title + '</span>' +
                        '<span class="page-status ' + statusClass + '">(' + page.status + ')</span>' +
                    '</div>' +
                    '<div class="page-meta">' +
                        '<span class="page-keyword"><strong>Keyword:</strong> ' + keywordDisplay + '</span>' +
                        '<span class="page-difficulty"><strong>Difficulty:</strong> ' + difficultyLabel + '</span>' +
                        '<a href="' + page.edit_url + '" class="page-edit-link" target="_blank">Edit</a>' +
                    '</div>' +
                '</div>';
            });
            pagesHtml += '</div>';
            
            return pagesHtml;
        },

        getDifficultyLabel: function(difficulty) {
            var difficultyMap = {
                'easy': 'Easy',
                'medium': 'Medium',
                'hard': 'Hard',
                'very-hard': 'Very Hard'
            };
            return difficultyMap[difficulty] || 'Medium';
        },

        restorePriorityAccordionStates: function(states) {
            // Restore the accordion states after HTML rebuild
            for (var priority in states) {
                var state = states[priority];
                var $header = $('.priority-accordion-header[data-priority="' + priority + '"]');
                var $content = $header.next('.priority-accordion-content');
                
                if (state.expanded) {
                    // Restore expanded state
                    $header.addClass('expanded');
                    $content.addClass('expanded').show(); // Use show() instead of slideDown to avoid animation
                    
                    if (state.loaded) {
                        // Restore loaded content
                        $content.addClass('loaded');
                        $content.html(state.content);
                    }
                }
            }
        },

        // Utility function to limit how often a function is called
        debounce: function(func, wait) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(function() {
                    func.apply(context, args);
                }, wait);
            };
        },

        // CLASSIC EDITOR SEMANTIC INDICATOR SYSTEM
        // Similar to Direct Editor system but works with WordPress Classic Editor TinyMCE

        /**
         * Initialize Classic Editor semantic indicators
         * Sets up TinyMCE content scanning for link detection - EXACTLY like Direct Editor
         */
        initClassicEditorSemanticIndicators: function() {
            var self = this;
            
            // Wait for TinyMCE to be ready
            if (typeof tinymce !== 'undefined') {
                // Hook into new editors being added (AddEditor event)
                tinymce.on('AddEditor', function(e) {
                    self.setupTinyMCEScanning(e.editor);
                });
                
                // Hook into already initialized editors (especially 'content' editor)
                tinymce.editors.forEach(function(editor) {
                    if (editor.initialized) {
                        self.setupTinyMCEScanning(editor);
                    } else {
                        // Wait for initialization
                        editor.on('init', function() {
                            self.setupTinyMCEScanning(editor);
                        });
                    }
                });
                
                // Special handling for main WordPress 'content' editor
                if (tinymce.get('content')) {
                    self.setupTinyMCEScanning(tinymce.get('content'));
                } else {
                    // Wait for content editor to be available
                    var contentCheckInterval = setInterval(function() {
                        var contentEditor = tinymce.get('content');
                        if (contentEditor && contentEditor.initialized) {
                            self.setupTinyMCEScanning(contentEditor);
                            clearInterval(contentCheckInterval);
                        }
                    }, 500);
                    
                    // Stop checking after 10 seconds
                    setTimeout(function() {
                        clearInterval(contentCheckInterval);
                    }, 10000);
                }
            } else {
                // Retry after a delay if TinyMCE isn't loaded yet
                setTimeout(function() {
                    self.initClassicEditorSemanticIndicators();
                }, 1000);
            }
        },

        /**
         * Set up TinyMCE content scanning for a specific editor instance
         * Uses EXACT same pattern as Direct Editor (lines 1286-1298)
         * @param {Object} editor - TinyMCE editor instance
         */
        setupTinyMCEScanning: function(editor) {
            var self = this;
            
            // EXACTLY like Direct Editor: Scan on editor initialization (with delay for content loading)
            editor.on('init', function() {
                setTimeout(function() {
                    self.scanTinyMCEContent(editor);
                }, 1000); // Same 1000ms delay as Direct Editor
            });
            
            // EXACTLY like Direct Editor: Scan when editor loses focus  
            editor.on('blur', function() {
                setTimeout(function() {
                    self.scanTinyMCEContent(editor);
                }, 100); // Same 100ms delay as Direct Editor
            });
            
            // Additional: Scan on content changes (debounced for performance)
            var scanTimeout;
            editor.on('change keyup input paste', function() {
                clearTimeout(scanTimeout);
                scanTimeout = setTimeout(function() {
                    self.scanTinyMCEContent(editor);
                }, 500);
            });
        },

        /**
         * Scan TinyMCE content for links and update semantic indicators
         * @param {Object} editor - TinyMCE editor instance
         */
        scanTinyMCEContent: function(editor) {
            if (!editor || !editor.getContent) {
                return;
            }
            
            // Get current editor content (HTML)
            var editorContent = editor.getContent();
            if (!editorContent || editorContent.trim().length === 0) {
                // Clear all indicators if no content
                this.clearAllSemanticIndicators();
                return;
            }
            
            // Extract URLs from content (only anchor links)
            var foundUrls = this.extractUrlsFromTinyMCEContent(editorContent);
            
            // Get all sidebar nav-link URLs to match against
            var sidebarUrls = this.getSemanticLinkUrls();
            
            // Apply semantic indicators based on matches
            this.applySemanticIndicators(foundUrls, sidebarUrls);
        },

        /**
         * Extract all URLs from TinyMCE HTML content
         * ONLY counts actual <a> tag links, NOT naked URLs in plain text
         * @param {string} content - HTML content from TinyMCE
         * @returns {Array} Array of normalized URLs found in anchor links
         */
        extractUrlsFromTinyMCEContent: function(content) {
            var urls = [];
            var tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            
            // ONLY find <a> tags with href attributes (proper anchor links)
            // Plain text URLs like "http://localhost:8884/page" should NOT count as links
            var links = tempDiv.querySelectorAll('a[href]');
            for (var i = 0; i < links.length; i++) {
                var href = links[i].getAttribute('href');
                if (href && href.trim().length > 0) {
                    urls.push(this.normalizeUrl(href));
                }
            }
            
            // Remove duplicates and return
            return Array.from(new Set(urls));
        },

        /**
         * Get all semantic link URLs from sidebar nav-link elements  
         * Similar to Direct Editor getSidebarLinkUrls but targets Classic Editor elements
         * @returns {Object} Object mapping normalized URLs to their DOM elements
         */
        getSemanticLinkUrls: function() {
            var sidebarUrls = {};
            var self = this;
            
            // Find all nav-link elements with clickable-url class in SEO Overview
            $('.nav-link.clickable-url').each(function() {
                var $linkElement = $(this);
                var url = $linkElement.attr('data-url') || $linkElement.data('url');
                
                if (url && url.trim()) {
                    var normalizedUrl = self.normalizeUrl(url);
                    sidebarUrls[normalizedUrl] = $linkElement;
                }
            });
            
            return sidebarUrls;
        },

        /**
         * Apply semantic indicators to matched nav-link elements
         * Uses bidirectional approach - removes old indicators and adds new ones
         * INCLUDES PERSISTENCE SYSTEM like Direct Editor to survive 5-second refresh cycles
         * @param {Array} foundUrls - URLs found in TinyMCE content
         * @param {Object} sidebarUrls - Sidebar URLs mapped to their elements
         */
        applySemanticIndicators: function(foundUrls, sidebarUrls) {
            var self = this;
            var addedCount = 0;
            var removedCount = 0;
            
            // Initialize foundLinkUrls Set if not exists (like Direct Editor)
            if (!this.foundLinkUrls) {
                this.foundLinkUrls = new Set();
            }
            
            // First pass: Remove indicators from elements that no longer have matching URLs
            Object.keys(sidebarUrls).forEach(function(sidebarUrl) {
                var $linkElement = sidebarUrls[sidebarUrl];
                var hasIndicator = $linkElement.hasClass('link-found-in-content');
                var isCurrentlyFound = foundUrls.indexOf(sidebarUrl) !== -1;
                
                if (hasIndicator && !isCurrentlyFound) {
                    $linkElement.removeClass('link-found-in-content');
                    
                    // PERSISTENCE: Remove from storage as well
                    self.foundLinkUrls.delete(sidebarUrl);
                    
                    removedCount++;
                }
            });
            
            // Second pass: Add indicators for URLs found in current content
            foundUrls.forEach(function(foundUrl) {
                if (sidebarUrls[foundUrl]) {
                    var $linkElement = sidebarUrls[foundUrl];
                    
                    // Check if indicator needs to be applied
                    if (!$linkElement.hasClass('link-found-in-content')) {
                        $linkElement.addClass('link-found-in-content');
                        
                        // PERSISTENCE: Store this URL so we can restore it after SEO Overview refreshes
                        self.foundLinkUrls.add(foundUrl);
                        
                        addedCount++;
                    } else {
                        // URL still found - ensure it's in storage
                        self.foundLinkUrls.add(foundUrl);
                    }
                }
            });
        },

        /**
         * Clear all semantic indicators from nav-link elements
         */
        clearAllSemanticIndicators: function() {
            var clearedCount = 0;
            $('.nav-link.link-found-in-content').each(function() {
                $(this).removeClass('link-found-in-content');
                clearedCount++;
            });
            
            // PERSISTENCE: Also clear stored URLs when clearing all indicators
            if (this.foundLinkUrls) {
                this.foundLinkUrls.clear();
            }
        },
        
        /**
         * PERSISTENCE: Restore semantic indicators after SEO Overview HTML regeneration
         * Called after the 5-second refresh cycle to preserve visual indicators
         * EXACTLY like Direct Editor's restoreLinkTrackingIndicators function
         */
        restoreSemanticIndicators: function() {
            if (!this.foundLinkUrls || this.foundLinkUrls.size === 0) {
                return;
            }
            
            // Get current sidebar URLs after HTML regeneration
            var sidebarUrls = this.getSemanticLinkUrls();
            var self = this;
            
            // Restore indicators for previously found URLs
            this.foundLinkUrls.forEach(function(foundUrl) {
                if (sidebarUrls[foundUrl]) {
                    var $linkElement = sidebarUrls[foundUrl];
                    if (!$linkElement.hasClass('link-found-in-content')) {
                        $linkElement.addClass('link-found-in-content');
                    }
                } else {
                    // URL no longer exists in sidebar - remove from storage
                    self.foundLinkUrls.delete(foundUrl);
                }
            });
        },

        /**
         * Normalize URL for consistent matching
         * @param {string} url - URL to normalize
         * @returns {string} Normalized URL
         */
        normalizeUrl: function(url) {
            if (!url) return '';
            
            // Remove trailing slash and convert to lowercase for consistent matching
            return url.trim().toLowerCase().replace(/\/$/, '');
        }
    };

    // Initialize the module
    SEOOverviewMetaBox.init();
    
    // Initialize Classic Editor semantic indicators
    $(document).ready(function() {
        // Add small delay to ensure other scripts are loaded
        setTimeout(function() {
            SEOOverviewMetaBox.initClassicEditorSemanticIndicators();
        }, 1500);
    });

})(jQuery); 