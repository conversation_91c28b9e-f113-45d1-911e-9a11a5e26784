# SLMM Interlinking Suite - Memory Leak & Performance Testing

This directory contains comprehensive testing tools for detecting memory leaks and measuring server performance in the SLMM Interlinking Suite.

## Quick Start

### Enable Testing Mode
First, ensure WordPress debug mode is enabled in `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### Access Testing Interface
Navigate to **Tools > SLMM Memory Tests** in WordPress admin for the web interface.

### Command Line Testing
```bash
# Basic memory leak test
./tests/load-testing-tools.sh memory

# AJAX load testing
./tests/load-testing-tools.sh ajax 10 50

# Full test suite
./tests/load-testing-tools.sh full
```

## Testing Methods

### 1. Memory Leak Detection

#### Web Interface
- Go to **Tools > SLMM Memory Tests**
- Set test parameters (iterations, size)
- Click "Run Memory Leak Test"
- Review results for memory growth patterns

#### Command Line
```bash
# Test with 10 iterations, 1000 pages each
./tests/load-testing-tools.sh memory 10 1000

# Stress test with 20 iterations, 5000 pages each
./tests/load-testing-tools.sh memory 20 5000
```

#### Expected Results
- **✅ PASS**: Memory growth < 10MB total
- **⚠️ WARNING**: Memory growth 10-50MB (investigate)
- **❌ FAIL**: Memory growth > 50MB (memory leak likely)

### 2. Server Load Testing

#### AJAX Endpoint Testing
```bash
# Test with 5 concurrent requests, 20 total
./tests/load-testing-tools.sh ajax 5 20

# Stress test with 15 concurrent, 100 total
./tests/load-testing-tools.sh ajax 15 100
```

#### Apache Bench Testing
```bash
# Basic load test
./tests/load-testing-tools.sh apache 100 10

# Heavy load test
./tests/load-testing-tools.sh apache 500 25
```

#### Expected Performance
- **Response Time**: < 2 seconds for 200+ posts
- **Success Rate**: > 95% under normal load
- **Throughput**: > 10 requests/second

### 3. System Resource Monitoring

```bash
# Monitor for 30 seconds, check every 2 seconds
./tests/load-testing-tools.sh monitor 30 2

# Extended monitoring during load test
./tests/load-testing-tools.sh monitor 300 5
```

## Real-World Testing Scenarios

### Large Site Testing (1000+ Posts)
```bash
# Set environment for your WordPress install
export WP_URL="http://your-wp-site.com"

# Test memory with large dataset
./tests/load-testing-tools.sh memory 15 2000

# Test concurrent user load
./tests/load-testing-tools.sh ajax 20 100
```

### Production Environment Testing
```bash
# Conservative production test
./tests/load-testing-tools.sh apache 200 5

# Monitor during peak usage
./tests/load-testing-tools.sh monitor 600 10
```

### Memory Leak Investigation
```bash
# Extended memory testing
./tests/load-testing-tools.sh memory 50 1000

# Check logs for specific patterns
tail -f /wp-content/debug.log | grep "SLMM Performance"
```

## Interpreting Results

### Memory Test Results
```json
{
  "memory_leak_detected": false,
  "total_memory_growth": 2097152,
  "total_growth_mb": 2.0,
  "recommendations": [
    "Memory usage appears stable",
    "Continue monitoring during production use"
  ]
}
```

### Load Test Analysis
- **HTTP 200 responses**: Normal operation
- **HTTP 500 errors**: Server overload or code errors
- **Timeouts**: Insufficient server resources
- **High response times**: Optimization needed

### System Monitoring
- **CPU Load > 2.0**: Server strain
- **Memory Usage > 80%**: Memory pressure
- **Disk I/O spikes**: Database bottlenecks

## Troubleshooting

### Common Issues

#### Memory Leaks
**Symptoms**: Gradual memory increase over iterations
**Solutions**:
- Check for unreleased variables in loops
- Verify proper `unset()` usage
- Review array handling in hierarchy processing

#### Slow Response Times
**Symptoms**: Response times > 3 seconds
**Solutions**:
- Enable database query caching
- Optimize WordPress database
- Increase PHP memory limit
- Review D3.js data size limits

#### High CPU Usage
**Symptoms**: Load average > 2.0 during tests
**Solutions**:
- Implement chunk processing
- Add processing delays
- Optimize mathematical calculations

### Performance Optimization

#### PHP Configuration
```ini
memory_limit = 512M
max_execution_time = 300
post_max_size = 64M
upload_max_filesize = 64M
```

#### WordPress Optimization
```php
// In wp-config.php
define('WP_MEMORY_LIMIT', '512M');
define('WP_MAX_MEMORY_LIMIT', '512M');

// Enable object caching
define('WP_CACHE', true);
```

## Automated Testing

### CI/CD Integration
```bash
#!/bin/bash
# Add to your deployment pipeline

# Run quick tests
./tests/load-testing-tools.sh memory 5 500
if [ $? -ne 0 ]; then
    echo "Memory test failed - deployment aborted"
    exit 1
fi

# Basic load test
./tests/load-testing-tools.sh ajax 3 10
if [ $? -ne 0 ]; then
    echo "Load test failed - check server capacity"
    exit 1
fi
```

### Cron Monitoring
```bash
# Add to crontab for regular monitoring
# 0 */6 * * * /path/to/tests/load-testing-tools.sh memory 10 1000
```

## Log Analysis

### WordPress Debug Logs
```bash
# Memory-related logs
grep "SLMM Performance" /wp-content/debug.log

# Memory warnings
grep "Memory limit" /wp-content/debug.log

# AJAX errors
grep "slmm_.*ajax" /wp-content/debug.log
```

### System Logs
```bash
# PHP errors
tail -f /var/log/php_errors.log

# Apache/Nginx errors
tail -f /var/log/apache2/error.log
tail -f /var/log/nginx/error.log
```

## Benchmarking Guidelines

### Baseline Metrics (Development)
- **Memory usage**: < 64MB for 1000 posts
- **Response time**: < 1 second for AJAX calls
- **Load capacity**: 10 concurrent users

### Production Targets
- **Memory efficiency**: < 100KB per post processed
- **Response time**: < 2 seconds for 5000+ posts
- **Concurrent capacity**: 25+ simultaneous users

### Performance Thresholds
- **🟢 Good**: Memory growth < 5MB, Response < 1s
- **🟡 Acceptable**: Memory growth 5-15MB, Response 1-3s
- **🔴 Poor**: Memory growth > 15MB, Response > 3s

## Advanced Testing

### Custom Test Scenarios
Modify `memory-leak-tester.php` to add:
- Custom post type testing
- ACF field testing
- Hierarchical depth testing
- CSV import/export testing

### Integration Testing
Test with other plugins:
- Yoast SEO compatibility
- WooCommerce large catalogs
- Multilingual plugins (WPML)

For support with testing or performance issues, check the main plugin documentation or contact the development team.