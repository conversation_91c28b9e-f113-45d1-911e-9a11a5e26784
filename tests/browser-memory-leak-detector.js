// BROWSER MEMORY LEAK DETECTOR
// Comprehensive JavaScript memory profiling for the interlinking suite
// Tracks memory usage at different stages to identify leak sources

(function() {
    console.log('🔍 Starting BROWSER Memory Leak Detection...');
    console.log('📊 This will track JavaScript heap and DOM memory usage');

    let memorySnapshots = [];
    let testIteration = 0;
    const maxIterations = 3; // Fewer iterations to see clear patterns

    // Memory monitoring utilities
    const MemoryProfiler = {
        // Get current memory usage
        getCurrentMemory: function() {
            const memory = {
                timestamp: Date.now(),
                iteration: testIteration
            };

            // Browser heap memory (Chrome/Edge)
            if (performance.memory) {
                memory.heap = {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024 * 100) / 100,
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024 * 100) / 100,
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024 * 100) / 100
                };
            }

            // DOM metrics
            memory.dom = {
                nodes: document.getElementsByTagName('*').length,
                divs: document.getElementsByTagName('div').length,
                scripts: document.getElementsByTagName('script').length,
                stylesheets: document.querySelectorAll('link[rel="stylesheet"], style').length
            };

            // D3.js specific objects
            memory.d3 = {
                svg_elements: document.querySelectorAll('svg').length,
                svg_children: 0,
                d3_selections: 0
            };

            // Count SVG children
            document.querySelectorAll('svg').forEach(svg => {
                memory.d3.svg_children += svg.getElementsByTagName('*').length;
            });

            // jQuery objects (if available)
            if (typeof jQuery !== 'undefined') {
                memory.jquery = {
                    cache_size: Object.keys(jQuery.cache || {}).length,
                    event_handlers: 0
                };

                // Count event handlers (approximate)
                try {
                    jQuery('*').each(function() {
                        const events = jQuery._data(this, 'events');
                        if (events) {
                            Object.keys(events).forEach(eventType => {
                                memory.jquery.event_handlers += events[eventType].length;
                            });
                        }
                    });
                } catch(e) {
                    memory.jquery.event_handlers = 'unknown';
                }
            }

            // Interlinking suite specific objects
            memory.interlinking = {
                slmm_objects: Object.keys(window).filter(key => key.startsWith('slmm')).length,
                interlinking_data: typeof window.slmmInterlinkingData !== 'undefined',
                tree_data_size: 0
            };

            // Measure tree data size if available
            if (window.slmmTreeData) {
                try {
                    memory.interlinking.tree_data_size = JSON.stringify(window.slmmTreeData).length;
                } catch(e) {
                    memory.interlinking.tree_data_size = 'circular_ref';
                }
            }

            return memory;
        },

        // Take a snapshot with a label
        snapshot: function(label) {
            const memory = this.getCurrentMemory();
            memory.label = label;
            memory.stage = label;
            memorySnapshots.push(memory);

            console.log(`📊 ${label}:`, {
                heap: memory.heap ? `${memory.heap.used}MB used, ${memory.heap.total}MB total` : 'unavailable',
                dom_nodes: memory.dom.nodes,
                svg_elements: memory.d3.svg_elements,
                svg_children: memory.d3.svg_children
            });

            return memory;
        },

        // Calculate memory growth between snapshots
        calculateGrowth: function(startSnapshot, endSnapshot) {
            const growth = {
                label: `${startSnapshot.label} → ${endSnapshot.label}`,
                time_diff: endSnapshot.timestamp - startSnapshot.timestamp
            };

            if (startSnapshot.heap && endSnapshot.heap) {
                growth.heap = {
                    used_mb: endSnapshot.heap.used - startSnapshot.heap.used,
                    total_mb: endSnapshot.heap.total - startSnapshot.heap.total
                };
            }

            growth.dom = {
                nodes: endSnapshot.dom.nodes - startSnapshot.dom.nodes,
                divs: endSnapshot.dom.divs - startSnapshot.dom.divs
            };

            growth.d3 = {
                svg_elements: endSnapshot.d3.svg_elements - startSnapshot.d3.svg_elements,
                svg_children: endSnapshot.d3.svg_children - startSnapshot.d3.svg_children
            };

            if (startSnapshot.jquery && endSnapshot.jquery) {
                growth.jquery = {
                    cache_growth: endSnapshot.jquery.cache_size - startSnapshot.jquery.cache_size,
                    handler_growth: typeof endSnapshot.jquery.event_handlers === 'number' && typeof startSnapshot.jquery.event_handlers === 'number'
                        ? endSnapshot.jquery.event_handlers - startSnapshot.jquery.event_handlers
                        : 'unknown'
                };
            }

            growth.interlinking = {
                objects_growth: endSnapshot.interlinking.slmm_objects - startSnapshot.interlinking.slmm_objects,
                tree_data_growth: endSnapshot.interlinking.tree_data_size - startSnapshot.interlinking.tree_data_size
            };

            return growth;
        }
    };

    // Test runner that isolates different components
    const MemoryLeakTester = {
        runComprehensiveTest: async function() {
            console.log('🔄 Running comprehensive memory leak test...');

            for (let i = 1; i <= maxIterations; i++) {
                testIteration = i;
                console.log(`\n🧪 === ITERATION ${i}/${maxIterations} ===`);

                await this.runSingleIteration(i);

                // Small delay between iterations
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            this.analyzeResults();
        },

        runSingleIteration: async function(iteration) {
            // STAGE 1: Initial state
            const initial = MemoryProfiler.snapshot(`iter${iteration}_initial`);

            // STAGE 2: Before AJAX call
            const beforeAjax = MemoryProfiler.snapshot(`iter${iteration}_before_ajax`);

            // STAGE 3: Make AJAX call (like the memory test)
            const ajaxPromise = new Promise((resolve) => {
                jQuery.post(slmmInterlinkingData.ajax_url, {
                    action: 'slmm_generate_silo_grid',
                    nonce: slmmInterlinkingData.nonce,
                    post_type_filter: 'page',
                    enable_memory_tracking: 'false', // Don't need PHP tracking for JS test
                    fresh_analysis: 'true',
                    include_links: 'true',
                    tree_mode: true,
                    max_depth: 5
                })
                .done(function(response) {
                    resolve(response);
                })
                .fail(function() {
                    resolve(null);
                });
            });

            const ajaxResponse = await ajaxPromise;
            const afterAjax = MemoryProfiler.snapshot(`iter${iteration}_after_ajax`);

            // STAGE 4: Process response data (simulate what the interface does)
            if (ajaxResponse && ajaxResponse.success) {
                // Simulate storing the data like the interface does
                window.slmmTempTestData = ajaxResponse;

                // Simulate some DOM manipulation (like the tree would do)
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = '<svg><g><circle/><text>Test</text></g></svg>'.repeat(100);
                document.body.appendChild(tempDiv);

                const afterProcessing = MemoryProfiler.snapshot(`iter${iteration}_after_processing`);

                // STAGE 5: Cleanup attempt (remove temp elements)
                document.body.removeChild(tempDiv);
                delete window.slmmTempTestData;

                const afterCleanup = MemoryProfiler.snapshot(`iter${iteration}_after_cleanup`);

                // Calculate and report growth for this iteration
                const ajaxGrowth = MemoryProfiler.calculateGrowth(beforeAjax, afterAjax);
                const processingGrowth = MemoryProfiler.calculateGrowth(afterAjax, afterProcessing);
                const cleanupGrowth = MemoryProfiler.calculateGrowth(afterProcessing, afterCleanup);

                console.log(`📈 AJAX Growth (${iteration}):`, ajaxGrowth);
                console.log(`🔄 Processing Growth (${iteration}):`, processingGrowth);
                console.log(`🧹 Cleanup Result (${iteration}):`, cleanupGrowth);
            }

            // Force garbage collection if available
            if (window.gc) {
                console.log('🗑️ Forcing garbage collection...');
                window.gc();
                MemoryProfiler.snapshot(`iter${iteration}_after_gc`);
            }
        },

        analyzeResults: function() {
            console.log('\n🔬 COMPREHENSIVE MEMORY LEAK ANALYSIS');
            console.log('=====================================');

            if (memorySnapshots.length === 0) {
                console.log('❌ No memory snapshots collected');
                return;
            }

            // Group snapshots by iteration
            const byIteration = {};
            memorySnapshots.forEach(snapshot => {
                if (!byIteration[snapshot.iteration]) {
                    byIteration[snapshot.iteration] = [];
                }
                byIteration[snapshot.iteration].push(snapshot);
            });

            // Analyze growth patterns
            const growthAnalysis = {
                heap_growth: [],
                dom_growth: [],
                svg_growth: [],
                jquery_growth: []
            };

            Object.keys(byIteration).forEach(iteration => {
                const snapshots = byIteration[iteration];
                const initial = snapshots.find(s => s.label.includes('initial'));
                const final = snapshots[snapshots.length - 1];

                if (initial && final) {
                    const totalGrowth = MemoryProfiler.calculateGrowth(initial, final);

                    if (totalGrowth.heap) {
                        growthAnalysis.heap_growth.push(totalGrowth.heap.used_mb);
                    }
                    growthAnalysis.dom_growth.push(totalGrowth.dom.nodes);
                    growthAnalysis.svg_growth.push(totalGrowth.d3.svg_children);

                    console.log(`\n📊 ITERATION ${iteration} TOTAL GROWTH:`);
                    console.log('  Heap:', totalGrowth.heap ? `${totalGrowth.heap.used_mb}MB` : 'N/A');
                    console.log('  DOM Nodes:', totalGrowth.dom.nodes);
                    console.log('  SVG Children:', totalGrowth.d3.svg_children);
                    console.log('  jQuery Cache:', totalGrowth.jquery ? totalGrowth.jquery.cache_growth : 'N/A');
                }
            });

            // Identify consistent growth patterns (memory leaks)
            console.log('\n🎯 MEMORY LEAK DETECTION:');

            if (growthAnalysis.heap_growth.length > 1) {
                const avgHeapGrowth = growthAnalysis.heap_growth.reduce((a, b) => a + b, 0) / growthAnalysis.heap_growth.length;
                if (avgHeapGrowth > 5) {
                    console.log('❌ HEAP MEMORY LEAK DETECTED: Average ' + avgHeapGrowth.toFixed(2) + 'MB growth per iteration');
                } else {
                    console.log('✅ Heap memory growth appears normal: ' + avgHeapGrowth.toFixed(2) + 'MB average');
                }
            }

            const avgDomGrowth = growthAnalysis.dom_growth.reduce((a, b) => a + b, 0) / growthAnalysis.dom_growth.length;
            if (avgDomGrowth > 10) {
                console.log('❌ DOM MEMORY LEAK DETECTED: Average ' + avgDomGrowth + ' nodes growth per iteration');
            } else {
                console.log('✅ DOM growth appears normal: ' + avgDomGrowth + ' nodes average');
            }

            const avgSvgGrowth = growthAnalysis.svg_growth.reduce((a, b) => a + b, 0) / growthAnalysis.svg_growth.length;
            if (avgSvgGrowth > 50) {
                console.log('❌ SVG/D3 MEMORY LEAK DETECTED: Average ' + avgSvgGrowth + ' SVG elements growth per iteration');
            } else {
                console.log('✅ SVG/D3 growth appears normal: ' + avgSvgGrowth + ' elements average');
            }

            // Export detailed results
            window.browserMemoryLeakResults = {
                snapshots: memorySnapshots,
                growthAnalysis: growthAnalysis,
                summary: {
                    iterations: maxIterations,
                    avgHeapGrowth: growthAnalysis.heap_growth.length > 0 ?
                        growthAnalysis.heap_growth.reduce((a, b) => a + b, 0) / growthAnalysis.heap_growth.length : 0,
                    avgDomGrowth: avgDomGrowth,
                    avgSvgGrowth: avgSvgGrowth
                }
            };

            console.log('\n💾 Detailed results exported to window.browserMemoryLeakResults');
            console.log('\n🔧 RECOMMENDATIONS:');

            if (window.browserMemoryLeakResults.summary.avgHeapGrowth > 5) {
                console.log('1. 🚨 Investigate JavaScript object retention');
                console.log('2. 🔧 Add explicit memory cleanup in AJAX callbacks');
                console.log('3. 📊 Check for circular references in data structures');
            }

            if (window.browserMemoryLeakResults.summary.avgDomGrowth > 10) {
                console.log('1. 🚨 Investigate DOM node accumulation');
                console.log('2. 🔧 Add explicit DOM cleanup after operations');
                console.log('3. 📊 Check for orphaned event listeners');
            }

            if (window.browserMemoryLeakResults.summary.avgSvgGrowth > 50) {
                console.log('1. 🚨 Investigate D3.js object retention');
                console.log('2. 🔧 Add D3.js cleanup patterns');
                console.log('3. 📊 Check for accumulated SVG elements');
            }

            console.log('\n✅ Browser memory leak analysis complete!');
        }
    };

    // Check prerequisites
    if (!window.slmmInterlinkingData) {
        console.error('❌ Not on interlinking suite page! Go to: wp-admin/admin.php?page=slmm-interlinking-suite');
        return;
    }

    if (!performance.memory) {
        console.warn('⚠️ Browser memory API not available. Limited memory tracking.');
    }

    // Start the test
    MemoryLeakTester.runComprehensiveTest().catch(error => {
        console.error('❌ Memory leak test failed:', error);
    });

})();