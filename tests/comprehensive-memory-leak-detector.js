/**
 * COMPREHENSIVE JAVASCRIPT MEMORY LEAK DETECTOR
 * Run this in browser console to identify which components are causing memory growth
 */

(function() {
    'use strict';

    // Memory tracking object
    window.slmmMemoryLeakDetector = {
        initialMemory: null,
        componentChecks: [],
        results: {},

        /**
         * Start comprehensive memory leak detection
         */
        detect: function() {
            console.log('🔍 SLMM COMPREHENSIVE MEMORY LEAK DETECTION STARTING...');
            console.log('==========================================');

            // Record initial memory
            this.recordInitialMemory();

            // Check all global SLMM objects
            this.checkGlobalObjects();

            // Check event listeners
            this.checkEventListeners();

            // Check timeouts and intervals
            this.checkTimersAndIntervals();

            // Check DOM elements
            this.checkDOMElements();

            // Check for circular references
            this.checkCircularReferences();

            // Generate final report
            this.generateReport();
        },

        /**
         * Record initial memory state
         */
        recordInitialMemory: function() {
            if (performance.memory) {
                this.initialMemory = {
                    used: Math.round(performance.memory.usedJSHeapSize / 1048576),
                    total: Math.round(performance.memory.totalJSHeapSize / 1048576),
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576)
                };

                console.log('📊 Initial Memory State:');
                console.log('Used:', this.initialMemory.used + 'MB');
                console.log('Total:', this.initialMemory.total + 'MB');
                console.log('Limit:', this.initialMemory.limit + 'MB');
                console.log('');
            }
        },

        /**
         * Check all global SLMM objects for memory usage
         */
        checkGlobalObjects: function() {
            console.log('🌐 CHECKING GLOBAL OBJECTS...');

            const globalObjects = [];

            // Find all SLMM-related global objects
            for (let prop in window) {
                if (prop.toLowerCase().includes('slmm')) {
                    try {
                        const obj = window[prop];
                        let size = 'unknown';
                        let type = typeof obj;
                        let details = {};

                        if (obj && typeof obj === 'object') {
                            // Check for arrays
                            if (Array.isArray(obj)) {
                                size = obj.length + ' items';
                                type = 'Array';
                            }
                            // Check for objects with arrays
                            else if (obj.nodes && Array.isArray(obj.nodes)) {
                                size = obj.nodes.length + ' nodes';
                                type = 'Object with nodes array';
                                details.nodesSize = obj.nodes.length;
                            }
                            // Check for objects with timeouts
                            else if (obj.timeouts && Array.isArray(obj.timeouts)) {
                                details.timeouts = obj.timeouts.length;
                                details.isDestroyed = obj.isDestroyed || false;
                            }
                            // Check for objects with eventListeners
                            if (obj.eventListeners && Array.isArray(obj.eventListeners)) {
                                details.eventListeners = obj.eventListeners.length;
                            }
                            // Check for D3.js objects
                            if (obj._groups || (obj.empty && typeof obj.empty === 'function')) {
                                type = 'D3.js Selection';
                                size = obj._groups ? obj._groups.length + ' groups' : 'empty selection';
                            }
                        }

                        globalObjects.push({
                            name: prop,
                            type: type,
                            size: size,
                            details: details
                        });

                    } catch (e) {
                        globalObjects.push({
                            name: prop,
                            type: 'Error accessing',
                            size: 'N/A',
                            details: { error: e.message }
                        });
                    }
                }
            }

            // Sort by potential memory impact
            globalObjects.sort((a, b) => {
                // Prioritize arrays and objects with nodes
                const aScore = (a.details.nodesSize || 0) + (a.details.timeouts || 0) * 10;
                const bScore = (b.details.nodesSize || 0) + (b.details.timeouts || 0) * 10;
                return bScore - aScore;
            });

            globalObjects.forEach(obj => {
                const warning = (obj.details.nodesSize > 100 || obj.details.timeouts > 5) ? ' ⚠️ POTENTIAL LEAK' : '';
                console.log(`- ${obj.name}: ${obj.type} (${obj.size})${warning}`);
                if (Object.keys(obj.details).length > 0) {
                    console.log('  Details:', obj.details);
                }
            });

            this.results.globalObjects = globalObjects;
            console.log('');
        },

        /**
         * Check for accumulated event listeners
         */
        checkEventListeners: function() {
            console.log('👂 CHECKING EVENT LISTENERS...');

            // Check document event listeners (approximate)
            const documentEvents = {};
            const elements = [document, window];

            // Common SLMM events to check
            const slmmEvents = [
                'slmmQuickBulkPagesCreated',
                'slmmPageCreated',
                'slmm:direct_editor:ready',
                'tinymce:editor-ready',
                'click.slmm-segmentation',
                'beforeunload.slmm-segmentation',
                'beforeunload.slmm-quickbulk'
            ];

            console.log('Checking for common SLMM event listeners...');

            // Check if elements have our event listeners
            elements.forEach(element => {
                const elementName = element === document ? 'document' : 'window';
                slmmEvents.forEach(eventType => {
                    // We can't directly count listeners, but we can check if they exist
                    console.log(`- ${elementName}.${eventType}: Cannot directly count (browser security)`);
                });
            });

            // Check global components for event listener tracking
            const componentsWithListeners = [];

            [
                'SLMMContentSegmentation',
                'slmmQuickBulkTreeIntegration',
                'SLMMDirectEditor',
                'slmmInterlinkingController'
            ].forEach(compName => {
                if (window[compName]) {
                    const comp = window[compName];
                    if (comp.eventListeners && Array.isArray(comp.eventListeners)) {
                        componentsWithListeners.push({
                            name: compName,
                            listenerCount: comp.eventListeners.length,
                            isDestroyed: comp.isDestroyed || false
                        });
                    }
                }
            });

            componentsWithListeners.forEach(comp => {
                const warning = comp.listenerCount > 5 ? ' ⚠️ HIGH COUNT' : '';
                const destroyed = comp.isDestroyed ? ' (destroyed)' : ' (active)';
                console.log(`- ${comp.name}: ${comp.listenerCount} tracked listeners${destroyed}${warning}`);
            });

            this.results.eventListeners = componentsWithListeners;
            console.log('');
        },

        /**
         * Check for accumulated timeouts and intervals
         */
        checkTimersAndIntervals: function() {
            console.log('⏰ CHECKING TIMEOUTS AND INTERVALS...');

            const componentTimers = [];

            [
                'SLMMContentSegmentation',
                'slmmQuickBulkTreeIntegration'
            ].forEach(compName => {
                if (window[compName]) {
                    const comp = window[compName];
                    if (comp.timeouts && Array.isArray(comp.timeouts)) {
                        componentTimers.push({
                            name: compName,
                            timeoutCount: comp.timeouts.length,
                            isDestroyed: comp.isDestroyed || false
                        });
                    }
                }
            });

            componentTimers.forEach(comp => {
                const warning = comp.timeoutCount > 3 ? ' ⚠️ HIGH COUNT' : '';
                const destroyed = comp.isDestroyed ? ' (destroyed)' : ' (active)';
                console.log(`- ${comp.name}: ${comp.timeoutCount} active timeouts${destroyed}${warning}`);
            });

            // Check for monitoring intervals
            console.log('- Memory monitoring intervals: Active (30-second intervals for multiple components)');

            this.results.timers = componentTimers;
            console.log('');
        },

        /**
         * Check DOM elements for potential leaks
         */
        checkDOMElements: function() {
            console.log('🏠 CHECKING DOM ELEMENTS...');

            const domChecks = {
                tinyMCEEditors: document.querySelectorAll('.wp-editor-area').length,
                slmmButtons: document.querySelectorAll('[class*="slmm"]').length,
                d3Selections: document.querySelectorAll('.node, .tree-node').length,
                overlayElements: document.querySelectorAll('.slmm-overlay, .content-overlay').length,
                modalElements: document.querySelectorAll('.slmm-modal, .direct-editor-modal').length
            };

            Object.entries(domChecks).forEach(([key, count]) => {
                const warning = count > 50 ? ' ⚠️ HIGH COUNT' : '';
                console.log(`- ${key}: ${count}${warning}`);
            });

            this.results.domElements = domChecks;
            console.log('');
        },

        /**
         * Check for circular references
         */
        checkCircularReferences: function() {
            console.log('🔄 CHECKING FOR CIRCULAR REFERENCES...');

            const circularChecks = [];

            // Check common SLMM objects for circular references
            ['SLMMContentSegmentation', 'slmmQuickBulkTreeIntegration', 'SLMMDirectEditor'].forEach(objName => {
                if (window[objName]) {
                    try {
                        JSON.stringify(window[objName]);
                        circularChecks.push({ name: objName, circular: false });
                    } catch (e) {
                        if (e.message.includes('circular')) {
                            circularChecks.push({ name: objName, circular: true });
                        }
                    }
                }
            });

            circularChecks.forEach(check => {
                const warning = check.circular ? ' ⚠️ CIRCULAR REFERENCE' : ' ✅ Clean';
                console.log(`- ${check.name}:${warning}`);
            });

            this.results.circularReferences = circularChecks;
            console.log('');
        },

        /**
         * Generate final comprehensive report
         */
        generateReport: function() {
            console.log('📋 COMPREHENSIVE MEMORY LEAK ANALYSIS');
            console.log('==========================================');

            // Current memory state
            if (performance.memory) {
                const currentMemory = {
                    used: Math.round(performance.memory.usedJSHeapSize / 1048576),
                    total: Math.round(performance.memory.totalJSHeapSize / 1048576)
                };

                console.log('📊 CURRENT MEMORY STATE:');
                console.log(`Used: ${currentMemory.used}MB (was ${this.initialMemory?.used || 'unknown'}MB)`);
                console.log(`Total: ${currentMemory.total}MB (was ${this.initialMemory?.total || 'unknown'}MB)`);
                console.log('');
            }

            // Top suspects for memory leaks
            console.log('🚨 TOP MEMORY LEAK SUSPECTS:');

            const suspects = [];

            // Check global objects
            if (this.results.globalObjects) {
                this.results.globalObjects.forEach(obj => {
                    if (obj.details.nodesSize > 100) {
                        suspects.push(`${obj.name}: ${obj.details.nodesSize} nodes (Global array accumulation)`);
                    }
                    if (obj.details.timeouts > 5) {
                        suspects.push(`${obj.name}: ${obj.details.timeouts} active timeouts (Timer leak)`);
                    }
                    if (obj.details.eventListeners > 5) {
                        suspects.push(`${obj.name}: ${obj.details.eventListeners} event listeners (Listener leak)`);
                    }
                });
            }

            // Check timers
            if (this.results.timers) {
                this.results.timers.forEach(timer => {
                    if (timer.timeoutCount > 3 && !timer.isDestroyed) {
                        suspects.push(`${timer.name}: ${timer.timeoutCount} active timeouts (Component not destroyed)`);
                    }
                });
            }

            // Check DOM elements
            if (this.results.domElements) {
                Object.entries(this.results.domElements).forEach(([key, count]) => {
                    if (count > 50) {
                        suspects.push(`DOM: ${count} ${key} elements (DOM accumulation)`);
                    }
                });
            }

            // Check circular references
            if (this.results.circularReferences) {
                this.results.circularReferences.forEach(check => {
                    if (check.circular) {
                        suspects.push(`${check.name}: Circular reference detected (GC prevention)`);
                    }
                });
            }

            if (suspects.length === 0) {
                console.log('✅ No obvious memory leak suspects found in SLMM components');
                console.log('The leak might be in:');
                console.log('- WordPress core JavaScript');
                console.log('- Other plugins');
                console.log('- Browser extensions');
                console.log('- Unchecked SLMM components');
            } else {
                suspects.forEach((suspect, index) => {
                    console.log(`${index + 1}. ${suspect}`);
                });
            }

            console.log('');
            console.log('💡 NEXT STEPS:');
            console.log('1. Refresh the page and run this script again');
            console.log('2. Compare results to identify growing components');
            console.log('3. Focus on components with increasing numbers');
            console.log('4. Check console for any error messages');

            // Store results globally for further analysis
            window.slmmMemoryLeakResults = this.results;

            return this.results;
        }
    };

    // Auto-start detection
    console.log('Starting comprehensive memory leak detection...');
    setTimeout(() => {
        window.slmmMemoryLeakDetector.detect();
    }, 1000);

})();

// USAGE: Copy and paste this entire script into your browser console
// Results will be stored in window.slmmMemoryLeakResults for further analysis