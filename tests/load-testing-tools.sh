#!/bin/bash
# File: tests/load-testing-tools.sh
#
# Server Load Testing Tools for SLMM Interlinking Suite
# Provides command-line tools for testing memory leaks and server performance

# Set script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PLUGIN_DIR="$(dirname "$SCRIPT_DIR")"

# Default WordPress URL (update for your environment)
WP_URL="${WP_URL:-http://localhost:8884}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to test PHP memory usage
test_php_memory() {
    local iterations=${1:-10}
    local size=${2:-1000}

    print_status "Testing PHP memory usage - Iterations: $iterations, Size: $size"

    # Create a temporary PHP script for testing
    cat > /tmp/slmm_memory_test.php << 'EOF'
<?php
// Memory usage test for SLMM Interlinking Suite
$start_memory = memory_get_usage(true);
$iterations = intval($argv[1] ?? 10);
$size = intval($argv[2] ?? 1000);

echo "Starting memory test - Iterations: $iterations, Size: $size\n";
echo "Initial memory: " . round($start_memory / 1024 / 1024, 2) . " MB\n";

$snapshots = array();

for ($i = 1; $i <= $iterations; $i++) {
    $before = memory_get_usage(true);

    // Simulate large data processing
    $data = array();
    for ($j = 0; $j < $size; $j++) {
        $data[] = array(
            'id' => $j,
            'title' => str_repeat('Test Page ' . $j, 5),
            'content' => str_repeat('Lorem ipsum content ', 50),
            'metadata' => array(
                'seo_score' => rand(1, 100),
                'keywords' => array_fill(0, 10, 'keyword' . $j),
                'links' => array_fill(0, 15, 'http://example.com/' . $j)
            )
        );
    }

    // Simulate processing
    foreach ($data as &$item) {
        $item['processed'] = true;
        $item['score'] = $item['metadata']['seo_score'] * 1.5;
    }

    $after = memory_get_usage(true);
    $growth = $after - $before;

    $snapshots[] = array(
        'iteration' => $i,
        'before' => $before,
        'after' => $after,
        'growth' => $growth,
        'growth_mb' => round($growth / 1024 / 1024, 2),
        'peak' => memory_get_peak_usage(true)
    );

    echo "Iteration $i: " . round($growth / 1024 / 1024, 2) . " MB growth\n";

    // Cleanup
    unset($data);

    // Force garbage collection
    if (function_exists('gc_collect_cycles')) {
        gc_collect_cycles();
    }
}

// Analyze results
$total_growth = end($snapshots)['after'] - $snapshots[0]['before'];
$avg_growth = $total_growth / $iterations;

echo "\n=== ANALYSIS ===\n";
echo "Total memory growth: " . round($total_growth / 1024 / 1024, 2) . " MB\n";
echo "Average per iteration: " . round($avg_growth / 1024 / 1024, 2) . " MB\n";
echo "Peak memory usage: " . round(memory_get_peak_usage(true) / 1024 / 1024, 2) . " MB\n";

if ($total_growth > (10 * 1024 * 1024)) {
    echo "WARNING: Potential memory leak detected!\n";
    exit(1);
} else {
    echo "Memory usage appears stable.\n";
    exit(0);
}
EOF

    # Run the PHP memory test
    php /tmp/slmm_memory_test.php "$iterations" "$size"
    local exit_code=$?

    # Cleanup
    rm -f /tmp/slmm_memory_test.php

    if [ $exit_code -eq 0 ]; then
        print_success "Memory test completed - No leaks detected"
    else
        print_error "Memory test failed - Potential memory leak"
    fi

    return $exit_code
}

# Function to test WordPress AJAX endpoints
test_wp_ajax_load() {
    local concurrent=${1:-5}
    local requests=${2:-20}
    local endpoint=${3:-"slmm_generate_silo_grid"}

    print_status "Testing WordPress AJAX load - Concurrent: $concurrent, Requests: $requests"

    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        print_error "curl is required for AJAX testing"
        return 1
    fi

    # Create temporary file for results
    local results_file="/tmp/slmm_ajax_results.txt"
    rm -f "$results_file"

    # Function to make AJAX request
    make_ajax_request() {
        local start_time=$(date +%s%N)
        local response=$(curl -s -w "%{http_code}|%{time_total}" \
            -X POST "$WP_URL/wp-admin/admin-ajax.php" \
            -d "action=$endpoint" \
            -d "post_type_filter=page" \
            -d "nonce=test_nonce" \
            2>/dev/null)
        local end_time=$(date +%s%N)

        local http_code=$(echo "$response" | tail -c 10 | cut -d'|' -f1)
        local time_total=$(echo "$response" | tail -c 10 | cut -d'|' -f2)
        local duration=$(((end_time - start_time) / 1000000)) # Convert to ms

        echo "$http_code,$time_total,$duration" >> "$results_file"
    }

    # Export function for parallel execution
    export -f make_ajax_request
    export WP_URL endpoint results_file

    print_status "Making $requests requests with $concurrent concurrent connections..."

    # Use GNU parallel if available, otherwise use xargs
    if command -v parallel &> /dev/null; then
        seq 1 "$requests" | parallel -j "$concurrent" make_ajax_request
    else
        seq 1 "$requests" | xargs -n 1 -P "$concurrent" -I {} bash -c 'make_ajax_request'
    fi

    # Analyze results
    if [ -f "$results_file" ]; then
        local total_requests=$(wc -l < "$results_file")
        local successful_requests=$(grep -c "^200," "$results_file" || echo "0")
        local avg_time=$(awk -F',' '{sum+=$2; count++} END {if(count>0) print sum/count; else print 0}' "$results_file")

        print_success "AJAX Load Test Results:"
        echo "  Total requests: $total_requests"
        echo "  Successful: $successful_requests"
        echo "  Success rate: $(echo "scale=2; $successful_requests * 100 / $total_requests" | bc)%"
        echo "  Average response time: ${avg_time}s"

        # Clean up
        rm -f "$results_file"

        if [ "$successful_requests" -lt "$((requests * 80 / 100))" ]; then
            print_warning "Low success rate detected - check server capacity"
            return 1
        fi
    else
        print_error "No results file found - test may have failed"
        return 1
    fi
}

# Function to test Apache Bench (if available)
test_apache_bench() {
    local requests=${1:-100}
    local concurrency=${2:-10}
    local url="${3:-$WP_URL/wp-admin/admin.php?page=slmm-interlinking-suite}"

    print_status "Running Apache Bench test - Requests: $requests, Concurrency: $concurrency"

    if ! command -v ab &> /dev/null; then
        print_warning "Apache Bench (ab) not available - skipping"
        return 0
    fi

    print_status "Testing URL: $url"

    # Run Apache Bench
    local ab_output=$(ab -n "$requests" -c "$concurrency" -k "$url" 2>&1)
    local exit_code=$?

    if [ $exit_code -eq 0 ]; then
        print_success "Apache Bench test completed"

        # Extract key metrics
        echo "$ab_output" | grep -E "(Requests per second|Time per request|Transfer rate)"

        # Check for errors
        local failed_requests=$(echo "$ab_output" | grep "Failed requests" | awk '{print $3}')
        if [ "${failed_requests:-0}" -gt "$((requests * 10 / 100))" ]; then
            print_warning "High failure rate detected: $failed_requests failed requests"
        fi
    else
        print_error "Apache Bench test failed"
        echo "$ab_output"
    fi

    return $exit_code
}

# Function to monitor system resources
monitor_system_resources() {
    local duration=${1:-30}
    local interval=${2:-2}

    print_status "Monitoring system resources for ${duration}s (interval: ${interval}s)"

    local log_file="/tmp/slmm_system_monitor.log"
    echo "timestamp,cpu_load,memory_usage,disk_io" > "$log_file"

    local end_time=$(($(date +%s) + duration))

    while [ $(date +%s) -lt $end_time ]; do
        local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        local cpu_load=""
        local memory_usage=""
        local disk_io=""

        # Get CPU load average (1-minute)
        if command -v uptime &> /dev/null; then
            cpu_load=$(uptime | awk -F'load average:' '{print $2}' | awk -F',' '{print $1}' | xargs)
        fi

        # Get memory usage percentage
        if command -v free &> /dev/null; then
            memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
        elif command -v vm_stat &> /dev/null; then
            # macOS
            memory_usage=$(vm_stat | head -5 | tail -4 | awk '{print $3}' | tr -d ':' | awk '{sum+=$1} END {print sum}')
        fi

        # Get disk I/O (simplified)
        if command -v iostat &> /dev/null; then
            disk_io=$(iostat -x 1 1 | tail -1 | awk '{print $4+$5}' 2>/dev/null || echo "0")
        fi

        echo "$timestamp,$cpu_load,$memory_usage,$disk_io" >> "$log_file"
        sleep "$interval"
    done

    print_success "System monitoring completed - Log saved to: $log_file"

    # Show summary
    echo "Resource Summary:"
    echo "  Max CPU Load: $(awk -F',' 'NR>1 && $2!="" {print $2}' "$log_file" | sort -n | tail -1)"
    echo "  Max Memory: $(awk -F',' 'NR>1 && $3!="" {print $3}' "$log_file" | sort -n | tail -1)%"
    echo "  Log file: $log_file"
}

# Function to run comprehensive test suite
run_full_test_suite() {
    print_status "Running comprehensive test suite for SLMM Interlinking Suite"

    local overall_result=0

    echo "==================="
    echo "1. PHP Memory Test"
    echo "==================="
    test_php_memory 10 1000
    [ $? -ne 0 ] && overall_result=1

    echo -e "\n==================="
    echo "2. AJAX Load Test"
    echo "==================="
    test_wp_ajax_load 5 20
    [ $? -ne 0 ] && overall_result=1

    echo -e "\n==================="
    echo "3. Apache Bench Test"
    echo "==================="
    test_apache_bench 50 5
    [ $? -ne 0 ] && overall_result=1

    echo -e "\n==================="
    echo "4. System Monitoring"
    echo "==================="
    monitor_system_resources 30 2

    echo -e "\n==================="
    echo "Test Suite Complete"
    echo "==================="

    if [ $overall_result -eq 0 ]; then
        print_success "All tests passed!"
    else
        print_error "Some tests failed - check logs for details"
    fi

    return $overall_result
}

# Command line interface
case "${1:-help}" in
    "memory")
        test_php_memory "${2:-10}" "${3:-1000}"
        ;;
    "ajax")
        test_wp_ajax_load "${2:-5}" "${3:-20}" "${4:-slmm_generate_silo_grid}"
        ;;
    "apache")
        test_apache_bench "${2:-100}" "${3:-10}" "$4"
        ;;
    "monitor")
        monitor_system_resources "${2:-30}" "${3:-2}"
        ;;
    "full")
        run_full_test_suite
        ;;
    "help"|*)
        echo "SLMM Interlinking Suite Load Testing Tools"
        echo ""
        echo "Usage: $0 [command] [options]"
        echo ""
        echo "Commands:"
        echo "  memory [iterations] [size]     - Test PHP memory usage"
        echo "  ajax [concurrent] [requests]   - Test AJAX endpoint load"
        echo "  apache [requests] [concurrency] - Run Apache Bench test"
        echo "  monitor [duration] [interval]  - Monitor system resources"
        echo "  full                          - Run complete test suite"
        echo "  help                          - Show this help"
        echo ""
        echo "Examples:"
        echo "  $0 memory 20 2000            - Test with 20 iterations, 2000 items each"
        echo "  $0 ajax 10 50                - Test with 10 concurrent, 50 requests"
        echo "  $0 apache 200 20             - Apache Bench: 200 requests, 20 concurrent"
        echo "  $0 monitor 60 5              - Monitor for 60s, check every 5s"
        echo ""
        echo "Environment Variables:"
        echo "  WP_URL                        - WordPress URL (default: http://localhost:8884)"
        ;;
esac