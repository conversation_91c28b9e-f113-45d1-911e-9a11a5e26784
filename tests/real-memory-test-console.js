// REAL MEMORY TESTING - Browser Console <PERSON>
// Paste this into browser console while on the interlinking suite page
// This tests ACTUAL WordPress data processing, not simulations!

(function() {
    console.log('🔍 Starting REAL Interlinking Suite Memory Test...');
    console.log('📊 This will test ACTUAL WordPress data processing');

    // Check if we're on the interlinking suite page
    if (!window.slmmInterlinkingData) {
        console.error('❌ Not on interlinking suite page! Go to: wp-admin/admin.php?page=slmm-interlinking-suite');
        return;
    }

    let testResults = [];
    let iteration = 0;
    const maxIterations = 5; // Fewer iterations for real data
    const postTypes = ['page', 'post']; // Test different post types

    console.log(`🚀 Testing ${maxIterations} iterations across post types: ${postTypes.join(', ')}`);

    // Function to run single memory test iteration
    function runMemoryTest(postType, iterationNum) {
        return new Promise((resolve, reject) => {
            console.log(`📈 Testing ${postType} - Iteration ${iterationNum}/${maxIterations}`);

            const startTime = performance.now();

            // Make ACTUAL AJAX call to interlinking suite with memory tracking enabled
            jQuery.post(slmmInterlinkingData.ajax_url, {
                action: 'slmm_generate_silo_grid',
                nonce: slmmInterlinkingData.nonce,
                post_type_filter: postType,
                enable_memory_tracking: 'true', // ENABLE REAL MEMORY TRACKING
                fresh_analysis: 'true', // Force fresh analysis
                include_links: 'true',
                tree_mode: true,
                max_depth: 5
            })
            .done(function(response) {
                const endTime = performance.now();
                const requestTime = endTime - startTime;

                if (response.success && response.data.memory_tracking) {
                    const memData = response.data.memory_tracking;

                    console.log(`✅ ${postType} (${iterationNum}) - ${memData.total_growth_mb} MB, ${memData.total_time_ms} ms`);

                    testResults.push({
                        iteration: iterationNum,
                        post_type: postType,
                        total_growth_mb: memData.total_growth_mb,
                        peak_memory_mb: memData.peak_memory_mb,
                        processing_time_ms: memData.total_time_ms,
                        request_time_ms: requestTime,
                        pages_processed: memData.analysis.total_pages,
                        efficiency_kb_per_page: memData.analysis.efficiency_kb_per_page,
                        memory_leak_detected: memData.analysis.memory_leak_detected,
                        elevated_usage: memData.analysis.elevated_usage,
                        stages: memData.stages,
                        raw_data: response.data
                    });

                    // Show stage breakdown
                    console.log(`📋 Stage Breakdown for ${postType}:`);
                    memData.stages.forEach(stage => {
                        console.log(`   ${stage.stage}: ${stage.growth_mb}MB (${stage.duration_ms}ms)`);
                    });

                } else {
                    console.warn(`⚠️ No memory tracking data for ${postType} iteration ${iterationNum}`);
                    testResults.push({
                        iteration: iterationNum,
                        post_type: postType,
                        error: 'No memory tracking data received',
                        raw_response: response
                    });
                }

                resolve();
            })
            .fail(function(xhr, status, error) {
                console.error(`❌ AJAX failed for ${postType} iteration ${iterationNum}:`, error);
                testResults.push({
                    iteration: iterationNum,
                    post_type: postType,
                    error: `AJAX Error: ${error}`,
                    status: status
                });
                resolve(); // Continue even on error
            });
        });
    }

    // Function to run all test iterations
    async function runAllTests() {
        console.log('🔄 Starting comprehensive memory testing...');

        for (let i = 1; i <= maxIterations; i++) {
            for (const postType of postTypes) {
                await runMemoryTest(postType, i);

                // Small delay between tests to prevent server overload
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        analyzeResults();
    }

    // Function to analyze all test results
    function analyzeResults() {
        console.log('\n🔬 COMPREHENSIVE ANALYSIS');
        console.log('========================');

        if (testResults.length === 0) {
            console.log('❌ No test results to analyze');
            return;
        }

        // Filter successful results
        const validResults = testResults.filter(r => !r.error && r.total_growth_mb !== undefined);

        if (validResults.length === 0) {
            console.log('❌ No valid memory data received');
            console.log('🔧 Check if memory tracking is working in backend');
            return;
        }

        // Calculate statistics
        const memoryGrowths = validResults.map(r => r.total_growth_mb);
        const processingTimes = validResults.map(r => r.processing_time_ms);
        const pagesProcessed = validResults.map(r => r.pages_processed);

        const avgMemoryGrowth = memoryGrowths.reduce((a, b) => a + b, 0) / memoryGrowths.length;
        const maxMemoryGrowth = Math.max(...memoryGrowths);
        const avgProcessingTime = processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length;
        const totalPagesProcessed = pagesProcessed.reduce((a, b) => a + b, 0);

        // Analysis by post type
        const byPostType = {};
        validResults.forEach(result => {
            if (!byPostType[result.post_type]) {
                byPostType[result.post_type] = [];
            }
            byPostType[result.post_type].push(result);
        });

        console.log('📊 OVERALL STATISTICS:');
        console.log(`   Average Memory Growth: ${avgMemoryGrowth.toFixed(2)} MB`);
        console.log(`   Maximum Memory Growth: ${maxMemoryGrowth.toFixed(2)} MB`);
        console.log(`   Average Processing Time: ${avgProcessingTime.toFixed(2)} ms`);
        console.log(`   Total Pages Processed: ${totalPagesProcessed}`);

        // Post type breakdown
        console.log('\n📋 BY POST TYPE:');
        Object.keys(byPostType).forEach(postType => {
            const typeResults = byPostType[postType];
            const typeAvgMemory = typeResults.reduce((sum, r) => sum + r.total_growth_mb, 0) / typeResults.length;
            const typeAvgPages = typeResults.reduce((sum, r) => sum + r.pages_processed, 0) / typeResults.length;

            console.log(`   ${postType.toUpperCase()}:`);
            console.log(`     Average Memory: ${typeAvgMemory.toFixed(2)} MB`);
            console.log(`     Average Pages: ${typeAvgPages.toFixed(0)} pages`);
            console.log(`     Efficiency: ${(typeAvgMemory / typeAvgPages * 1024).toFixed(2)} KB/page`);
        });

        // Memory leak detection
        const leakDetected = validResults.some(r => r.memory_leak_detected);
        const elevatedUsage = validResults.some(r => r.elevated_usage);

        console.log('\n🎯 ANALYSIS RESULTS:');
        if (leakDetected) {
            console.log('❌ MEMORY LEAK DETECTED in one or more tests');
            console.log('🔧 Investigate backend processing for unreleased references');
        } else if (elevatedUsage) {
            console.log('⚠️ ELEVATED MEMORY USAGE detected');
            console.log('🔧 Consider optimization strategies for large sites');
        } else {
            console.log('✅ MEMORY USAGE APPEARS NORMAL');
            console.log('👍 Backend processing is operating efficiently');
        }

        // Performance assessment
        if (avgProcessingTime > 5000) {
            console.log('⏱️ SLOW PROCESSING detected (>5s average)');
            console.log('🔧 Consider implementing chunk processing for large sites');
        } else if (avgProcessingTime > 2000) {
            console.log('⏱️ Moderate processing time (2-5s average)');
            console.log('💡 Monitor performance with larger datasets');
        } else {
            console.log('⚡ FAST PROCESSING (<2s average) - Performance looks good!');
        }

        // Show detailed results table
        console.log('\n📋 DETAILED RESULTS:');
        console.table(validResults.map(r => ({
            'Iteration': r.iteration,
            'Post Type': r.post_type,
            'Memory (MB)': r.total_growth_mb,
            'Time (ms)': r.processing_time_ms.toFixed(0),
            'Pages': r.pages_processed,
            'KB/Page': r.efficiency_kb_per_page
        })));

        // Export results for further analysis
        window.slmmRealTestResults = testResults;
        window.slmmMemoryAnalysis = {
            avgMemoryGrowth,
            maxMemoryGrowth,
            avgProcessingTime,
            totalPagesProcessed,
            byPostType,
            leakDetected,
            elevatedUsage,
            validResults
        };

        console.log('\n💾 Results exported to:');
        console.log('   window.slmmRealTestResults (raw data)');
        console.log('   window.slmmMemoryAnalysis (analysis)');

        // Recommendations
        console.log('\n🎯 RECOMMENDATIONS:');
        if (maxMemoryGrowth > 50) {
            console.log('1. 🚨 CRITICAL: Investigate memory leaks in backend processing');
            console.log('2. 🔧 Review array cleanup in hierarchy and link analysis');
            console.log('3. 📊 Add more garbage collection calls in processing loops');
        } else if (avgMemoryGrowth > 20) {
            console.log('1. ⚠️ Consider implementing chunk processing for large sites');
            console.log('2. 🔧 Optimize data structures to reduce memory footprint');
            console.log('3. 📊 Monitor memory usage in production environment');
        } else {
            console.log('1. ✅ Current memory usage is acceptable');
            console.log('2. 📊 Continue monitoring with larger datasets');
            console.log('3. 🚀 Consider optimizing for even better performance');
        }
    }

    // Start the test suite
    runAllTests().catch(error => {
        console.error('❌ Test suite failed:', error);
    });

})();